# File Upload Size Limit Fix - Templates Cave

## Problem
You're encountering the error: "PHP Request Startup: POST Content-Length of 18023285 bytes exceeds the limit of 8388608 bytes" when trying to upload large files.

This error occurs because P<PERSON>'s default upload limits are too small for the file sizes that Templates Cave supports (up to 100MB for product files).

## Solution Overview
The issue is resolved by updating PHP configuration settings to allow larger file uploads. Multiple methods are provided below.

## Method 1: MAMP Configuration (Recommended for MAMP users)

### Step 1: Locate MAMP PHP Configuration
1. Open MAMP application
2. Go to MAMP → Preferences → PHP
3. Note your PHP version (e.g., 8.2.0)
4. Navigate to: `/Applications/MAMP/bin/php/php[YOUR_VERSION]/conf/`
   - Example: `/Applications/MAMP/bin/php/php8.2.0/conf/`

### Step 2: Edit php.ini
1. Find the `php.ini` file in the conf directory
2. Open it with a text editor
3. Find and update these settings (or add them if they don't exist):

```ini
; File Upload Settings
upload_max_filesize = 100M
post_max_size = 100M
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
max_file_uploads = 20
```

### Step 3: Restart MAMP
1. Stop MAMP servers
2. Start MAMP servers again
3. Test file upload functionality

## Method 2: Using the Provided Configuration File

### Step 1: Copy Configuration
1. Copy the `php-upload-config.ini` file from your project root
2. Place it in your MAMP PHP configuration directory:
   `/Applications/MAMP/bin/php/php[YOUR_VERSION]/conf/`

### Step 2: Include in php.ini
1. Open the main `php.ini` file
2. Add this line at the end:
   ```ini
   ; Include Templates Cave upload configuration
   include_path = "/Applications/MAMP/bin/php/php8.2.0/conf/php-upload-config.ini"
   ```

### Step 3: Restart MAMP
Restart MAMP servers to apply changes.

## Method 3: MAMP Pro Interface (If you have MAMP Pro)

1. Open MAMP Pro
2. Go to PHP tab
3. Select your PHP version
4. Click "php.ini" tab
5. Update the following settings:
   - `upload_max_filesize = 100M`
   - `post_max_size = 100M`
   - `memory_limit = 256M`
   - `max_execution_time = 300`
   - `max_input_time = 300`
6. Save and restart servers

## Method 4: .htaccess Configuration (Already Applied)

The `.htaccess` file in the `public` directory has been updated with PHP configuration directives. This method works if your server supports PHP configuration via .htaccess.

## Verification

### Check if Settings Applied
1. Create a PHP file with `<?php phpinfo(); ?>` in your public directory
2. Access it via browser (e.g., `http://127.0.0.1:8000/phpinfo.php`)
3. Search for the following settings and verify they show the new values:
   - `upload_max_filesize`
   - `post_max_size`
   - `memory_limit`
   - `max_execution_time`

### Test Upload Functionality
1. Go to Admin Panel → Products → Create Product
2. Try uploading a file larger than 8MB but smaller than 100MB
3. The upload should now work without errors

## File Size Limits in Templates Cave

- **Product Files**: Up to 100MB (ZIP, RAR, 7Z, TAR, GZ)
- **Product Images**: Up to 5MB each (JPEG, PNG, JPG, GIF, WebP)
- **Blog Images**: Up to 2MB each (JPEG, PNG, JPG, GIF)
- **Maximum Images per Product**: 10 images

## Troubleshooting

### If Upload Still Fails:

1. **Check MAMP Error Logs**:
   - Location: `/Applications/MAMP/logs/php_error.log`
   - Look for specific error messages

2. **Verify PHP Version**:
   - Ensure you're editing the correct PHP version's configuration
   - MAMP may have multiple PHP versions installed

3. **Check File Permissions**:
   - Ensure MAMP has write permissions to upload directories
   - Check `storage/app/public` permissions

4. **Disk Space**:
   - Ensure sufficient disk space is available
   - Large uploads require temporary storage space

5. **Browser Timeout**:
   - Large uploads may take time
   - Don't refresh the page during upload

### Alternative Solutions:

1. **Use Smaller Files**: 
   - Compress files before uploading
   - Split large archives into smaller parts

2. **External File Hosting**:
   - Use cloud storage (AWS S3, Google Drive, etc.)
   - Provide download links instead of direct uploads

3. **FTP Upload**:
   - Upload large files via FTP to `storage/app/public/products/files/`
   - Add file references manually in the admin panel

## Security Considerations

The updated configuration includes security enhancements:
- Hidden PHP version in headers
- Secure session settings
- Protected sensitive files via .htaccess
- Reasonable execution time limits

## Support

If you continue to experience issues:
1. Check the MAMP documentation for your specific version
2. Verify your MAMP installation is up to date
3. Consider using a different local development environment (Laravel Valet, Docker, etc.)

Remember to restart MAMP servers after any configuration changes!
