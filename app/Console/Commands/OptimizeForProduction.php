<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;

class OptimizeForProduction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:optimize-production {--force : Force optimization even in non-production environment}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize the application for production deployment';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!app()->environment('production') && !$this->option('force')) {
            $this->error('This command should only be run in production environment.');
            $this->info('Use --force flag to run in other environments.');
            return 1;
        }

        $this->info('🚀 Optimizing Templates Cave for production...');
        $this->newLine();

        // Clear all caches first
        $this->info('🧹 Clearing all caches...');
        Artisan::call('cache:clear');
        Artisan::call('config:clear');
        Artisan::call('route:clear');
        Artisan::call('view:clear');
        $this->line('   ✅ Caches cleared');

        // Optimize configuration
        $this->info('⚙️  Caching configuration...');
        Artisan::call('config:cache');
        $this->line('   ✅ Configuration cached');

        // Optimize routes
        $this->info('🛣️  Caching routes...');
        Artisan::call('route:cache');
        $this->line('   ✅ Routes cached');

        // Optimize views
        $this->info('👁️  Caching views...');
        Artisan::call('view:cache');
        $this->line('   ✅ Views cached');

        // Optimize events
        $this->info('⚡ Caching events...');
        Artisan::call('event:cache');
        $this->line('   ✅ Events cached');

        // Generate sitemap
        $this->info('🗺️  Generating sitemap...');
        try {
            $seoService = app(\App\Services\SEOService::class);
            $sitemap = $seoService->generateSitemap();
            file_put_contents(public_path('sitemap.xml'), $sitemap);
            $this->line('   ✅ Sitemap generated');
        } catch (\Exception $e) {
            $this->line('   ⚠️  Sitemap generation skipped: ' . $e->getMessage());
        }

        // Generate robots.txt
        $this->info('🤖 Generating robots.txt...');
        try {
            $seoService = app(\App\Services\SEOService::class);
            $robots = $seoService->generateRobotsTxt();
            file_put_contents(public_path('robots.txt'), $robots);
            $this->line('   ✅ Robots.txt generated');
        } catch (\Exception $e) {
            $this->line('   ⚠️  Robots.txt generation skipped: ' . $e->getMessage());
        }

        // Cache popular content
        $this->info('📦 Caching popular content...');
        try {
            $performanceService = app(\App\Services\PerformanceService::class);
            $performanceService->cachePopularContent();
            $this->line('   ✅ Popular content cached');
        } catch (\Exception $e) {
            $this->line('   ⚠️  Content caching skipped: ' . $e->getMessage());
        }

        // Build frontend assets (if not already built)
        if (file_exists(base_path('package.json'))) {
            $this->info('🎨 Building frontend assets...');
            exec('npm run build 2>&1', $output, $returnCode);
            if ($returnCode === 0) {
                $this->line('   ✅ Frontend assets built');
            } else {
                $this->line('   ⚠️  Frontend build skipped (run manually if needed)');
            }
        }

        $this->newLine();
        $this->info('✅ Production optimization completed successfully!');
        $this->newLine();

        // Display optimization summary
        $this->displayOptimizationSummary();

        return 0;
    }

    /**
     * Display optimization summary
     */
    private function displayOptimizationSummary()
    {
        $this->info('📊 Optimization Summary:');
        $this->line('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

        $optimizations = [
            '🗂️  Configuration cached' => 'Faster config loading',
            '🛣️  Routes cached' => 'Faster route resolution',
            '👁️  Views cached' => 'Faster view rendering',
            '⚡ Events cached' => 'Faster event handling',
            '🗺️  Sitemap generated' => 'Better SEO indexing',
            '🤖 Robots.txt generated' => 'Search engine guidance',
            '📦 Popular content cached' => 'Faster page loads',
            '🎨 Frontend assets built' => 'Optimized CSS/JS',
        ];

        foreach ($optimizations as $optimization => $benefit) {
            $this->line(sprintf('  %-30s %s', $optimization, $benefit));
        }

        $this->line('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        $this->newLine();

        $this->info('🔧 Additional Recommendations:');
        $recommendations = [
            'Enable OPcache in PHP for better performance',
            'Configure Redis/Memcached for session and cache storage',
            'Set up a CDN for static assets',
            'Enable Gzip compression on your web server',
            'Configure proper cache headers for static files',
            'Set up monitoring for performance metrics',
        ];

        foreach ($recommendations as $recommendation) {
            $this->line("  • {$recommendation}");
        }

        $this->newLine();
        $this->info('🌟 Your Templates Cave marketplace is now optimized for production!');
    }
}
