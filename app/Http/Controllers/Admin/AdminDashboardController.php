<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\Download;
use App\Models\Comment;

class AdminDashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Get statistics
        $stats = [
            'total_users' => User::count(),
            'total_products' => Product::count(),
            'total_orders' => Order::count(),
            'total_downloads' => Download::count(),
            'pending_comments' => Comment::pending()->count(),
            'active_vendors' => User::vendors()->active()->count(),
            'published_products' => Product::published()->count(),
            'revenue_this_month' => Order::completed()
                ->whereMonth('created_at', now()->month)
                ->sum('amount'),
        ];

        // Get recent activities
        $recentUsers = User::latest()->limit(5)->get();
        $recentProducts = Product::with(['user', 'category'])->latest()->limit(5)->get();
        $recentOrders = Order::with(['user', 'product'])->latest()->limit(5)->get();
        $pendingComments = Comment::with(['user', 'commentable'])->pending()->latest()->limit(5)->get();

        return view('admin.dashboard', compact('stats', 'recentUsers', 'recentProducts', 'recentOrders', 'pendingComments'));
    }
}
