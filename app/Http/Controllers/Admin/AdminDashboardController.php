<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\Download;
use App\Models\Comment;

class AdminDashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Get basic statistics
        $stats = [
            'total_users' => User::count(),
            'total_products' => Product::count(),
            'total_orders' => Order::count(),
            'revenue_this_month' => Order::where('status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('amount'),
        ];

        // Get revenue analytics
        $revenueAnalytics = $this->getRevenueAnalytics();

        // Get recent activities
        $recentUsers = User::latest()->limit(5)->get();
        $recentProducts = Product::with('category')->latest()->limit(10)->get();
        $recentOrders = Order::with('user', 'product')->latest()->limit(5)->get();

        return view('admin.dashboard', compact('stats', 'revenueAnalytics', 'recentUsers', 'recentProducts', 'recentOrders'));
    }

    /**
     * Get revenue analytics data
     */
    private function getRevenueAnalytics()
    {
        $today = now();
        $thisWeek = $today->copy()->startOfWeek();
        $thisMonth = $today->copy()->startOfMonth();
        $lastMonth = $today->copy()->subMonth()->startOfMonth();
        $lastMonthEnd = $today->copy()->subMonth()->endOfMonth();

        // Revenue comparison
        $currentMonthRevenue = Order::where('status', 'completed')
            ->whereBetween('created_at', [$thisMonth, $today])
            ->sum('amount');

        $lastMonthRevenue = Order::where('status', 'completed')
            ->whereBetween('created_at', [$lastMonth, $lastMonthEnd])
            ->sum('amount');

        $revenueGrowth = $lastMonthRevenue > 0
            ? round((($currentMonthRevenue - $lastMonthRevenue) / $lastMonthRevenue) * 100, 1)
            : 0;

        // Daily revenue for the last 7 days
        $dailyRevenue = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = $today->copy()->subDays($i);
            $revenue = Order::where('status', 'completed')
                ->whereDate('created_at', $date)
                ->sum('amount');

            $dailyRevenue[] = [
                'date' => $date->format('M j'),
                'revenue' => (float) $revenue,
                'orders' => Order::where('status', 'completed')
                    ->whereDate('created_at', $date)
                    ->count()
            ];
        }

        // Top products by revenue this month
        $topProducts = Order::where('status', 'completed')
            ->whereBetween('created_at', [$thisMonth, $today])
            ->with('product')
            ->select('product_id', \DB::raw('SUM(amount) as revenue'), \DB::raw('COUNT(*) as sales'))
            ->groupBy('product_id')
            ->orderBy('revenue', 'desc')
            ->limit(5)
            ->get();

        // Payment method distribution
        $paymentMethods = Order::where('status', 'completed')
            ->whereBetween('created_at', [$thisMonth, $today])
            ->select('payment_method', \DB::raw('COUNT(*) as count'), \DB::raw('SUM(amount) as revenue'))
            ->groupBy('payment_method')
            ->get();

        return [
            'current_month_revenue' => $currentMonthRevenue,
            'last_month_revenue' => $lastMonthRevenue,
            'revenue_growth' => $revenueGrowth,
            'daily_revenue' => $dailyRevenue,
            'top_products' => $topProducts,
            'payment_methods' => $paymentMethods,
            'total_revenue' => Order::where('status', 'completed')->sum('amount'),
            'average_order_value' => Order::where('status', 'completed')->avg('amount') ?? 0,
        ];
    }
}
