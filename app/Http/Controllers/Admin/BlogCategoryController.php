<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class BlogCategoryController extends Controller
{
    /**
     * Display a listing of blog categories
     */
    public function index(Request $request)
    {
        $query = BlogCategory::withCount(['posts', 'publishedPosts']);

        // Search
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->get('status') === 'active') {
                $query->where('is_active', true);
            } elseif ($request->get('status') === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Sorting
        $sort = $request->get('sort', 'sort_order');
        switch ($sort) {
            case 'name':
                $query->orderBy('name');
                break;
            case 'posts_count':
                $query->orderBy('posts_count', 'desc');
                break;
            case 'created_at':
                $query->orderBy('created_at', 'desc');
                break;
            default:
                $query->ordered();
                break;
        }

        $categories = $query->paginate(20)->withQueryString();

        return view('admin.blog.categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new blog category
     */
    public function create()
    {
        return view('admin.blog.categories.create');
    }

    /**
     * Store a newly created blog category
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:blog_categories,slug',
            'description' => 'nullable|string|max:1000',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:100',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        $data = $request->all();

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        // Ensure unique slug
        $originalSlug = $data['slug'];
        $counter = 1;
        while (BlogCategory::where('slug', $data['slug'])->exists()) {
            $data['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Set default sort order
        if (empty($data['sort_order'])) {
            $data['sort_order'] = BlogCategory::max('sort_order') + 1;
        }

        BlogCategory::create($data);

        return redirect()->route('admin.blog.categories.index')
            ->with('success', 'Blog category created successfully!');
    }

    /**
     * Display the specified blog category
     */
    public function show(BlogCategory $category)
    {
        $category->load(['posts' => function ($query) {
            $query->with(['author'])->latest()->take(10);
        }]);

        return view('admin.blog.categories.show', compact('category'));
    }

    /**
     * Show the form for editing the specified blog category
     */
    public function edit(BlogCategory $category)
    {
        return view('admin.blog.categories.edit', compact('category'));
    }

    /**
     * Update the specified blog category
     */
    public function update(Request $request, BlogCategory $category)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => ['nullable', 'string', 'max:255', Rule::unique('blog_categories')->ignore($category->id)],
            'description' => 'nullable|string|max:1000',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:100',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        $data = $request->all();

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        // Ensure unique slug (excluding current category)
        $originalSlug = $data['slug'];
        $counter = 1;
        while (BlogCategory::where('slug', $data['slug'])->where('id', '!=', $category->id)->exists()) {
            $data['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        $category->update($data);

        return redirect()->route('admin.blog.categories.index')
            ->with('success', 'Blog category updated successfully!');
    }

    /**
     * Remove the specified blog category
     */
    public function destroy(BlogCategory $category)
    {
        // Check if category has posts
        if ($category->posts()->count() > 0) {
            return redirect()->route('admin.blog.categories.index')
                ->with('error', 'Cannot delete category that has blog posts. Please move or delete the posts first.');
        }

        $category->delete();

        return redirect()->route('admin.blog.categories.index')
            ->with('success', 'Blog category deleted successfully!');
    }

    /**
     * Toggle active status of a category
     */
    public function toggleActive(BlogCategory $category)
    {
        $category->update(['is_active' => !$category->is_active]);

        $status = $category->is_active ? 'activated' : 'deactivated';
        return redirect()->back()->with('success', "Category {$status} successfully.");
    }

    /**
     * Handle bulk actions for blog categories
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'category_ids' => 'required|array',
            'category_ids.*' => 'exists:blog_categories,id',
        ]);

        $action = $request->get('action');
        $categoryIds = $request->get('category_ids');
        $count = count($categoryIds);

        switch ($action) {
            case 'activate':
                BlogCategory::whereIn('id', $categoryIds)->update(['is_active' => true]);
                $message = "{$count} category(ies) activated successfully.";
                break;

            case 'deactivate':
                BlogCategory::whereIn('id', $categoryIds)->update(['is_active' => false]);
                $message = "{$count} category(ies) deactivated successfully.";
                break;

            case 'delete':
                BlogCategory::whereIn('id', $categoryIds)->delete();
                $message = "{$count} category(ies) deleted successfully.";
                break;
        }

        return redirect()->route('admin.blog.categories.index')->with('success', $message);
    }

    /**
     * Update category sort order
     */
    public function updateOrder(Request $request)
    {
        $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:blog_categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->categories as $categoryData) {
            BlogCategory::where('id', $categoryData['id'])
                ->update(['sort_order' => $categoryData['sort_order']]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Toggle category status
     */
    public function toggleStatus(BlogCategory $category)
    {
        $category->update(['is_active' => !$category->is_active]);

        $status = $category->is_active ? 'activated' : 'deactivated';

        return redirect()->route('admin.blog.categories.index')
            ->with('success', "Category {$status} successfully!");
    }
}
