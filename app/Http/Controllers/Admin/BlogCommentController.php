<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogComment;
use App\Models\BlogPost;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BlogCommentController extends Controller
{
    /**
     * Display a listing of blog comments
     */
    public function index(Request $request)
    {
        $query = BlogComment::with(['post', 'user', 'parent']);

        // Search
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('content', 'like', "%{$search}%")
                  ->orWhere('author_name', 'like', "%{$search}%")
                  ->orWhere('author_email', 'like', "%{$search}%")
                  ->orWhereHas('post', function ($postQuery) use ($search) {
                      $postQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Post filter
        if ($request->filled('post')) {
            $query->where('blog_post_id', $request->get('post'));
        }

        // Date range filter
        if ($request->filled('date_range')) {
            $dateRange = $request->get('date_range');
            switch ($dateRange) {
                case 'today':
                    $query->whereDate('created_at', today());
                    break;
                case 'week':
                    $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereBetween('created_at', [now()->startOfMonth(), now()->endOfMonth()]);
                    break;
            }
        }

        $comments = $query->latest()->paginate(20)->withQueryString();
        $posts = BlogPost::orderBy('title')->get();

        // Get comment stats
        $stats = [
            'pending' => BlogComment::where('status', 'pending')->count(),
            'approved' => BlogComment::where('status', 'approved')->count(),
            'spam' => BlogComment::where('status', 'spam')->count(),
        ];

        return view('admin.blog.comments.index', compact('comments', 'posts', 'stats'));
    }

    /**
     * Display the specified comment
     */
    public function show(BlogComment $comment)
    {
        $comment->load(['post', 'user', 'parent', 'replies.user']);
        
        return view('admin.blog.comments.show', compact('comment'));
    }

    /**
     * Approve a comment
     */
    public function approve(BlogComment $comment)
    {
        $comment->approve(Auth::id());

        return redirect()->back()->with('success', 'Comment approved successfully.');
    }

    /**
     * Reject a comment
     */
    public function reject(BlogComment $comment)
    {
        $comment->reject();

        return redirect()->back()->with('success', 'Comment rejected successfully.');
    }

    /**
     * Mark comment as spam
     */
    public function spam(BlogComment $comment)
    {
        $comment->markAsSpam();

        return redirect()->back()->with('success', 'Comment marked as spam.');
    }

    /**
     * Pin/unpin a comment
     */
    public function togglePin(BlogComment $comment)
    {
        if ($comment->is_pinned) {
            $comment->unpin();
            $message = 'Comment unpinned successfully.';
        } else {
            $comment->pin();
            $message = 'Comment pinned successfully.';
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Delete a comment
     */
    public function destroy(BlogComment $comment)
    {
        // Delete all replies first
        $comment->replies()->delete();
        
        // Delete the comment
        $comment->delete();

        return redirect()->route('admin.blog.comments.index')
            ->with('success', 'Comment and all replies deleted successfully.');
    }

    /**
     * Bulk actions for comments
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:approve,reject,spam,delete',
            'comment_ids' => 'required|array',
            'comment_ids.*' => 'exists:blog_comments,id',
        ]);

        $comments = BlogComment::whereIn('id', $request->comment_ids);
        $count = $comments->count();

        switch ($request->action) {
            case 'approve':
                $comments->update([
                    'status' => 'approved',
                    'approved_at' => now(),
                    'approved_by' => Auth::id(),
                ]);
                $message = "{$count} comments approved successfully.";
                break;

            case 'reject':
                $comments->update(['status' => 'rejected']);
                $message = "{$count} comments rejected successfully.";
                break;

            case 'spam':
                $comments->update(['status' => 'spam']);
                $message = "{$count} comments marked as spam.";
                break;

            case 'delete':
                // Delete replies first
                BlogComment::whereIn('parent_id', $request->comment_ids)->delete();
                // Delete comments
                $comments->delete();
                $message = "{$count} comments deleted successfully.";
                break;
        }

        return redirect()->back()->with('success', $message);
    }
}
