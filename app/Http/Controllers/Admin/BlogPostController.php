<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class BlogPostController extends Controller
{
    /**
     * Display a listing of blog posts
     */
    public function index(Request $request)
    {
        $query = BlogPost::with(['author', 'category']);

        // Search
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('blog_category_id', $request->get('category'));
        }

        // Author filter
        if ($request->filled('author')) {
            $query->where('author_id', $request->get('author'));
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        // Sorting
        $sort = $request->get('sort', 'created_at');
        $direction = $request->get('direction', 'desc');
        $query->orderBy($sort, $direction);

        $posts = $query->paginate(20)->withQueryString();

        // Get filter options
        $categories = BlogCategory::active()->ordered()->get();
        $authors = \App\Models\User::where('role', 'admin')->get();

        return view('admin.blog.posts.index', compact('posts', 'categories', 'authors'));
    }

    /**
     * Show the form for creating a new blog post
     */
    public function create()
    {
        $categories = BlogCategory::active()->ordered()->get();
        return view('admin.blog.posts.create', compact('categories'));
    }

    /**
     * Store a newly created blog post
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:blog_posts,slug',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'blog_category_id' => 'nullable|exists:blog_categories,id',
            'status' => 'required|in:draft,published,archived',
            'is_featured' => 'boolean',
            'allow_comments' => 'boolean',
            'tags' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string',
            'canonical_url' => 'nullable|url',
            'published_at' => 'nullable|date',
        ]);

        $data = $request->all();

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        }

        // Ensure unique slug
        $originalSlug = $data['slug'];
        $counter = 1;
        while (BlogPost::where('slug', $data['slug'])->exists()) {
            $data['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Set author
        $data['author_id'] = auth()->id();

        // Handle tags
        if ($request->filled('tags')) {
            $data['tags'] = array_map('trim', explode(',', $request->tags));
        }

        // Handle meta keywords
        if ($request->filled('meta_keywords')) {
            $data['meta_keywords'] = array_map('trim', explode(',', $request->meta_keywords));
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $data['featured_image'] = $this->uploadImage($request->file('featured_image'), 'featured');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            $galleryImages = [];
            foreach ($request->file('gallery_images') as $image) {
                $galleryImages[] = $this->uploadImage($image, 'gallery');
            }
            $data['gallery_images'] = $galleryImages;
        }

        // Set published_at if status is published and no date is set
        if ($data['status'] === 'published' && empty($data['published_at'])) {
            $data['published_at'] = now();
        }

        $post = BlogPost::create($data);

        return redirect()->route('admin.blog.posts.index')
            ->with('success', 'Blog post created successfully!');
    }

    /**
     * Display the specified blog post
     */
    public function show(BlogPost $post)
    {
        $post->load(['author', 'category', 'comments.user']);
        return view('admin.blog.posts.show', compact('post'));
    }

    /**
     * Show the form for editing the specified blog post
     */
    public function edit(BlogPost $post)
    {
        $categories = BlogCategory::active()->ordered()->get();

        // Convert arrays to comma-separated strings for form
        $post->tags_string = $post->tags ? implode(', ', $post->tags) : '';
        $post->meta_keywords_string = $post->meta_keywords ? implode(', ', $post->meta_keywords) : '';

        return view('admin.blog.posts.edit', compact('post', 'categories'));
    }

    /**
     * Update the specified blog post
     */
    public function update(Request $request, BlogPost $post)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => ['nullable', 'string', 'max:255', Rule::unique('blog_posts')->ignore($post->id)],
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'blog_category_id' => 'nullable|exists:blog_categories,id',
            'status' => 'required|in:draft,published,archived',
            'is_featured' => 'boolean',
            'allow_comments' => 'boolean',
            'tags' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string',
            'canonical_url' => 'nullable|url',
            'published_at' => 'nullable|date',
        ]);

        $data = $request->all();

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        }

        // Ensure unique slug (excluding current post)
        $originalSlug = $data['slug'];
        $counter = 1;
        while (BlogPost::where('slug', $data['slug'])->where('id', '!=', $post->id)->exists()) {
            $data['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Handle tags
        if ($request->filled('tags')) {
            $data['tags'] = array_map('trim', explode(',', $request->tags));
        } else {
            $data['tags'] = null;
        }

        // Handle meta keywords
        if ($request->filled('meta_keywords')) {
            $data['meta_keywords'] = array_map('trim', explode(',', $request->meta_keywords));
        } else {
            $data['meta_keywords'] = null;
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($post->featured_image) {
                Storage::disk('public')->delete('blog/featured/' . $post->featured_image);
            }
            $data['featured_image'] = $this->uploadImage($request->file('featured_image'), 'featured');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            // Delete old gallery images
            if ($post->gallery_images) {
                foreach ($post->gallery_images as $image) {
                    Storage::disk('public')->delete('blog/gallery/' . $image);
                }
            }

            $galleryImages = [];
            foreach ($request->file('gallery_images') as $image) {
                $galleryImages[] = $this->uploadImage($image, 'gallery');
            }
            $data['gallery_images'] = $galleryImages;
        }

        // Set published_at if status is published and no date is set
        if ($data['status'] === 'published' && empty($data['published_at']) && $post->status !== 'published') {
            $data['published_at'] = now();
        }

        $post->update($data);

        return redirect()->route('admin.blog.posts.index')
            ->with('success', 'Blog post updated successfully!');
    }

    /**
     * Remove the specified blog post
     */
    public function destroy(BlogPost $post)
    {
        // Delete associated images
        if ($post->featured_image) {
            Storage::disk('public')->delete('blog/featured/' . $post->featured_image);
        }

        if ($post->gallery_images) {
            foreach ($post->gallery_images as $image) {
                Storage::disk('public')->delete('blog/gallery/' . $image);
            }
        }

        $post->delete();

        return redirect()->route('admin.blog.posts.index')
            ->with('success', 'Blog post deleted successfully!');
    }

    /**
     * Upload image helper
     */
    private function uploadImage($file, $type = 'featured')
    {
        $directory = "blog/{$type}";
        $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();

        $file->storeAs($directory, $filename, 'public');

        return $filename;
    }

    /**
     * Bulk actions for blog posts
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,publish,draft,archive,feature,unfeature',
            'posts' => 'required|array',
            'posts.*' => 'exists:blog_posts,id'
        ]);

        $posts = BlogPost::whereIn('id', $request->posts);

        switch ($request->action) {
            case 'delete':
                foreach ($posts->get() as $post) {
                    $this->destroy($post);
                }
                $message = 'Selected posts deleted successfully!';
                break;
            case 'publish':
                $posts->update(['status' => 'published', 'published_at' => now()]);
                $message = 'Selected posts published successfully!';
                break;
            case 'draft':
                $posts->update(['status' => 'draft']);
                $message = 'Selected posts moved to draft!';
                break;
            case 'archive':
                $posts->update(['status' => 'archived']);
                $message = 'Selected posts archived successfully!';
                break;
            case 'feature':
                $posts->update(['is_featured' => true]);
                $message = 'Selected posts featured successfully!';
                break;
            case 'unfeature':
                $posts->update(['is_featured' => false]);
                $message = 'Selected posts unfeatured successfully!';
                break;
        }

        return redirect()->route('admin.blog.posts.index')->with('success', $message);
    }
}
