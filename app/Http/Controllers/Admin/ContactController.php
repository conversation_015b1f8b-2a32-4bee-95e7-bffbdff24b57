<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactSubmission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ContactController extends Controller
{
    /**
     * Display a listing of contact submissions.
     */
    public function index(Request $request)
    {
        $query = ContactSubmission::query()->latest();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search by name, email, or subject
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%");
            });
        }

        $submissions = $query->paginate(20);

        // Get statistics
        $stats = [
            'total' => ContactSubmission::count(),
            'new' => ContactSubmission::where('status', 'new')->count(),
            'read' => ContactSubmission::where('status', 'read')->count(),
            'replied' => ContactSubmission::where('status', 'replied')->count(),
            'archived' => ContactSubmission::where('status', 'archived')->count(),
        ];

        return view('admin.contact.index', compact('submissions', 'stats'));
    }

    /**
     * Display the specified contact submission.
     */
    public function show(ContactSubmission $contact)
    {
        // Mark as read if it's new
        if ($contact->status === 'new') {
            $contact->markAsRead();
        }

        return view('admin.contact.show', compact('contact'));
    }

    /**
     * Update the status of a contact submission.
     */
    public function updateStatus(Request $request, ContactSubmission $contact)
    {
        $request->validate([
            'status' => 'required|in:new,read,replied,archived',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $contact->update([
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
        ]);

        if ($request->status === 'replied' && $contact->replied_at === null) {
            $contact->update(['replied_at' => now()]);
        }

        return back()->with('success', 'Contact submission status updated successfully.');
    }

    /**
     * Send a reply email to the contact submission.
     */
    public function sendReply(Request $request, ContactSubmission $contact)
    {
        $request->validate([
            'reply_subject' => 'required|string|max:255',
            'reply_message' => 'required|string|max:5000',
        ]);

        try {
            // Send reply email
            Mail::send('emails.contact-reply', [
                'original_submission' => $contact,
                'reply_message' => $request->reply_message,
            ], function ($message) use ($contact, $request) {
                $message->to($contact->email, $contact->name)
                    ->subject($request->reply_subject)
                    ->from(config('mail.from.address'), config('mail.from.name'));
            });

            // Update submission status
            $contact->markAsReplied();
            $contact->update([
                'admin_notes' => $request->admin_notes ?? $contact->admin_notes,
            ]);

            return back()->with('success', 'Reply sent successfully!');

        } catch (\Exception $e) {
            \Log::error('Contact reply error: ' . $e->getMessage());

            return back()->with('error', 'Failed to send reply. Please try again.');
        }
    }

    /**
     * Delete a contact submission.
     */
    public function destroy(ContactSubmission $contact)
    {
        $contact->delete();

        return back()->with('success', 'Contact submission deleted successfully.');
    }

    /**
     * Bulk actions for contact submissions.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:mark_read,mark_replied,archive,delete',
            'submissions' => 'required|array',
            'submissions.*' => 'exists:contact_submissions,id',
        ]);

        $submissions = ContactSubmission::whereIn('id', $request->submissions);

        switch ($request->action) {
            case 'mark_read':
                $submissions->update(['status' => 'read', 'read_at' => now()]);
                $message = 'Selected submissions marked as read.';
                break;
            case 'mark_replied':
                $submissions->update(['status' => 'replied', 'replied_at' => now()]);
                $message = 'Selected submissions marked as replied.';
                break;
            case 'archive':
                $submissions->update(['status' => 'archived']);
                $message = 'Selected submissions archived.';
                break;
            case 'delete':
                $submissions->delete();
                $message = 'Selected submissions deleted.';
                break;
        }

        return back()->with('success', $message);
    }
}
