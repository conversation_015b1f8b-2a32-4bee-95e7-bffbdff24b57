<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Newsletter;
use Illuminate\Http\Request;

class NewsletterController extends Controller
{
    /**
     * Display a listing of newsletter subscriptions.
     */
    public function index(Request $request)
    {
        $query = Newsletter::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where('email', 'like', "%{$search}%");
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $subscriptions = $query->latest()->paginate(15)->withQueryString();

        // Get statistics
        $stats = [
            'total' => Newsletter::count(),
            'active' => Newsletter::where('status', 'active')->count(),
            'pending' => Newsletter::where('status', 'pending')->count(),
            'unsubscribed' => Newsletter::where('status', 'unsubscribed')->count(),
        ];

        return view('admin.newsletter.index', compact('subscriptions', 'stats'));
    }

    /**
     * Remove the specified subscription from storage.
     */
    public function destroy(Newsletter $newsletter)
    {
        $newsletter->delete();

        return redirect()->route('admin.newsletter.index')
            ->with('success', 'Newsletter subscription deleted successfully.');
    }

    /**
     * Export subscriptions as CSV.
     */
    public function export(Request $request)
    {
        $query = Newsletter::query();

        // Apply same filters as index method
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where('email', 'like', "%{$search}%");
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $subscriptions = $query->latest()->get();

        $filename = 'newsletter_subscriptions_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($subscriptions) {
            $file = fopen('php://output', 'w');
            
            // Add CSV headers
            fputcsv($file, [
                'ID',
                'Email',
                'Status',
                'Subscribed At',
                'Unsubscribed At',
                'Verified At',
                'Created At'
            ]);

            // Add subscription data
            foreach ($subscriptions as $subscription) {
                fputcsv($file, [
                    $subscription->id,
                    $subscription->email,
                    ucfirst($subscription->status),
                    $subscription->subscribed_at ? $subscription->subscribed_at->format('Y-m-d H:i:s') : '',
                    $subscription->unsubscribed_at ? $subscription->unsubscribed_at->format('Y-m-d H:i:s') : '',
                    $subscription->verified_at ? $subscription->verified_at->format('Y-m-d H:i:s') : '',
                    $subscription->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Toggle subscription status.
     */
    public function toggleStatus(Newsletter $newsletter)
    {
        $newStatus = $newsletter->status === 'active' ? 'unsubscribed' : 'active';
        
        $newsletter->update([
            'status' => $newStatus,
            'unsubscribed_at' => $newStatus === 'unsubscribed' ? now() : null,
        ]);

        $status = $newStatus === 'active' ? 'activated' : 'deactivated';

        return redirect()->back()
            ->with('success', "Subscription {$status} successfully.");
    }
}
