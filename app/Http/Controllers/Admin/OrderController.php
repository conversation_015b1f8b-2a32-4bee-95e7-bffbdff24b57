<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\User;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class OrderController extends Controller
{


    /**
     * Display a listing of orders
     */
    public function index(Request $request)
    {
        $query = Order::with(['user', 'product'])
            ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('payment_id', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('product', function ($productQuery) use ($search) {
                      $productQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Payment method filter
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->get('payment_method'));
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        // Amount range filter
        if ($request->filled('min_amount')) {
            $query->where('amount', '>=', $request->get('min_amount'));
        }

        if ($request->filled('max_amount')) {
            $query->where('amount', '<=', $request->get('max_amount'));
        }

        $orders = $query->paginate(15)->withQueryString();

        // Get statistics for dashboard
        $stats = $this->getOrderStatistics();

        // Get filter options
        $users = User::where('role', 'member')->orderBy('name')->get();
        $products = Product::where('status', 'published')->orderBy('title')->get();

        return view('admin.orders.index', compact('orders', 'stats', 'users', 'products'));
    }

    /**
     * Display the specified order
     */
    public function show(Order $order)
    {
        $order->load(['user', 'product.category', 'product.primaryImage']);
        
        return view('admin.orders.show', compact('order'));
    }

    /**
     * Update order status
     */
    public function updateStatus(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:pending,completed,failed,refunded,cancelled',
            'notes' => 'nullable|string|max:1000',
        ]);

        $oldStatus = $order->status;
        $newStatus = $request->status;

        // Update order
        $order->update([
            'status' => $newStatus,
            'notes' => $request->notes,
        ]);

        // Handle status-specific logic
        if ($newStatus === 'completed' && $oldStatus !== 'completed') {
            $order->markAsCompleted();
        } elseif ($newStatus === 'failed' && $oldStatus !== 'failed') {
            $order->markAsFailed();
        }

        return redirect()->back()->with('success', 'Order status updated successfully.');
    }

    /**
     * Confirm bank transfer payment
     */
    public function confirmBankTransfer(Order $order)
    {
        if ($order->payment_method !== 'bank_transfer' || $order->status !== 'pending') {
            return redirect()->back()->with('error', 'Invalid order for bank transfer confirmation.');
        }

        $order->markAsCompleted();

        return redirect()->back()->with('success', 'Bank transfer payment confirmed successfully.');
    }

    /**
     * Export orders to CSV
     */
    public function export(Request $request)
    {
        $query = Order::with(['user', 'product']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->get('payment_method'));
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        $orders = $query->orderBy('created_at', 'desc')->get();

        $filename = 'orders_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($orders) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Order Number',
                'Customer Name',
                'Customer Email',
                'Product Title',
                'Amount',
                'Currency',
                'Status',
                'Payment Method',
                'Payment ID',
                'Order Date',
                'Paid Date',
                'Notes'
            ]);

            // CSV data
            foreach ($orders as $order) {
                fputcsv($file, [
                    $order->order_number,
                    $order->user->name,
                    $order->user->email,
                    $order->product->title,
                    $order->amount,
                    $order->currency,
                    $order->status,
                    $order->payment_method,
                    $order->payment_id,
                    $order->created_at->format('Y-m-d H:i:s'),
                    $order->paid_at ? $order->paid_at->format('Y-m-d H:i:s') : '',
                    $order->notes
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get order statistics
     */
    private function getOrderStatistics()
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        return [
            'total_orders' => Order::count(),
            'total_revenue' => Order::where('status', 'completed')->sum('amount'),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'completed_orders' => Order::where('status', 'completed')->count(),
            'failed_orders' => Order::where('status', 'failed')->count(),
            
            'today_orders' => Order::whereDate('created_at', $today)->count(),
            'today_revenue' => Order::where('status', 'completed')
                                   ->whereDate('created_at', $today)
                                   ->sum('amount'),
            
            'month_orders' => Order::where('created_at', '>=', $thisMonth)->count(),
            'month_revenue' => Order::where('status', 'completed')
                                   ->where('created_at', '>=', $thisMonth)
                                   ->sum('amount'),
            
            'last_month_orders' => Order::whereBetween('created_at', [
                $lastMonth, 
                $lastMonth->copy()->endOfMonth()
            ])->count(),
            
            'payment_methods' => Order::select('payment_method', DB::raw('count(*) as count'))
                                     ->groupBy('payment_method')
                                     ->pluck('count', 'payment_method')
                                     ->toArray(),
        ];
    }

    /**
     * Get order analytics data
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '30'); // days
        $startDate = Carbon::now()->subDays($period);

        // Daily revenue chart data
        $dailyRevenue = Order::where('status', 'completed')
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(amount) as revenue'),
                DB::raw('COUNT(*) as orders')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Top products by revenue
        $topProducts = Order::where('status', 'completed')
            ->where('created_at', '>=', $startDate)
            ->with('product')
            ->select('product_id', DB::raw('SUM(amount) as revenue'), DB::raw('COUNT(*) as sales'))
            ->groupBy('product_id')
            ->orderBy('revenue', 'desc')
            ->limit(10)
            ->get();

        // Payment method distribution
        $paymentMethods = Order::where('created_at', '>=', $startDate)
            ->select('payment_method', DB::raw('count(*) as count'))
            ->groupBy('payment_method')
            ->get();

        return response()->json([
            'daily_revenue' => $dailyRevenue,
            'top_products' => $topProducts,
            'payment_methods' => $paymentMethods,
        ]);
    }
}
