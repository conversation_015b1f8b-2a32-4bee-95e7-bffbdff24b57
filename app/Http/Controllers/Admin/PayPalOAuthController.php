<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Laravel\Socialite\Facades\Socialite;
use SocialiteProviders\PayPal\Provider;
use Exception;

class PayPalOAuthController extends Controller
{
    /**
     * Redirect to PayPal OAuth
     */
    public function redirect()
    {
        try {
            // Configure PayPal OAuth with current settings
            $this->configurePayPalOAuth();
            
            return Socialite::driver('paypal')
                ->scopes(['openid', 'profile', 'email'])
                ->redirect();
                
        } catch (Exception $e) {
            Log::error('PayPal OAuth redirect failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->route('admin.settings.payment')
                ->with('error', 'Failed to connect to PayPal. Please check your configuration.');
        }
    }

    /**
     * Handle PayPal OAuth callback
     */
    public function callback(Request $request)
    {
        try {
            // Configure PayPal OAuth with current settings
            $this->configurePayPalOAuth();
            
            // Get user info from PayPal
            $paypalUser = Socialite::driver('paypal')->user();
            
            // Extract PayPal credentials from the OAuth response
            $clientId = $paypalUser->token ?? null;
            $clientSecret = session('paypal_temp_secret') ?? null;
            
            // If we don't have the client secret from session, we need to use the existing one
            if (!$clientSecret) {
                $clientSecret = Setting::get('paypal_client_secret');
            }
            
            // Update PayPal settings with OAuth credentials
            $this->updatePayPalSettings([
                'paypal_enabled' => true,
                'paypal_client_id' => $paypalUser->getId(),
                'paypal_client_secret' => $clientSecret,
                'paypal_mode' => Setting::get('paypal_mode', 'sandbox'),
                'paypal_connected' => true,
                'paypal_connected_at' => now(),
                'paypal_user_email' => $paypalUser->getEmail(),
                'paypal_user_name' => $paypalUser->getName(),
            ]);
            
            // Clear temporary session data
            session()->forget('paypal_temp_secret');
            
            return redirect()->route('admin.settings.payment')
                ->with('success', 'PayPal account connected successfully! Your PayPal credentials have been automatically configured.');
                
        } catch (Exception $e) {
            Log::error('PayPal OAuth callback failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            return redirect()->route('admin.settings.payment')
                ->with('error', 'Failed to connect PayPal account. Please try again or configure manually.');
        }
    }

    /**
     * Disconnect PayPal account
     */
    public function disconnect()
    {
        try {
            // Update PayPal settings to disconnect
            $this->updatePayPalSettings([
                'paypal_enabled' => false,
                'paypal_connected' => false,
                'paypal_connected_at' => null,
                'paypal_user_email' => null,
                'paypal_user_name' => null,
            ]);
            
            return redirect()->route('admin.settings.payment')
                ->with('success', 'PayPal account disconnected successfully.');
                
        } catch (Exception $e) {
            Log::error('PayPal disconnect failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->route('admin.settings.payment')
                ->with('error', 'Failed to disconnect PayPal account.');
        }
    }

    /**
     * Test PayPal connection
     */
    public function testConnection()
    {
        try {
            $clientId = Setting::get('paypal_client_id');
            $clientSecret = Setting::get('paypal_client_secret');
            $mode = Setting::get('paypal_mode', 'sandbox');

            if (!$clientId || !$clientSecret) {
                // Update connection status
                Setting::set('paypal_connected', false, 'boolean', 'payment');
                Setting::set('paypal_last_error', 'PayPal credentials are not configured.', 'string', 'payment');

                return response()->json([
                    'success' => false,
                    'message' => 'PayPal credentials are not configured.'
                ]);
            }

            // Test PayPal API connection
            $baseUrl = $mode === 'live'
                ? 'https://api.paypal.com'
                : 'https://api.sandbox.paypal.com';

            // Get access token to test credentials
            $response = $this->getPayPalAccessToken($baseUrl, $clientId, $clientSecret);

            if ($response && isset($response['access_token'])) {
                // Update connection status
                Setting::set('paypal_connected', true, 'boolean', 'payment');
                Setting::set('paypal_last_tested', now(), 'string', 'payment');
                Setting::set('paypal_last_error', null, 'string', 'payment');

                return response()->json([
                    'success' => true,
                    'message' => 'PayPal connection successful!',
                    'details' => [
                        'mode' => $mode,
                        'tested_at' => now()->format('Y-m-d H:i:s'),
                        'token_type' => $response['token_type'] ?? 'Bearer'
                    ]
                ]);
            } else {
                $errorMessage = 'PayPal connection failed. Please check your credentials.';

                // Update connection status
                Setting::set('paypal_connected', false, 'boolean', 'payment');
                Setting::set('paypal_last_error', $errorMessage, 'string', 'payment');

                return response()->json([
                    'success' => false,
                    'message' => $errorMessage
                ]);
            }

        } catch (Exception $e) {
            $errorMessage = 'Connection test failed: ' . $e->getMessage();

            // Update connection status
            Setting::set('paypal_connected', false, 'boolean', 'payment');
            Setting::set('paypal_last_error', $errorMessage, 'string', 'payment');

            Log::error('PayPal connection test failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'client_id' => $clientId ? 'Set' : 'Not set',
                'client_secret' => $clientSecret ? 'Set' : 'Not set',
                'mode' => $mode
            ]);

            return response()->json([
                'success' => false,
                'message' => $errorMessage
            ]);
        }
    }

    /**
     * Configure PayPal OAuth provider
     */
    private function configurePayPalOAuth()
    {
        $clientId = Setting::get('paypal_client_id') ?: config('services.paypal.client_id');
        $clientSecret = Setting::get('paypal_client_secret') ?: config('services.paypal.client_secret');
        $mode = Setting::get('paypal_mode', 'sandbox');
        
        // Store client secret in session for callback
        session(['paypal_temp_secret' => $clientSecret]);
        
        // Configure Socialite PayPal provider
        config([
            'services.paypal.client_id' => $clientId,
            'services.paypal.client_secret' => $clientSecret,
            'services.paypal.redirect' => route('admin.settings.paypal.callback'),
        ]);
    }

    /**
     * Update PayPal settings
     */
    private function updatePayPalSettings(array $settings)
    {
        foreach ($settings as $key => $value) {
            $type = in_array($key, ['paypal_enabled', 'paypal_connected']) ? 'boolean' : 'string';
            Setting::set($key, $value, $type, 'payment');
        }
    }

    /**
     * Get PayPal access token for testing
     */
    private function getPayPalAccessToken($baseUrl, $clientId, $clientSecret)
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $baseUrl . '/v1/oauth2/token',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => 'grant_type=client_credentials',
            CURLOPT_HTTPHEADER => [
                'Accept: application/json',
                'Accept-Language: en_US',
                'Authorization: Basic ' . base64_encode($clientId . ':' . $clientSecret)
            ],
            CURLOPT_TIMEOUT => 30,
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            return json_decode($response, true);
        }
        
        return null;
    }
}
