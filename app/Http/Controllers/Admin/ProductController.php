<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductImage;
use App\Models\Category;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\File;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Product::with(['user', 'category', 'primaryImage']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by admin
        if ($request->filled('admin')) {
            $query->where('user_id', $request->admin);
        }

        $products = $query->latest()->paginate(15)->withQueryString();
        $categories = Category::orderBy('name')->get();
        $admins = User::where('role', 'admin')->orderBy('name')->get();

        return view('admin.products.index', compact('products', 'categories', 'admins'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::where('is_active', true)->orderBy('name')->get();
        $admins = User::where('role', 'admin')->orderBy('name')->get();

        return view('admin.products.create', compact('categories', 'admins'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'short_description' => ['nullable', 'string', 'max:500'],
            'description' => ['required', 'string'],
            'category_id' => ['required', 'exists:categories,id'],
            'user_id' => ['required', 'exists:users,id'],
            'price' => ['required', 'numeric', 'min:0'],
            'is_free' => ['boolean'],
            'demo_url' => ['nullable', 'url'],
            'file_urls' => ['nullable', 'array'],
            'file_urls.*' => ['nullable', 'url'],
            'local_file' => ['nullable', File::types(['zip', 'rar', '7z', 'tar', 'gz'])->max(100 * 1024)], // 100MB max
            'status' => ['required', 'in:draft,published,archived'],
            'is_featured' => ['boolean'],
            'is_trending' => ['boolean'],
            'tags' => ['nullable', 'string'],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:500'],
            'meta_keywords' => ['nullable', 'string'],
            'images' => ['nullable', 'array', 'max:10'],
            'images.*' => ['image', 'mimes:jpeg,png,jpg,gif,webp', 'max:5120'], // 5MB max per image
            'image_alt_texts' => ['nullable', 'array'],
            'image_alt_texts.*' => ['nullable', 'string', 'max:255'],
            'primary_image_index' => ['nullable', 'integer', 'min:0'],
        ]);

        // Handle file upload
        $localFilePath = null;
        if ($request->hasFile('local_file')) {
            $file = $request->file('local_file');
            $filename = time() . '_' . Str::slug(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME)) . '.' . $file->getClientOriginalExtension();
            $localFilePath = $file->storeAs('products/files', $filename, 'public');
        }

        // Create the product
        $product = Product::create([
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'short_description' => $request->short_description,
            'description' => $request->description,
            'category_id' => $request->category_id,
            'user_id' => $request->user_id,
            'price' => $request->price,
            'is_free' => $request->boolean('is_free'),
            'demo_url' => $request->demo_url,
            'file_urls' => $request->file_urls ? array_filter($request->file_urls, function($url) {
                return !empty(trim($url));
            }) : null,
            'local_file_path' => $localFilePath,
            'status' => $request->status,
            'is_featured' => $request->boolean('is_featured'),
            'is_trending' => $request->boolean('is_trending'),
            'tags' => $request->tags ? explode(',', $request->tags) : null,
            'meta_title' => $request->meta_title ?: $request->title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords ? explode(',', $request->meta_keywords) : null,
            'published_at' => $request->status === 'published' ? now() : null,
        ]);

        // Handle image uploads
        if ($request->hasFile('images')) {
            $primaryImageIndex = $request->primary_image_index ?? 0;

            foreach ($request->file('images') as $index => $image) {
                $filename = time() . '_' . $index . '_' . Str::slug(pathinfo($image->getClientOriginalName(), PATHINFO_FILENAME)) . '.' . $image->getClientOriginalExtension();
                $imagePath = $image->storeAs('products/images', $filename, 'public');

                ProductImage::create([
                    'product_id' => $product->id,
                    'image_path' => $imagePath,
                    'alt_text' => $request->image_alt_texts[$index] ?? $product->title,
                    'sort_order' => $index,
                    'is_primary' => $index == $primaryImageIndex,
                ]);
            }
        }

        return redirect()->route('admin.products.index')
            ->with('success', 'Product created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        $product->load(['user', 'category', 'images', 'orders', 'downloads', 'comments']);

        return view('admin.products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        $product->load(['images' => function($query) {
            $query->orderBy('sort_order');
        }]);

        $categories = Category::where('is_active', true)->orderBy('name')->get();
        $admins = User::where('role', 'admin')->orderBy('name')->get();

        return view('admin.products.edit', compact('product', 'categories', 'admins'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'short_description' => ['nullable', 'string', 'max:500'],
            'description' => ['required', 'string'],
            'category_id' => ['required', 'exists:categories,id'],
            'user_id' => ['required', 'exists:users,id'],
            'price' => ['required', 'numeric', 'min:0'],
            'is_free' => ['boolean'],
            'demo_url' => ['nullable', 'url'],
            'file_urls' => ['nullable', 'array'],
            'file_urls.*' => ['nullable', 'url'],
            'local_file' => ['nullable', File::types(['zip', 'rar', '7z', 'tar', 'gz'])->max(100 * 1024)], // 100MB max
            'status' => ['required', 'in:draft,published,archived'],
            'is_featured' => ['boolean'],
            'is_trending' => ['boolean'],
            'tags' => ['nullable', 'string'],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:500'],
            'meta_keywords' => ['nullable', 'string'],
            'new_images' => ['nullable', 'array', 'max:10'],
            'new_images.*' => ['image', 'mimes:jpeg,png,jpg,gif,webp', 'max:5120'], // 5MB max per image
            'new_image_alt_texts' => ['nullable', 'array'],
            'new_image_alt_texts.*' => ['nullable', 'string', 'max:255'],
            'remove_images' => ['nullable', 'array'],
            'remove_images.*' => ['integer', 'exists:product_images,id'],
            'primary_image_id' => ['nullable', 'integer', 'exists:product_images,id'],
        ]);

        // Handle file upload
        $localFilePath = $product->local_file_path;
        if ($request->hasFile('local_file')) {
            // Delete old file if exists
            if ($localFilePath && Storage::disk('public')->exists($localFilePath)) {
                Storage::disk('public')->delete($localFilePath);
            }

            $file = $request->file('local_file');
            $filename = time() . '_' . Str::slug(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME)) . '.' . $file->getClientOriginalExtension();
            $localFilePath = $file->storeAs('products/files', $filename, 'public');
        }

        // Update the product
        $updateData = [
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'short_description' => $request->short_description,
            'description' => $request->description,
            'category_id' => $request->category_id,
            'user_id' => $request->user_id,
            'price' => $request->price,
            'is_free' => $request->boolean('is_free'),
            'demo_url' => $request->demo_url,
            'file_urls' => $request->file_urls ? array_filter($request->file_urls, function($url) {
                return !empty(trim($url));
            }) : null,
            'local_file_path' => $localFilePath,
            'status' => $request->status,
            'is_featured' => $request->boolean('is_featured'),
            'is_trending' => $request->boolean('is_trending'),
            'tags' => $request->tags ? explode(',', $request->tags) : null,
            'meta_title' => $request->meta_title ?: $request->title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords ? explode(',', $request->meta_keywords) : null,
        ];

        // Set published_at if status is being changed to published
        if ($request->status === 'published' && $product->status !== 'published') {
            $updateData['published_at'] = now();
        }

        $product->update($updateData);

        // Handle image removal
        if ($request->filled('remove_images')) {
            $imagesToRemove = ProductImage::whereIn('id', $request->remove_images)
                ->where('product_id', $product->id)
                ->get();

            foreach ($imagesToRemove as $image) {
                if (Storage::disk('public')->exists($image->image_path)) {
                    Storage::disk('public')->delete($image->image_path);
                }
                $image->delete();
            }
        }

        // Handle new image uploads
        if ($request->hasFile('new_images')) {
            $maxSortOrder = $product->images()->max('sort_order') ?? -1;

            foreach ($request->file('new_images') as $index => $image) {
                $filename = time() . '_' . ($maxSortOrder + $index + 1) . '_' . Str::slug(pathinfo($image->getClientOriginalName(), PATHINFO_FILENAME)) . '.' . $image->getClientOriginalExtension();
                $imagePath = $image->storeAs('products/images', $filename, 'public');

                ProductImage::create([
                    'product_id' => $product->id,
                    'image_path' => $imagePath,
                    'alt_text' => $request->new_image_alt_texts[$index] ?? $product->title,
                    'sort_order' => $maxSortOrder + $index + 1,
                    'is_primary' => false,
                ]);
            }
        }

        // Handle primary image update
        if ($request->filled('primary_image_id')) {
            // Reset all images to non-primary
            $product->images()->update(['is_primary' => false]);

            // Set the selected image as primary
            ProductImage::where('id', $request->primary_image_id)
                ->where('product_id', $product->id)
                ->update(['is_primary' => true]);
        }

        return redirect()->route('admin.products.index')
            ->with('success', 'Product updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        // Delete product file if exists
        if ($product->local_file_path && Storage::disk('public')->exists($product->local_file_path)) {
            Storage::disk('public')->delete($product->local_file_path);
        }

        // Delete product images
        foreach ($product->images as $image) {
            if (Storage::disk('public')->exists($image->image_path)) {
                Storage::disk('public')->delete($image->image_path);
            }
        }

        $product->delete();

        return redirect()->route('admin.products.index')
            ->with('success', 'Product deleted successfully.');
    }

    /**
     * Approve a product.
     */
    public function approve(Product $product)
    {
        $product->update([
            'status' => 'published',
            'published_at' => now()
        ]);

        return redirect()->back()
            ->with('success', 'Product approved and published successfully.');
    }

    /**
     * Reject a product.
     */
    public function reject(Product $product)
    {
        $product->update(['status' => 'archived']);

        return redirect()->back()
            ->with('success', 'Product rejected and archived.');
    }

    /**
     * Toggle featured status.
     */
    public function toggleFeatured(Product $product)
    {
        $product->update(['is_featured' => !$product->is_featured]);

        $status = $product->is_featured ? 'featured' : 'unfeatured';

        return redirect()->back()
            ->with('success', "Product {$status} successfully.");
    }

    /**
     * Delete a product image.
     */
    public function deleteImage(ProductImage $image)
    {
        // Check if this image belongs to a product (security check)
        $product = $image->product;

        // If this is the primary image and there are other images, make the first one primary
        if ($image->is_primary && $product->images()->count() > 1) {
            $nextImage = $product->images()->where('id', '!=', $image->id)->orderBy('sort_order')->first();
            if ($nextImage) {
                $nextImage->update(['is_primary' => true]);
            }
        }

        // Delete the file from storage
        if (Storage::disk('public')->exists($image->image_path)) {
            Storage::disk('public')->delete($image->image_path);
        }

        $image->delete();

        return response()->json(['success' => true, 'message' => 'Image deleted successfully.']);
    }

    /**
     * Update image order and primary status.
     */
    public function updateImageOrder(Request $request, Product $product)
    {
        $request->validate([
            'images' => ['required', 'array'],
            'images.*.id' => ['required', 'integer', 'exists:product_images,id'],
            'images.*.sort_order' => ['required', 'integer', 'min:0'],
            'primary_image_id' => ['nullable', 'integer', 'exists:product_images,id'],
        ]);

        // Update sort orders
        foreach ($request->images as $imageData) {
            ProductImage::where('id', $imageData['id'])
                ->where('product_id', $product->id)
                ->update(['sort_order' => $imageData['sort_order']]);
        }

        // Update primary image
        if ($request->filled('primary_image_id')) {
            // Reset all images to non-primary
            $product->images()->update(['is_primary' => false]);

            // Set the selected image as primary
            ProductImage::where('id', $request->primary_image_id)
                ->where('product_id', $product->id)
                ->update(['is_primary' => true]);
        }

        return response()->json(['success' => true, 'message' => 'Image order updated successfully.']);
    }
}
