<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Comment;
use App\Models\Product;
use Illuminate\Http\Request;

class ReviewController extends Controller
{
    /**
     * Display a listing of reviews
     */
    public function index(Request $request)
    {
        $query = Comment::with(['user', 'commentable'])
            ->where('is_review', true)
            ->latest();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by rating
        if ($request->filled('rating')) {
            $query->where('rating', $request->rating);
        }

        // Search by content or user name
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('content', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $reviews = $query->paginate(20);

        $stats = [
            'total' => Comment::where('is_review', true)->count(),
            'pending' => Comment::where('is_review', true)->where('status', 'pending')->count(),
            'approved' => Comment::where('is_review', true)->where('status', 'approved')->count(),
            'rejected' => Comment::where('is_review', true)->where('status', 'rejected')->count(),
        ];

        return view('admin.reviews.index', compact('reviews', 'stats'));
    }

    /**
     * Display the specified review
     */
    public function show(Comment $review)
    {
        $review->load(['user', 'commentable']);
        
        if (!$review->is_review) {
            abort(404);
        }

        return view('admin.reviews.show', compact('review'));
    }

    /**
     * Approve a review
     */
    public function approve(Comment $review)
    {
        if (!$review->is_review) {
            abort(404);
        }

        $review->update(['status' => 'approved']);

        return back()->with('success', 'Review approved successfully.');
    }

    /**
     * Reject a review
     */
    public function reject(Comment $review)
    {
        if (!$review->is_review) {
            abort(404);
        }

        $review->update(['status' => 'rejected']);

        return back()->with('success', 'Review rejected successfully.');
    }

    /**
     * Delete a review
     */
    public function destroy(Comment $review)
    {
        if (!$review->is_review) {
            abort(404);
        }

        $review->delete();

        return back()->with('success', 'Review deleted successfully.');
    }

    /**
     * Bulk actions for reviews
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:approve,reject,delete',
            'reviews' => 'required|array',
            'reviews.*' => 'exists:comments,id'
        ]);

        $reviews = Comment::whereIn('id', $request->reviews)
            ->where('is_review', true)
            ->get();

        $count = 0;
        foreach ($reviews as $review) {
            switch ($request->action) {
                case 'approve':
                    $review->update(['status' => 'approved']);
                    $count++;
                    break;
                case 'reject':
                    $review->update(['status' => 'rejected']);
                    $count++;
                    break;
                case 'delete':
                    $review->delete();
                    $count++;
                    break;
            }
        }

        $action = ucfirst($request->action);
        return back()->with('success', "{$count} reviews {$action}d successfully.");
    }
}
