<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\SEOService;
use App\Services\PerformanceService;
use App\Services\AnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SEOController extends Controller
{
    protected $seoService;
    protected $performanceService;
    protected $analyticsService;

    public function __construct(
        SEOService $seoService,
        PerformanceService $performanceService,
        AnalyticsService $analyticsService
    ) {
        $this->seoService = $seoService;
        $this->performanceService = $performanceService;
        $this->analyticsService = $analyticsService;
    }

    /**
     * SEO dashboard
     */
    public function index()
    {
        $performanceReport = $this->performanceService->generatePerformanceReport();
        $analyticsReport = $this->analyticsService->generateAnalyticsReport();
        $imageOptimizations = $this->performanceService->getImageOptimizationSuggestions();

        return view('admin.seo.index', compact(
            'performanceReport',
            'analyticsReport',
            'imageOptimizations'
        ));
    }

    /**
     * Performance monitoring
     */
    public function performance()
    {
        $metrics = $this->performanceService->getPerformanceMetrics(24);
        $slowQueries = $this->performanceService->getSlowQueries();
        $criticalMetrics = $this->performanceService->getCriticalMetrics();
        $optimizations = $this->performanceService->optimizeQueries();

        return view('admin.seo.performance', compact(
            'metrics',
            'slowQueries',
            'criticalMetrics',
            'optimizations'
        ));
    }

    /**
     * Analytics dashboard
     */
    public function analytics()
    {
        $userBehavior = $this->analyticsService->getUserBehaviorAnalytics(30);
        $searchAnalytics = $this->analyticsService->getSearchAnalytics(30);
        $popularProducts = $this->analyticsService->getPopularProducts(30);
        $conversionMetrics = $this->analyticsService->getConversionMetrics(30);

        return view('admin.seo.analytics', compact(
            'userBehavior',
            'searchAnalytics',
            'popularProducts',
            'conversionMetrics'
        ));
    }

    /**
     * SEO tools
     */
    public function tools()
    {
        return view('admin.seo.tools');
    }

    /**
     * Generate sitemap
     */
    public function generateSitemap()
    {
        try {
            $sitemap = $this->seoService->generateSitemap();
            Cache::forget('sitemap');

            return response()->json([
                'success' => true,
                'message' => 'Sitemap generated successfully',
                'url' => route('sitemap')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate sitemap: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear caches
     */
    public function clearCaches()
    {
        try {
            $this->performanceService->clearPerformanceCaches();
            Cache::flush();

            return response()->json([
                'success' => true,
                'message' => 'All caches cleared successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear caches: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test page speed
     */
    public function testPageSpeed(Request $request)
    {
        $url = $request->input('url');

        if (!$url) {
            return response()->json([
                'success' => false,
                'message' => 'URL is required'
            ], 400);
        }

        try {
            $startTime = microtime(true);
            $response = file_get_contents($url);
            $loadTime = (microtime(true) - $startTime) * 1000;

            $size = strlen($response);
            $gzipSize = strlen(gzencode($response));
            $compressionRatio = round((1 - $gzipSize / $size) * 100, 2);

            return response()->json([
                'success' => true,
                'data' => [
                    'load_time' => round($loadTime, 2),
                    'size' => $size,
                    'gzip_size' => $gzipSize,
                    'compression_ratio' => $compressionRatio,
                    'status' => $loadTime < 1000 ? 'excellent' : ($loadTime < 3000 ? 'good' : 'needs_improvement')
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to test page speed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * SEO audit
     */
    public function audit(Request $request)
    {
        $url = $request->input('url', route('home'));

        try {
            $content = file_get_contents($url);
            $audit = $this->performSEOAudit($content, $url);

            return response()->json([
                'success' => true,
                'data' => $audit
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to perform SEO audit: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Perform SEO audit on content
     */
    private function performSEOAudit($content, $url)
    {
        $issues = [];
        $recommendations = [];
        $score = 100;

        // Check title tag
        if (!preg_match('/<title[^>]*>(.*?)<\/title>/i', $content, $matches)) {
            $issues[] = 'Missing title tag';
            $score -= 15;
        } else {
            $title = trim($matches[1]);
            if (strlen($title) < 30) {
                $recommendations[] = 'Title tag is too short (less than 30 characters)';
                $score -= 5;
            } elseif (strlen($title) > 60) {
                $recommendations[] = 'Title tag is too long (more than 60 characters)';
                $score -= 5;
            }
        }

        // Check meta description
        if (!preg_match('/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $content, $matches)) {
            $issues[] = 'Missing meta description';
            $score -= 10;
        } else {
            $description = trim($matches[1]);
            if (strlen($description) < 120) {
                $recommendations[] = 'Meta description is too short (less than 120 characters)';
                $score -= 3;
            } elseif (strlen($description) > 160) {
                $recommendations[] = 'Meta description is too long (more than 160 characters)';
                $score -= 3;
            }
        }

        // Check H1 tags
        $h1Count = preg_match_all('/<h1[^>]*>/i', $content);
        if ($h1Count === 0) {
            $issues[] = 'Missing H1 tag';
            $score -= 10;
        } elseif ($h1Count > 1) {
            $recommendations[] = 'Multiple H1 tags found (should be only one)';
            $score -= 5;
        }

        // Check images without alt text
        $imgCount = preg_match_all('/<img[^>]*>/i', $content, $imgMatches);
        $imgWithoutAlt = 0;
        foreach ($imgMatches[0] as $img) {
            if (!preg_match('/alt=["\'][^"\']*["\']/', $img)) {
                $imgWithoutAlt++;
            }
        }
        if ($imgWithoutAlt > 0) {
            $recommendations[] = "{$imgWithoutAlt} images without alt text";
            $score -= min($imgWithoutAlt * 2, 10);
        }

        // Check canonical URL
        if (!preg_match('/<link[^>]*rel=["\']canonical["\'][^>]*>/i', $content)) {
            $recommendations[] = 'Missing canonical URL';
            $score -= 3;
        }

        // Check Open Graph tags
        $ogTags = ['og:title', 'og:description', 'og:image', 'og:url'];
        $missingOgTags = [];
        foreach ($ogTags as $tag) {
            if (!preg_match('/<meta[^>]*property=["\']' . preg_quote($tag) . '["\'][^>]*>/i', $content)) {
                $missingOgTags[] = $tag;
            }
        }
        if (!empty($missingOgTags)) {
            $recommendations[] = 'Missing Open Graph tags: ' . implode(', ', $missingOgTags);
            $score -= count($missingOgTags) * 2;
        }

        return [
            'score' => max($score, 0),
            'grade' => $this->getGrade($score),
            'issues' => $issues,
            'recommendations' => $recommendations,
            'url' => $url,
            'audited_at' => now(),
        ];
    }

    /**
     * Get grade based on score
     */
    private function getGrade($score)
    {
        if ($score >= 90) return 'A';
        if ($score >= 80) return 'B';
        if ($score >= 70) return 'C';
        if ($score >= 60) return 'D';
        return 'F';
    }
}
