<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{


    /**
     * Display the general settings page
     */
    public function general()
    {
        $settings = Setting::getGroup('general');
        return view('admin.settings.general', compact('settings'));
    }

    /**
     * Update general settings
     */
    public function updateGeneral(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'site_name' => 'required|string|max:255',
            'site_tagline' => 'nullable|string|max:255',
            'site_description' => 'nullable|string|max:1000',
            'site_keywords' => 'nullable|string|max:500',
            'contact_email' => 'required|email|max:255',
            'support_email' => 'required|email|max:255',
            'admin_email' => 'required|email|max:255',
            'contact_phone' => 'nullable|string|max:50',
            'contact_address' => 'nullable|string|max:500',
            'timezone' => 'required|string|max:100',
            'default_currency' => 'required|string|max:10',
            'currency_symbol' => 'required|string|max:10',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'facebook_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'linkedin_url' => 'nullable|url|max:255',
            'youtube_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Save general settings
        $generalSettings = [
            'site_name' => $request->site_name,
            'site_tagline' => $request->site_tagline,
            'site_description' => $request->site_description,
            'site_keywords' => $request->site_keywords,
            'contact_email' => $request->contact_email,
            'support_email' => $request->support_email,
            'admin_email' => $request->admin_email,
            'contact_phone' => $request->contact_phone,
            'contact_address' => $request->contact_address,
            'timezone' => $request->timezone,
            'default_currency' => $request->default_currency,
            'currency_symbol' => $request->currency_symbol,
            'commission_rate' => $request->commission_rate,
            'facebook_url' => $request->facebook_url,
            'twitter_url' => $request->twitter_url,
            'linkedin_url' => $request->linkedin_url,
            'youtube_url' => $request->youtube_url,
            'instagram_url' => $request->instagram_url,
        ];

        foreach ($generalSettings as $key => $value) {
            $type = in_array($key, ['commission_rate']) ? 'float' : 'string';
            Setting::set($key, $value, $type, 'general');
        }

        return redirect()->back()->with('success', 'General settings updated successfully.');
    }

    /**
     * Display the email settings page
     */
    public function email()
    {
        $settings = Setting::getGroup('email');
        return view('admin.settings.email', compact('settings'));
    }

    /**
     * Update email settings
     */
    public function updateEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'mail_driver' => 'required|string|in:smtp,sendmail,mailgun,ses,postmark',
            'mail_host' => 'required_if:mail_driver,smtp|nullable|string|max:255',
            'mail_port' => 'required_if:mail_driver,smtp|nullable|integer|min:1|max:65535',
            'mail_username' => 'required_if:mail_driver,smtp|nullable|string|max:255',
            'mail_password' => 'nullable|string|max:255',
            'mail_encryption' => 'nullable|string|in:tls,ssl',
            'mail_from_address' => 'required|email|max:255',
            'mail_from_name' => 'required|string|max:255',
            'imap_enabled' => 'boolean',
            'imap_host' => 'nullable|string|max:255',
            'imap_port' => 'nullable|integer|min:1|max:65535',
            'imap_username' => 'nullable|string|max:255',
            'imap_password' => 'nullable|string|max:255',
            'imap_encryption' => 'nullable|string|in:tls,ssl',
            'imap_validate_cert' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Save email settings
        $emailSettings = [
            'mail_driver' => $request->mail_driver,
            'mail_host' => $request->mail_host,
            'mail_port' => $request->mail_port,
            'mail_username' => $request->mail_username,
            'mail_encryption' => $request->mail_encryption,
            'mail_from_address' => $request->mail_from_address,
            'mail_from_name' => $request->mail_from_name,
            'imap_enabled' => $request->boolean('imap_enabled'),
            'imap_host' => $request->imap_host,
            'imap_port' => $request->imap_port,
            'imap_username' => $request->imap_username,
            'imap_encryption' => $request->imap_encryption,
            'imap_validate_cert' => $request->boolean('imap_validate_cert'),
        ];

        // Only update passwords if provided
        if ($request->filled('mail_password')) {
            $emailSettings['mail_password'] = $request->mail_password;
        }

        if ($request->filled('imap_password')) {
            $emailSettings['imap_password'] = $request->imap_password;
        }

        foreach ($emailSettings as $key => $value) {
            $type = 'string';
            if (in_array($key, ['mail_port', 'imap_port'])) {
                $type = 'integer';
            } elseif (in_array($key, ['imap_enabled', 'imap_validate_cert'])) {
                $type = 'boolean';
            }
            Setting::set($key, $value, $type, 'email');
        }

        return redirect()->back()->with('success', 'Email settings updated successfully.');
    }

    /**
     * Test IMAP connection
     */
    public function testImap(Request $request)
    {
        try {
            $settings = Setting::getGroup('email');

            if (!($settings['imap_enabled'] ?? false)) {
                return response()->json([
                    'success' => false,
                    'message' => 'IMAP is not enabled in settings.'
                ]);
            }

            $host = $settings['imap_host'] ?? '';
            $port = $settings['imap_port'] ?? 993;
            $username = $settings['imap_username'] ?? '';
            $password = $settings['imap_password'] ?? '';
            $encryption = $settings['imap_encryption'] ?? 'ssl';
            $validateCert = $settings['imap_validate_cert'] ?? true;

            if (empty($host) || empty($username) || empty($password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'IMAP host, username, and password are required.'
                ]);
            }

            // Build connection string
            $connectionString = '{' . $host . ':' . $port . '/imap';
            if ($encryption) {
                $connectionString .= '/' . $encryption;
            }
            if (!$validateCert) {
                $connectionString .= '/novalidate-cert';
            }
            $connectionString .= '}INBOX';

            // Test IMAP connection
            $connection = @imap_open($connectionString, $username, $password);

            if ($connection) {
                $mailboxInfo = imap_status($connection, $connectionString, SA_ALL);
                imap_close($connection);

                return response()->json([
                    'success' => true,
                    'message' => 'IMAP connection successful! Found ' . ($mailboxInfo->messages ?? 0) . ' messages in INBOX.'
                ]);
            } else {
                $error = imap_last_error() ?: 'Unknown IMAP connection error';
                return response()->json([
                    'success' => false,
                    'message' => 'IMAP connection failed: ' . $error
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'IMAP test failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test SMTP connection
     */
    public function testSmtp(Request $request)
    {
        try {
            $settings = Setting::getGroup('email');

            // Configure mail settings temporarily
            config([
                'mail.default' => 'smtp',
                'mail.mailers.smtp.host' => $settings['mail_host'] ?? '',
                'mail.mailers.smtp.port' => $settings['mail_port'] ?? 587,
                'mail.mailers.smtp.encryption' => $settings['mail_encryption'] ?? 'tls',
                'mail.mailers.smtp.username' => $settings['mail_username'] ?? '',
                'mail.mailers.smtp.password' => $settings['mail_password'] ?? '',
                'mail.from.address' => $settings['mail_from_address'] ?? config('mail.from.address'),
                'mail.from.name' => $settings['mail_from_name'] ?? config('mail.from.name'),
            ]);

            // Test SMTP connection by sending a test email to the admin
            $adminEmail = auth()->user()->email;

            \Mail::raw('This is a test email to verify your SMTP configuration is working correctly.', function ($message) use ($adminEmail) {
                $message->to($adminEmail)
                        ->subject('SMTP Test - Templates Cave');
            });

            return response()->json([
                'success' => true,
                'message' => 'SMTP connection successful! Test email sent to ' . $adminEmail
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'SMTP connection failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test email configuration
     */
    public function testEmail(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email',
        ]);

        try {
            Mail::raw('This is a test email from Templates Cave admin panel.', function ($message) use ($request) {
                $message->to($request->test_email)
                    ->subject('Test Email - Templates Cave');
            });

            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the payment settings page
     */
    public function payment()
    {
        $settings = Setting::getGroup('payment');
        return view('admin.settings.payment', compact('settings'));
    }

    /**
     * Update payment settings
     */
    public function updatePayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'stripe_enabled' => 'boolean',
            'stripe_key' => 'nullable|string|max:255',
            'stripe_secret' => 'nullable|string|max:255',
            'stripe_webhook_secret' => 'nullable|string|max:255',
            'paypal_enabled' => 'boolean',
            'paypal_client_id' => 'nullable|string|max:255',
            'paypal_client_secret' => 'nullable|string|max:255',
            'paypal_mode' => 'required|string|in:sandbox,live',
            'bank_transfer_enabled' => 'boolean',
            'bank_account_name' => 'nullable|string|max:255',
            'bank_account_number' => 'nullable|string|max:255',
            'bank_name' => 'nullable|string|max:255',
            'bank_code' => 'nullable|string|max:255',
            'bank_swift_code' => 'nullable|string|max:255',
            'bank_iban' => 'nullable|string|max:255',
            'bank_transfer_instructions' => 'nullable|string|max:1000',
            'nowpayments_enabled' => 'boolean',
            'nowpayments_api_key' => 'nullable|string|max:255',
            'nowpayments_sandbox' => 'boolean',
            'nowpayments_ipn_secret' => 'nullable|string|max:255',
            'nowpayments_callback_url' => 'nullable|url|max:255',
            'nowpayments_accepted_currencies' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Save payment settings
        $paymentSettings = [
            'stripe_enabled' => $request->boolean('stripe_enabled'),
            'stripe_key' => $request->stripe_key,
            'stripe_secret' => $request->stripe_secret,
            'stripe_webhook_secret' => $request->stripe_webhook_secret,
            'paypal_enabled' => $request->boolean('paypal_enabled'),
            'paypal_client_id' => $request->paypal_client_id,
            'paypal_client_secret' => $request->paypal_client_secret,
            'paypal_mode' => $request->paypal_mode,
            'bank_transfer_enabled' => $request->boolean('bank_transfer_enabled'),
            'bank_account_name' => $request->bank_account_name,
            'bank_account_number' => $request->bank_account_number,
            'bank_name' => $request->bank_name,
            'bank_code' => $request->bank_code,
            'bank_swift_code' => $request->bank_swift_code,
            'bank_iban' => $request->bank_iban,
            'bank_transfer_instructions' => $request->bank_transfer_instructions,
            'nowpayments_enabled' => $request->boolean('nowpayments_enabled'),
            'nowpayments_api_key' => $request->nowpayments_api_key,
            'nowpayments_sandbox' => $request->boolean('nowpayments_sandbox'),
            'nowpayments_ipn_secret' => $request->nowpayments_ipn_secret,
            'nowpayments_callback_url' => $request->nowpayments_callback_url,
            'nowpayments_accepted_currencies' => $request->nowpayments_accepted_currencies,
        ];

        foreach ($paymentSettings as $key => $value) {
            $type = in_array($key, ['stripe_enabled', 'paypal_enabled', 'bank_transfer_enabled', 'nowpayments_enabled', 'nowpayments_sandbox']) ? 'boolean' : 'string';
            Setting::set($key, $value, $type, 'payment');
        }

        // Clear all settings cache to ensure fresh data
        Setting::clearCache();

        return redirect()->back()->with('success', 'Payment settings updated successfully.');
    }

    /**
     * Display the SEO settings page
     */
    public function seo()
    {
        $settings = Setting::getGroup('seo');
        return view('admin.settings.seo', compact('settings'));
    }

    /**
     * Update SEO settings
     */
    public function updateSeo(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'meta_title_suffix' => 'nullable|string|max:255',
            'meta_description_default' => 'nullable|string|max:500',
            'meta_keywords_default' => 'nullable|string|max:500',
            'google_analytics_id' => 'nullable|string|max:255',
            'google_analytics_enabled' => 'boolean',
            'sitemap_enabled' => 'boolean',
            'robots_txt_enabled' => 'boolean',
            'google_site_verification' => 'nullable|string|max:255',
            'bing_site_verification' => 'nullable|string|max:255',
            'facebook_app_id' => 'nullable|string|max:255',
            'twitter_site' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Save SEO settings
        $seoSettings = [
            'meta_title_suffix' => $request->meta_title_suffix,
            'meta_description_default' => $request->meta_description_default,
            'meta_keywords_default' => $request->meta_keywords_default,
            'google_analytics_id' => $request->google_analytics_id,
            'google_analytics_enabled' => $request->boolean('google_analytics_enabled'),
            'sitemap_enabled' => $request->boolean('sitemap_enabled'),
            'robots_txt_enabled' => $request->boolean('robots_txt_enabled'),
            'google_site_verification' => $request->google_site_verification,
            'bing_site_verification' => $request->bing_site_verification,
            'facebook_app_id' => $request->facebook_app_id,
            'twitter_site' => $request->twitter_site,
        ];

        foreach ($seoSettings as $key => $value) {
            $type = in_array($key, ['google_analytics_enabled', 'sitemap_enabled', 'robots_txt_enabled']) ? 'boolean' : 'string';
            Setting::set($key, $value, $type, 'seo');
        }

        return redirect()->back()->with('success', 'SEO settings updated successfully.');
    }

    /**
     * Test NOWPayments connection
     */
    public function testNowPayments(Request $request)
    {
        try {
            $settings = Setting::getGroup('payment');

            if (!($settings['nowpayments_enabled'] ?? false)) {
                return response()->json([
                    'success' => false,
                    'message' => 'NOWPayments is not enabled in settings.'
                ]);
            }

            $apiKey = $settings['nowpayments_api_key'] ?? '';
            $sandbox = $settings['nowpayments_sandbox'] ?? false;

            if (empty($apiKey)) {
                return response()->json([
                    'success' => false,
                    'message' => 'NOWPayments API key is required.'
                ]);
            }

            // Determine API URL based on sandbox mode
            $baseUrl = $sandbox ? 'https://api-sandbox.nowpayments.io' : 'https://api.nowpayments.io';

            // Test API connection by getting status
            $response = Http::withHeaders([
                'x-api-key' => $apiKey,
                'Content-Type' => 'application/json',
            ])->get($baseUrl . '/v1/status');

            if ($response->successful()) {
                $data = $response->json();
                return response()->json([
                    'success' => true,
                    'message' => 'NOWPayments connection successful! Status: ' . ($data['message'] ?? 'OK')
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'NOWPayments connection failed: ' . $response->body()
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'NOWPayments test failed: ' . $e->getMessage()
            ]);
        }
    }
}
