<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Category;
use App\Models\Download;

class DashboardController extends Controller
{
    /**
     * Display the user dashboard.
     */
    public function index()
    {
        $user = auth()->user();

        // Get user's recent downloads
        $recentDownloads = Download::with(['product', 'product.category'])
            ->where('user_id', $user->id)
            ->latest()
            ->limit(5)
            ->get();

        // Get featured products
        $featuredProducts = Product::with(['category', 'user', 'primaryImage'])
            ->published()
            ->featured()
            ->limit(6)
            ->get();

        // Get user's download count
        $downloadCount = Download::where('user_id', $user->id)->count();

        return view('dashboard', compact('recentDownloads', 'featuredProducts', 'downloadCount'));
    }
}
