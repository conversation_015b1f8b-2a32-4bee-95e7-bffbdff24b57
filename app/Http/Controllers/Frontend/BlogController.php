<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Models\BlogComment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class BlogController extends Controller
{
    /**
     * Display blog index with posts
     */
    public function index(Request $request)
    {
        $query = BlogPost::with(['author', 'category'])
            ->where('status', 'published')
            ->where('published_at', '<=', now());

        // Search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->get('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('excerpt', 'like', "%{$searchTerm}%")
                  ->orWhere('content', 'like', "%{$searchTerm}%")
                  ->orWhereJsonContains('tags', $searchTerm);
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $category = BlogCategory::where('slug', $request->get('category'))->first();
            if ($category) {
                $query->where('blog_category_id', $category->id);
            }
        }

        // Tag filter
        if ($request->filled('tag')) {
            $query->whereJsonContains('tags', $request->get('tag'));
        }

        // Featured filter
        if ($request->boolean('featured')) {
            $query->where('is_featured', true);
        }

        // Date filter
        if ($request->filled('year')) {
            $query->whereYear('published_at', $request->get('year'));
        }

        if ($request->filled('month')) {
            $query->whereMonth('published_at', $request->get('month'));
        }

        // Sorting
        $sort = $request->get('sort', 'latest');
        switch ($sort) {
            case 'popular':
                $query->orderBy('view_count', 'desc');
                break;
            case 'comments':
                $query->orderBy('comment_count', 'desc');
                break;
            case 'likes':
                $query->orderBy('like_count', 'desc');
                break;
            case 'oldest':
                $query->orderBy('published_at', 'asc');
                break;
            case 'latest':
            default:
                $query->orderBy('published_at', 'desc');
                break;
        }

        // Pagination
        $perPage = $request->get('per_page', 12);
        $perPage = in_array($perPage, [6, 12, 24]) ? $perPage : 12;
        $posts = $query->paginate($perPage)->withQueryString();

        // Get categories for sidebar
        $categories = BlogCategory::active()
            ->withCount(['publishedPosts'])
            ->ordered()
            ->get();

        // Get popular tags
        $popularTags = $this->getPopularTags();

        // Get recent posts for sidebar
        $recentPosts = BlogPost::with(['author', 'category'])
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->orderBy('published_at', 'desc')
            ->limit(5)
            ->get();

        // Get featured posts
        $featuredPosts = BlogPost::with(['author', 'category'])
            ->where('status', 'published')
            ->where('is_featured', true)
            ->where('published_at', '<=', now())
            ->orderBy('published_at', 'desc')
            ->limit(3)
            ->get();

        return view('frontend.blog.index', compact(
            'posts',
            'categories',
            'popularTags',
            'recentPosts',
            'featuredPosts'
        ));
    }

    /**
     * Display a single blog post
     */
    public function show(Request $request, $slug)
    {
        $post = BlogPost::with(['author', 'category', 'approvedComments.user', 'approvedComments.replies.user'])
            ->where('slug', $slug)
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->firstOrFail();

        // Increment view count
        $post->increment('view_count');

        // Get related posts
        $relatedPosts = BlogPost::with(['author', 'category'])
            ->where('status', 'published')
            ->where('id', '!=', $post->id)
            ->where(function ($query) use ($post) {
                $query->where('blog_category_id', $post->blog_category_id);

                // Also match by tags if available
                if ($post->tags) {
                    foreach ($post->tags as $tag) {
                        $query->orWhereJsonContains('tags', $tag);
                    }
                }
            })
            ->orderBy('published_at', 'desc')
            ->limit(4)
            ->get();

        // Get comments with pagination
        $comments = $post->approvedComments()
            ->with(['user', 'replies.user'])
            ->whereNull('parent_id') // Only top-level comments
            ->orderBy('is_pinned', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Get next and previous posts
        $nextPost = BlogPost::where('status', 'published')
            ->where('published_at', '>', $post->published_at)
            ->orderBy('published_at', 'asc')
            ->first();

        $previousPost = BlogPost::where('status', 'published')
            ->where('published_at', '<', $post->published_at)
            ->orderBy('published_at', 'desc')
            ->first();

        return view('frontend.blog.show', compact(
            'post',
            'relatedPosts',
            'comments',
            'nextPost',
            'previousPost'
        ));
    }

    /**
     * Display posts by category
     */
    public function category($slug)
    {
        $category = BlogCategory::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        $posts = BlogPost::with(['author', 'category'])
            ->where('blog_category_id', $category->id)
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        // Get other categories for sidebar
        $otherCategories = BlogCategory::active()
            ->where('id', '!=', $category->id)
            ->withCount(['publishedPosts'])
            ->ordered()
            ->get();

        // Get popular tags
        $popularTags = $this->getPopularTags();

        // Get recent posts for sidebar
        $recentPosts = BlogPost::with(['author', 'category'])
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->orderBy('published_at', 'desc')
            ->limit(5)
            ->get();

        return view('frontend.blog.category', compact('category', 'posts', 'otherCategories', 'popularTags', 'recentPosts'));
    }

    /**
     * Display posts by tag
     */
    public function tag($tag)
    {
        $posts = BlogPost::with(['author', 'category'])
            ->whereJsonContains('tags', $tag)
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        // Get categories for filter dropdown
        $categories = BlogCategory::active()
            ->withCount(['publishedPosts'])
            ->ordered()
            ->get();

        // Get popular tags
        $popularTags = $this->getPopularTags();

        // Get recent posts for sidebar
        $recentPosts = BlogPost::with(['author', 'category'])
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->orderBy('published_at', 'desc')
            ->limit(5)
            ->get();

        return view('frontend.blog.tag', compact('tag', 'posts', 'categories', 'popularTags', 'recentPosts'));
    }

    /**
     * Store a comment for a blog post
     */
    public function storeComment(Request $request, $slug)
    {
        $post = BlogPost::where('slug', $slug)
            ->where('status', 'published')
            ->where('allow_comments', true)
            ->firstOrFail();

        $request->validate([
            'content' => 'required|string|max:2000',
            'parent_id' => 'nullable|exists:blog_comments,id',
            'author_name' => 'required_without:user_id|string|max:100',
            'author_email' => 'required_without:user_id|email|max:100',
            'author_website' => 'nullable|url|max:200',
        ]);

        $comment = new BlogComment();
        $comment->blog_post_id = $post->id;
        $comment->content = $request->content;
        $comment->parent_id = $request->parent_id;
        $comment->ip_address = $request->ip();
        $comment->user_agent = $request->userAgent();

        if (auth()->check()) {
            $comment->user_id = auth()->id();
            $comment->status = 'approved'; // Auto-approve for registered users
        } else {
            $comment->author_name = $request->author_name;
            $comment->author_email = $request->author_email;
            $comment->author_website = $request->author_website;
            $comment->status = 'pending'; // Require moderation for guests
        }

        $comment->save();

        if ($comment->status === 'approved') {
            $post->increment('comment_count');
        }

        return back()->with('success',
            $comment->status === 'approved'
                ? 'Comment posted successfully!'
                : 'Comment submitted for moderation.'
        );
    }

    /**
     * Get popular tags
     */
    private function getPopularTags($limit = 20)
    {
        return Cache::remember('blog.popular_tags', 3600, function () use ($limit) {
            $tags = DB::table('blog_posts')
                ->where('status', 'published')
                ->whereNotNull('tags')
                ->pluck('tags')
                ->flatten()
                ->map(function ($tagJson) {
                    return json_decode($tagJson, true);
                })
                ->flatten()
                ->filter()
                ->countBy()
                ->sortDesc()
                ->take($limit)
                ->keys()
                ->toArray();

            return $tags;
        });
    }

    /**
     * Search blog posts (AJAX)
     */
    public function search(Request $request)
    {
        $query = $request->get('q');

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $posts = BlogPost::where('status', 'published')
            ->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('excerpt', 'like', "%{$query}%");
            })
            ->with(['author', 'category'])
            ->orderBy('published_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($post) {
                return [
                    'id' => $post->id,
                    'title' => $post->title,
                    'slug' => $post->slug,
                    'excerpt' => $post->excerpt,
                    'url' => route('blog.show', $post->slug),
                    'author' => $post->author->name,
                    'category' => $post->category->name ?? null,
                    'published_at' => $post->published_at->format('M j, Y'),
                ];
            });

        return response()->json($posts);
    }
}
