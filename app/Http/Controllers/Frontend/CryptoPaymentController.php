<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CryptoPaymentController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
        $this->middleware('auth');
    }

    /**
     * Create crypto payment
     */
    public function create(Request $request, Product $product)
    {
        $request->validate([
            'pay_currency' => 'required|string|in:btc,eth,ltc,usdt,usdc,bnb,ada,dot,sol,matic',
        ]);

        $user = Auth::user();
        $payCurrency = $request->pay_currency;

        // Create crypto payment
        $result = $this->paymentService->createCryptoPayment($product, $user, $payCurrency);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'order_id' => $result['order']->id,
                'payment_details' => $result['payment_details'],
                'redirect_url' => route('payment.crypto.show', $result['order']->id),
            ]);
        } else {
            return response()->json([
                'success' => false,
                'error' => $result['error'],
            ], 400);
        }
    }

    /**
     * Show crypto payment page
     */
    public function show(Order $order)
    {
        // Ensure user owns this order
        if ($order->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to order');
        }

        // Check if order is crypto payment
        if ($order->payment_method !== 'crypto') {
            abort(404, 'Order not found');
        }

        // Get latest payment status
        if ($order->payment_id) {
            $statusResult = $this->paymentService->checkCryptoPaymentStatus($order->payment_id);
            if ($statusResult['success']) {
                $order = $statusResult['order']; // Get updated order
            }
        }

        return view('frontend.payment.crypto', compact('order'));
    }

    /**
     * Check payment status (AJAX)
     */
    public function checkStatus(Order $order)
    {
        // Ensure user owns this order
        if ($order->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if (!$order->payment_id) {
            return response()->json(['error' => 'Payment ID not found'], 404);
        }

        $result = $this->paymentService->checkCryptoPaymentStatus($order->payment_id);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'status' => $result['payment_status']['payment_status'],
                'order_status' => $result['order']->status,
                'payment_details' => $result['payment_status'],
            ]);
        } else {
            return response()->json([
                'success' => false,
                'error' => $result['error'],
            ], 400);
        }
    }

    /**
     * Handle successful payment
     */
    public function success(Order $order)
    {
        // Ensure user owns this order
        if ($order->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to order');
        }

        // Check payment status one more time
        if ($order->payment_id) {
            $this->paymentService->checkCryptoPaymentStatus($order->payment_id);
            $order->refresh();
        }

        return view('frontend.payment.success', compact('order'));
    }

    /**
     * Handle cancelled payment
     */
    public function cancel(Order $order)
    {
        // Ensure user owns this order
        if ($order->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to order');
        }

        // Mark order as cancelled if still pending
        if ($order->status === 'pending') {
            $order->update(['status' => 'cancelled']);
        }

        return view('frontend.payment.cancelled', compact('order'));
    }

    /**
     * Handle IPN callback from NOWPayments
     */
    public function callback(Request $request)
    {
        try {
            // Log the callback for debugging
            Log::info('Crypto payment callback received', [
                'payload' => $request->all(),
                'headers' => $request->headers->all(),
            ]);

            // Verify the callback (you should implement proper IPN verification)
            $paymentId = $request->input('payment_id');
            $paymentStatus = $request->input('payment_status');

            if (!$paymentId) {
                Log::warning('Crypto payment callback missing payment_id');
                return response('Missing payment_id', 400);
            }

            // Find the order
            $order = Order::where('payment_id', $paymentId)->first();

            if (!$order) {
                Log::warning('Crypto payment callback: Order not found', ['payment_id' => $paymentId]);
                return response('Order not found', 404);
            }

            // Update payment status
            $result = $this->paymentService->checkCryptoPaymentStatus($paymentId);

            if ($result['success']) {
                Log::info('Crypto payment status updated', [
                    'order_id' => $order->id,
                    'payment_id' => $paymentId,
                    'status' => $result['payment_status']['payment_status'],
                ]);

                return response('OK', 200);
            } else {
                Log::error('Failed to update crypto payment status', [
                    'order_id' => $order->id,
                    'payment_id' => $paymentId,
                    'error' => $result['error'],
                ]);

                return response('Failed to update status', 500);
            }

        } catch (\Exception $e) {
            Log::error('Crypto payment callback error', [
                'error' => $e->getMessage(),
                'payload' => $request->all(),
            ]);

            return response('Internal server error', 500);
        }
    }

    /**
     * Get available cryptocurrencies (AJAX)
     */
    public function getCurrencies()
    {
        $currencies = $this->paymentService->getAvailableCryptocurrencies();

        return response()->json([
            'success' => true,
            'currencies' => $currencies,
        ]);
    }
}
