<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Download;
use App\Services\DownloadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class DashboardController extends Controller
{
    protected $downloadService;

    public function __construct(DownloadService $downloadService)
    {
        $this->middleware('auth');
        $this->downloadService = $downloadService;
    }

    /**
     * Show user dashboard
     */
    public function index()
    {
        $user = Auth::user();

        // Get user's orders
        $orders = Order::with(['product.category', 'product.primaryImage'])
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get recent downloads
        $recentDownloads = $this->downloadService->getUserDownloadHistory($user, 10);

        // Get user statistics
        $stats = [
            'total_orders' => Order::where('user_id', $user->id)->count(),
            'completed_orders' => Order::where('user_id', $user->id)->where('status', 'completed')->count(),
            'total_spent' => Order::where('user_id', $user->id)->where('status', 'completed')->sum('amount'),
            'total_downloads' => Download::where('user_id', $user->id)->count(),
            'free_products' => Order::where('user_id', $user->id)->where('payment_method', 'free')->count(),
        ];

        return view('frontend.dashboard.index', compact('orders', 'recentDownloads', 'stats'));
    }

    /**
     * Show user's orders
     */
    public function orders(Request $request)
    {
        $user = Auth::user();
        
        $query = Order::with(['product.category', 'product.primaryImage'])
            ->where('user_id', $user->id);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Search by product name
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('product', function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%");
            });
        }

        $orders = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('frontend.dashboard.orders', compact('orders'));
    }

    /**
     * Show user's downloads
     */
    public function downloads(Request $request)
    {
        $user = Auth::user();
        
        $query = Download::with(['product.category', 'order'])
            ->where('user_id', $user->id);

        // Filter by product
        if ($request->filled('product')) {
            $query->where('product_id', $request->product);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('downloaded_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('downloaded_at', '<=', $request->date_to);
        }

        $downloads = $query->orderBy('downloaded_at', 'desc')->paginate(15);

        // Get user's products for filter dropdown
        $userProducts = Order::with('product')
            ->where('user_id', $user->id)
            ->where('status', 'completed')
            ->get()
            ->pluck('product')
            ->unique('id');

        return view('frontend.dashboard.downloads', compact('downloads', 'userProducts'));
    }

    /**
     * Show order details
     */
    public function orderDetails(Order $order)
    {
        // Verify order belongs to current user
        if ($order->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to order.');
        }

        $order->load(['product.category', 'product.primaryImage']);

        // Get download history for this order
        $downloads = Download::where('order_id', $order->id)
            ->orderBy('downloaded_at', 'desc')
            ->get();

        return view('frontend.dashboard.order-details', compact('order', 'downloads'));
    }

    /**
     * Show user profile
     */
    public function profile()
    {
        $user = Auth::user();
        return view('frontend.dashboard.profile', compact('user'));
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        return redirect()->back()->with('success', 'Profile updated successfully.');
    }

    /**
     * Show account settings
     */
    public function settings()
    {
        $user = Auth::user();
        return view('frontend.dashboard.settings', compact('user'));
    }

    /**
     * Update password
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = Auth::user();

        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'Current password is incorrect.']);
        }

        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->back()->with('success', 'Password updated successfully.');
    }

    /**
     * Get dashboard analytics data
     */
    public function analytics()
    {
        $user = Auth::user();

        // Monthly spending data
        $monthlySpending = Order::where('user_id', $user->id)
            ->where('status', 'completed')
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, SUM(amount) as total')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get();

        // Download activity
        $downloadActivity = Download::where('user_id', $user->id)
            ->selectRaw('DATE(downloaded_at) as date, COUNT(*) as count')
            ->where('downloaded_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Category breakdown
        $categoryBreakdown = Order::where('user_id', $user->id)
            ->where('status', 'completed')
            ->with('product.category')
            ->get()
            ->groupBy('product.category.name')
            ->map(function ($orders) {
                return [
                    'count' => $orders->count(),
                    'total' => $orders->sum('amount'),
                ];
            });

        return response()->json([
            'monthly_spending' => $monthlySpending,
            'download_activity' => $downloadActivity,
            'category_breakdown' => $categoryBreakdown,
        ]);
    }
}
