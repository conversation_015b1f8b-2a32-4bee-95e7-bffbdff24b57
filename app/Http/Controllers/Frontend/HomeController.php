<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\User;
use App\Models\Testimonial;
use App\Models\Faq;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{
    /**
     * Display the homepage
     */
    public function index()
    {
        // Get featured products (limit to 12)
        $featuredProducts = Product::with(['category', 'primaryImage'])
            ->where('status', 'published')
            ->where('is_featured', true)
            ->orderBy('created_at', 'desc')
            ->limit(12)
            ->get();

        // Get active categories with product counts (limit to 8)
        $categories = Category::withCount(['products' => function ($query) {
                $query->where('status', 'published');
            }])
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->limit(8)
            ->get();

        // Get statistics for the stats section
        $stats = [
            'total_products' => Product::where('status', 'published')->count(),
            'total_downloads' => Product::sum('download_count'),
            'total_users' => User::where('is_active', true)->count(),
            'total_categories' => Category::where('is_active', true)->count(),
        ];

        // Get testimonials (limit to 6 for homepage)
        $testimonials = Testimonial::active()
            ->featured()
            ->ordered()
            ->limit(6)
            ->get();

        // Get FAQs (limit to 8 for homepage)
        $faqs = Faq::active()
            ->ordered()
            ->limit(8)
            ->get();

        return view('frontend.home', compact('featuredProducts', 'categories', 'stats', 'testimonials', 'faqs'));
    }
}
