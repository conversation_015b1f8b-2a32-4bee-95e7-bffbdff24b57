<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{
    /**
     * Display the homepage
     */
    public function index()
    {
        // Get featured products (limit to 6)
        $featuredProducts = Product::with(['category', 'primaryImage'])
            ->where('status', 'approved')
            ->where('is_featured', true)
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();

        // Get active categories with product counts (limit to 8)
        $categories = Category::withCount(['products' => function ($query) {
                $query->where('status', 'approved');
            }])
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->limit(8)
            ->get();

        // Get statistics for the stats section
        $stats = [
            'total_products' => Product::where('status', 'approved')->count(),
            'total_downloads' => Product::sum('download_count'),
            'total_users' => User::where('is_active', true)->count(),
            'total_categories' => Category::where('is_active', true)->count(),
        ];

        return view('frontend.home', compact('featuredProducts', 'categories', 'stats'));
    }
}
