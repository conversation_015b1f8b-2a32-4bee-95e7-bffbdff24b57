<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Newsletter;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class NewsletterController extends Controller
{
    /**
     * Subscribe to newsletter
     */
    public function subscribe(Request $request)
    {
        $request->validate([
            'email' => 'required|email|max:255',
        ]);

        $email = $request->email;

        // Check if email already exists
        $existing = Newsletter::where('email', $email)->first();

        if ($existing) {
            if ($existing->status === 'active') {
                return response()->json([
                    'success' => false,
                    'message' => 'You are already subscribed to our newsletter!'
                ]);
            } elseif ($existing->status === 'unsubscribed') {
                // Reactivate subscription
                $existing->update([
                    'status' => 'active',
                    'subscribed_at' => now(),
                    'unsubscribed_at' => null,
                    'verification_token' => Str::random(60),
                    'verified_at' => now(), // Auto-verify for simplicity
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Welcome back! Your subscription has been reactivated.'
                ]);
            }
        } else {
            // Create new subscription
            Newsletter::create([
                'email' => $email,
                'status' => 'active',
                'subscribed_at' => now(),
                'verification_token' => Str::random(60),
                'verified_at' => now(), // Auto-verify for simplicity
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Thank you for subscribing! You\'ll receive updates about new products and offers.'
        ]);
    }

    /**
     * Unsubscribe from newsletter
     */
    public function unsubscribe(Request $request)
    {
        $token = $request->get('token');
        
        if (!$token) {
            abort(404);
        }

        $subscription = Newsletter::where('verification_token', $token)->first();

        if (!$subscription) {
            abort(404);
        }

        $subscription->update([
            'status' => 'unsubscribed',
            'unsubscribed_at' => now(),
        ]);

        return view('frontend.newsletter.unsubscribed', compact('subscription'));
    }
}
