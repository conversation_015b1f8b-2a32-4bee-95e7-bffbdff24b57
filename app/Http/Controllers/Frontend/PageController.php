<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Page;
use App\Models\ContactSubmission;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class PageController extends Controller
{
    /**
     * Display the specified page.
     */
    public function show($slug)
    {
        $page = Page::where('slug', $slug)
            ->where('status', 'published')
            ->firstOrFail();

        return view('frontend.pages.show', compact('page'));
    }

    /**
     * Display the contact page.
     */
    public function contact()
    {
        // Try to get contact page from database first
        $page = Page::where('slug', 'contact')
            ->where('status', 'published')
            ->first();

        // Get general settings for social media links and contact info
        $settings = Setting::getGroup('general');

        return view('frontend.pages.contact', compact('page', 'settings'));
    }

    /**
     * Handle contact form submission.
     */
    public function submitContact(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:5000',
            'g-recaptcha-response' => 'nullable', // Add reCAPTCHA validation if needed
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $data = $request->only(['name', 'email', 'subject', 'message']);

            // Store contact submission in database
            $submission = ContactSubmission::create([
                'name' => $data['name'],
                'email' => $data['email'],
                'subject' => $data['subject'],
                'message' => $data['message'],
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'status' => 'new',
            ]);

            // Send email to admin using dynamic SMTP settings
            $this->sendContactEmail($data);
        } catch (\Exception $e) {
            \Log::error('Contact form error: ' . $e->getMessage());

            return back()
                ->with('error', 'Sorry, there was an error sending your message. Please try again later.')
                ->withInput();
        }
    }

    /**
     * Send contact email using dynamic SMTP settings
     */
    private function sendContactEmail($data)
    {
        try {
            // Get email settings from database
            $emailSettings = Setting::getGroup('email');

            // Check if email settings are configured
            $fromAddress = $emailSettings['mail_from_address'] ?? config('mail.from.address');
            if (!$fromAddress || $fromAddress === '<EMAIL>') {
                \Log::info('Contact form email not sent - no valid from address configured');
                return;
            }

            // Configure mail settings dynamically
            $this->configureMailSettings($emailSettings);

            // Send the email
            Mail::send('emails.contact', $data, function ($message) use ($data, $fromAddress) {
                $message->to($fromAddress)
                    ->subject('Contact Form: ' . $data['subject'])
                    ->replyTo($data['email'], $data['name']);
            });

        } catch (\Exception $mailError) {
            \Log::warning('Contact form email failed: ' . $mailError->getMessage());
            // Continue execution - submission is still saved
        }
    }

    /**
     * Configure mail settings dynamically from database
     */
    private function configureMailSettings($settings)
    {
        // Set mail configuration from database settings
        config([
            'mail.default' => $settings['mail_driver'] ?? 'smtp',
            'mail.mailers.smtp.host' => $settings['mail_host'] ?? config('mail.mailers.smtp.host'),
            'mail.mailers.smtp.port' => $settings['mail_port'] ?? config('mail.mailers.smtp.port'),
            'mail.mailers.smtp.encryption' => $settings['mail_encryption'] ?? config('mail.mailers.smtp.encryption'),
            'mail.mailers.smtp.username' => $settings['mail_username'] ?? config('mail.mailers.smtp.username'),
            'mail.mailers.smtp.password' => $settings['mail_password'] ?? config('mail.mailers.smtp.password'),
            'mail.from.address' => $settings['mail_from_address'] ?? config('mail.from.address'),
            'mail.from.name' => $settings['mail_from_name'] ?? config('mail.from.name'),
        ]);

        // Clear the mail manager instance to force reconfiguration
        app()->forgetInstance('mail.manager');
        app()->forgetInstance('mailer');

            return back()->with('success', 'Thank you for your message! We\'ll get back to you soon.');
    }
}
