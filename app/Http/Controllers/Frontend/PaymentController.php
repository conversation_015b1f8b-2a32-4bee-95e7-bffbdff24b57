<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Order;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Show payment page for a product
     */
    public function show(Product $product)
    {
        // Check if product is available for purchase
        if ($product->status !== 'published') {
            abort(404, 'Product not found or not available for purchase.');
        }

        // Check if user already owns this product
        $existingOrder = Order::where('user_id', Auth::id())
            ->where('product_id', $product->id)
            ->where('status', 'completed')
            ->first();

        if ($existingOrder) {
            return redirect()->route('products.download', $product->slug)
                ->with('info', 'You already own this product. You can download it directly.');
        }

        // Handle free products
        if ($product->is_free) {
            $result = $this->paymentService->processFreeProduct($product, Auth::user());
            
            if ($result['success']) {
                return redirect()->route('products.download', $product->slug)
                    ->with('success', 'Free product added to your library!');
            } else {
                return back()->with('error', $result['error']);
            }
        }

        // Get available payment methods
        $paymentMethods = $this->paymentService->getPaymentMethods();
        $enabledMethods = array_filter($paymentMethods, fn($method) => $method['enabled']);

        if (empty($enabledMethods)) {
            return back()->with('error', 'No payment methods are currently available.');
        }

        return view('frontend.payment.show', compact('product', 'enabledMethods'));
    }

    /**
     * Process payment intent creation
     */
    public function createPaymentIntent(Request $request, Product $product)
    {
        $request->validate([
            'payment_method' => 'required|in:stripe,paypal,bank_transfer,crypto',
        ]);

        $paymentMethod = $request->payment_method;

        // Verify payment method is enabled
        if (!$this->paymentService->isPaymentMethodEnabled($paymentMethod)) {
            return response()->json([
                'success' => false,
                'error' => 'Selected payment method is not available.',
            ], 400);
        }

        // Check if product is available
        if ($product->status !== 'published' || $product->is_free) {
            return response()->json([
                'success' => false,
                'error' => 'Product is not available for purchase.',
            ], 400);
        }

        // Check if user already owns this product
        $existingOrder = Order::where('user_id', Auth::id())
            ->where('product_id', $product->id)
            ->where('status', 'completed')
            ->first();

        if ($existingOrder) {
            return response()->json([
                'success' => false,
                'error' => 'You already own this product.',
            ], 400);
        }

        try {
            DB::beginTransaction();

            $result = match ($paymentMethod) {
                'stripe' => $this->paymentService->createStripePaymentIntent($product, Auth::user()),
                'paypal' => $this->paymentService->createPayPalPayment($product, Auth::user()),
                'bank_transfer' => $this->paymentService->createBankTransferOrder($product, Auth::user()),
                'crypto' => $this->paymentService->createCryptoPayment($product, Auth::user(), $request->input('pay_currency', 'btc')),
                default => ['success' => false, 'error' => 'Unsupported payment method'],
            };

            if ($result['success']) {
                DB::commit();
                return response()->json($result);
            } else {
                DB::rollBack();
                return response()->json($result, 400);
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payment intent creation failed', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'user_id' => Auth::id(),
                'payment_method' => $paymentMethod,
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Payment processing failed. Please try again.',
            ], 500);
        }
    }

    /**
     * Confirm Stripe payment
     */
    public function confirmPayment(Request $request)
    {
        $request->validate([
            'payment_intent_id' => 'required|string',
        ]);

        try {
            $result = $this->paymentService->confirmStripePayment($request->payment_intent_id);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'redirect_url' => route('payment.success', $result['order']->id),
                ]);
            } else {
                return response()->json($result, 400);
            }

        } catch (\Exception $e) {
            Log::error('Payment confirmation failed', [
                'error' => $e->getMessage(),
                'payment_intent_id' => $request->payment_intent_id,
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Payment confirmation failed. Please contact support.',
            ], 500);
        }
    }

    /**
     * Show payment success page
     */
    public function success(Order $order)
    {
        // Verify order belongs to current user
        if ($order->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to order.');
        }

        // Load relationships
        $order->load(['product', 'user']);

        return view('frontend.payment.success', compact('order'));
    }

    /**
     * Show payment failure page
     */
    public function failed(Request $request)
    {
        $error = $request->get('error', 'Payment was unsuccessful.');
        return view('frontend.payment.failed', compact('error'));
    }

    /**
     * Handle bank transfer confirmation
     */
    public function bankTransferConfirmation(Order $order)
    {
        // Verify order belongs to current user
        if ($order->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to order.');
        }

        // Verify order is bank transfer and pending
        if ($order->payment_method !== 'bank_transfer' || $order->status !== 'pending') {
            return redirect()->route('products.index')
                ->with('error', 'Invalid order for bank transfer confirmation.');
        }

        $order->load(['product']);

        // Get bank transfer details from database settings
        $paymentSettings = \App\Models\Setting::getGroup('payment');
        $bankDetails = [
            'enabled' => $paymentSettings['bank_transfer_enabled'] ?? false,
            'account_name' => $paymentSettings['bank_account_name'] ?? '',
            'account_number' => $paymentSettings['bank_account_number'] ?? '',
            'bank_name' => $paymentSettings['bank_name'] ?? '',
            'bank_code' => $paymentSettings['bank_code'] ?? '',
            'swift_code' => $paymentSettings['bank_swift_code'] ?? '',
            'iban' => $paymentSettings['bank_iban'] ?? '',
            'instructions' => $paymentSettings['bank_transfer_instructions'] ?? '',
        ];

        return view('frontend.payment.bank-transfer', compact('order', 'bankDetails'));
    }

    /**
     * Webhook handler for Stripe
     */
    public function stripeWebhook(Request $request)
    {
        $payload = $request->getContent();
        $sig_header = $request->header('Stripe-Signature');
        $endpoint_secret = config('services.stripe.webhook_secret');

        try {
            $event = \Stripe\Webhook::constructEvent($payload, $sig_header, $endpoint_secret);

            // Handle the event
            switch ($event['type']) {
                case 'payment_intent.succeeded':
                    $paymentIntent = $event['data']['object'];
                    $this->handleSuccessfulPayment($paymentIntent);
                    break;

                case 'payment_intent.payment_failed':
                    $paymentIntent = $event['data']['object'];
                    $this->handleFailedPayment($paymentIntent);
                    break;

                default:
                    Log::info('Unhandled Stripe webhook event', ['type' => $event['type']]);
            }

            return response()->json(['status' => 'success']);

        } catch (\UnexpectedValueException $e) {
            Log::error('Invalid Stripe webhook payload', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Invalid payload'], 400);

        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            Log::error('Invalid Stripe webhook signature', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Invalid signature'], 400);

        } catch (\Exception $e) {
            Log::error('Stripe webhook processing failed', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Handle successful payment from webhook
     */
    private function handleSuccessfulPayment($paymentIntent)
    {
        $order = Order::where('payment_id', $paymentIntent['id'])->first();

        if ($order && $order->status === 'pending') {
            $order->markAsCompleted();
            
            Log::info('Payment confirmed via webhook', [
                'order_id' => $order->id,
                'payment_intent_id' => $paymentIntent['id'],
            ]);
        }
    }

    /**
     * Handle failed payment from webhook
     */
    private function handleFailedPayment($paymentIntent)
    {
        $order = Order::where('payment_id', $paymentIntent['id'])->first();

        if ($order && $order->status === 'pending') {
            $order->markAsFailed();

            Log::info('Payment failed via webhook', [
                'order_id' => $order->id,
                'payment_intent_id' => $paymentIntent['id'],
            ]);
        }
    }

    /**
     * Handle PayPal payment success
     */
    public function paypalSuccess(Request $request, Order $order)
    {
        // Verify order belongs to current user
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        // Verify order is pending and uses PayPal
        if ($order->status !== 'pending' || $order->payment_method !== 'paypal') {
            return redirect()->route('dashboard')->with('error', 'Invalid order.');
        }

        $paymentId = $request->get('paymentId');
        $payerId = $request->get('PayerID');

        if (!$paymentId || !$payerId) {
            return redirect()->route('products.show', $order->product->slug)
                ->with('error', 'PayPal payment was not completed.');
        }

        // Execute PayPal payment
        $result = $this->paymentService->executePayPalPayment($paymentId, $payerId, $order);

        if ($result['success']) {
            return redirect()->route('payment.success', $order->id)
                ->with('success', 'Payment completed successfully!');
        } else {
            return redirect()->route('products.show', $order->product->slug)
                ->with('error', $result['error'] ?? 'PayPal payment failed.');
        }
    }

    /**
     * Handle PayPal payment cancellation
     */
    public function paypalCancel(Order $order)
    {
        // Verify order belongs to current user
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        // Update order status to cancelled
        $order->update([
            'status' => 'cancelled',
            'notes' => 'Payment cancelled by user via PayPal',
        ]);

        return redirect()->route('products.show', $order->product->slug)
            ->with('info', 'Payment was cancelled. You can try again anytime.');
    }
}
