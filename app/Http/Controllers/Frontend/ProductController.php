<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\Order;
use App\Models\Comment;
use App\Services\SearchService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    protected $searchService;

    public function __construct(SearchService $searchService)
    {
        $this->searchService = $searchService;
    }

    /**
     * Display a listing of products
     */
    public function index(Request $request)
    {
        // Use the SearchService for advanced search and filtering
        $products = $this->searchService->searchProducts($request);

        // Get categories for filter dropdown
        $categories = Category::where('is_active', true)
            ->withCount(['products' => function ($query) {
                $query->where('status', 'published');
            }])
            ->orderBy('name')
            ->get();

        // Get all unique tags for tag filter
        $allTags = Product::where('status', 'published')
            ->whereNotNull('tags')
            ->pluck('tags')
            ->flatten()
            ->unique()
            ->sort()
            ->values();

        // Get price range for price filter
        $priceRange = Product::where('status', 'published')
            ->where('is_free', false)
            ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
            ->first();

        // Get category for breadcrumb if filtering by category
        $category = null;
        if ($request->filled('category')) {
            if (is_numeric($request->get('category'))) {
                $category = Category::find($request->get('category'));
            } else {
                $category = Category::where('slug', $request->get('category'))->first();
            }
        }

        // Get filter counts for UI
        $filterCounts = [
            'total' => Product::where('status', 'published')->count(),
            'free' => Product::where('status', 'published')->where('is_free', true)->count(),
            'paid' => Product::where('status', 'published')->where('is_free', false)->count(),
            'featured' => Product::where('status', 'published')->where('is_featured', true)->count(),
            'trending' => Product::where('status', 'published')->where('is_trending', true)->count(),
        ];

        return view('frontend.products.index', compact(
            'products',
            'categories',
            'category',
            'allTags',
            'priceRange',
            'filterCounts'
        ));
    }

    /**
     * Display the specified product
     */
    public function show($slug)
    {
        $product = Product::with(['category', 'images', 'approvedReviews.user'])
            ->where('slug', $slug)
            ->where('status', 'published')
            ->firstOrFail();

        // Increment view count
        $product->increment('view_count');

        // Get related products from the same category
        $relatedProducts = Product::with(['category', 'primaryImage'])
            ->where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->where('status', 'published')
            ->limit(4)
            ->get();

        // Check if current user can leave a review
        $canReview = false;
        $hasReviewed = false;

        if (auth()->check()) {
            // Check if user has purchased this product
            $hasPurchased = Order::where('user_id', auth()->id())
                ->where('product_id', $product->id)
                ->where('status', 'completed')
                ->exists();

            $canReview = $hasPurchased || $product->is_free;

            // Allow multiple reviews - set hasReviewed to false
            $hasReviewed = false;
        }

        return view('frontend.products.show', compact('product', 'relatedProducts', 'canReview', 'hasReviewed'));
    }

    /**
     * Handle product search
     */
    public function search(Request $request)
    {
        return $this->index($request);
    }

    /**
     * Get enhanced search suggestions for autocomplete
     */
    public function suggestions(Request $request)
    {
        $query = trim($request->get('q', ''));

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $suggestions = [];

        // Product suggestions with enhanced data
        $products = Product::with(['category', 'primaryImage'])
            ->where('status', 'published')
            ->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('short_description', 'like', "%{$query}%");
            })
            ->select('id', 'title', 'slug', 'short_description', 'price', 'is_free', 'category_id')
            ->limit(5)
            ->get();

        foreach ($products as $product) {
            $suggestions[] = [
                'type' => 'product',
                'title' => $product->title,
                'subtitle' => $product->category->name ?? '',
                'description' => $product->short_description ? \Illuminate\Support\Str::limit($product->short_description, 60) : '',
                'price' => $product->is_free ? 'Free' : '$' . number_format($product->price, 2),
                'url' => route('products.show', $product->slug),
                'image' => $product->primaryImage ? Storage::url($product->primaryImage->image_path) : null,
                'icon' => 'fas fa-box'
            ];
        }

        // Category suggestions
        $categories = Category::where('is_active', true)
            ->where('name', 'like', "%{$query}%")
            ->withCount(['products' => function ($q) {
                $q->where('status', 'published');
            }])
            ->select('name', 'slug', 'description')
            ->limit(3)
            ->get();

        foreach ($categories as $category) {
            $suggestions[] = [
                'type' => 'category',
                'title' => $category->name,
                'subtitle' => $category->products_count . ' products',
                'description' => $category->description ? \Illuminate\Support\Str::limit($category->description, 60) : '',
                'url' => route('products.index', ['category' => $category->slug]),
                'icon' => 'fas fa-folder'
            ];
        }

        // Popular search terms (tags)
        $tags = Product::where('status', 'published')
            ->whereNotNull('tags')
            ->get()
            ->pluck('tags')
            ->flatten()
            ->filter(function ($tag) use ($query) {
                return stripos($tag, $query) !== false;
            })
            ->unique()
            ->take(3);

        foreach ($tags as $tag) {
            $suggestions[] = [
                'type' => 'tag',
                'title' => $tag,
                'subtitle' => 'Search tag',
                'url' => route('products.index', ['q' => $tag]),
                'icon' => 'fas fa-tag'
            ];
        }

        // Quick filters
        $quickFilters = [
            'free' => 'Free Products',
            'featured' => 'Featured Products',
            'trending' => 'Trending Products'
        ];

        foreach ($quickFilters as $filter => $label) {
            if (stripos($label, $query) !== false) {
                $suggestions[] = [
                    'type' => 'filter',
                    'title' => $label,
                    'subtitle' => 'Quick filter',
                    'url' => route('products.index', [$filter => '1']),
                    'icon' => 'fas fa-filter'
                ];
            }
        }

        return response()->json(array_slice($suggestions, 0, 10));
    }

    /**
     * Get filter options for advanced filtering
     */
    public function filterOptions(Request $request)
    {
        $category = $request->get('category');

        $query = Product::where('status', 'published');

        if ($category) {
            if (is_numeric($category)) {
                $query->where('category_id', $category);
            } else {
                $categoryModel = Category::where('slug', $category)->first();
                if ($categoryModel) {
                    $query->where('category_id', $categoryModel->id);
                }
            }
        }

        // Get price range for the filtered products
        $priceRange = $query->where('is_free', false)
            ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
            ->first();

        // Get available tags for the filtered products
        $tags = $query->whereNotNull('tags')
            ->pluck('tags')
            ->flatten()
            ->unique()
            ->sort()
            ->values()
            ->take(20);

        // Get product counts by price ranges
        $priceCounts = [
            'free' => (clone $query)->where('is_free', true)->count(),
            'under_10' => (clone $query)->where('is_free', false)->where('price', '<', 10)->count(),
            '10_to_25' => (clone $query)->where('is_free', false)->whereBetween('price', [10, 25])->count(),
            '25_to_50' => (clone $query)->where('is_free', false)->whereBetween('price', [25, 50])->count(),
            'over_50' => (clone $query)->where('is_free', false)->where('price', '>', 50)->count(),
        ];

        return response()->json([
            'price_range' => $priceRange,
            'tags' => $tags,
            'price_counts' => $priceCounts
        ]);
    }

    /**
     * Handle product download
     */
    public function download($slug)
    {
        $product = Product::where('slug', $slug)
            ->where('status', 'published')
            ->firstOrFail();

        // Check if user is authenticated
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Please login to download products.');
        }

        // Check if user owns this product (has completed order)
        $order = Order::where('user_id', auth()->id())
            ->where('product_id', $product->id)
            ->where('status', 'completed')
            ->first();

        if (!$order && !$product->is_free) {
            return redirect()->route('products.show', $slug)
                ->with('error', 'You need to purchase this product before downloading.');
        }

        // For free products, create a free order if it doesn't exist
        if ($product->is_free && !$order) {
            $order = Order::create([
                'user_id' => auth()->id(),
                'product_id' => $product->id,
                'amount' => 0,
                'currency' => config('templatescave.payments.currency', 'usd'),
                'status' => 'completed',
                'payment_method' => 'free',
                'paid_at' => now(),
            ]);
        }

        // Check download limits and tracking
        $downloadService = app(\App\Services\DownloadService::class);
        $downloadResult = $downloadService->processDownload($product, auth()->user(), $order);

        if (!$downloadResult['success']) {
            return redirect()->back()->with('error', $downloadResult['error']);
        }

        // Return the download response
        return $downloadResult['response'];
    }

    /**
     * Handle product purchase
     */
    public function purchase($slug)
    {
        $product = Product::where('slug', $slug)
            ->where('status', 'published')
            ->firstOrFail();

        // Check if user is authenticated
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Please login to purchase products.');
        }

        // Redirect to payment page
        return redirect()->route('payment.show', $product->slug);
    }

    /**
     * Store a review for a product
     */
    public function storeReview(Request $request, $slug)
    {
        $product = Product::where('slug', $slug)
            ->where('status', 'published')
            ->firstOrFail();

        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string|max:1000',
            'rating' => 'required|integer|min:1|max:5',
        ]);

        // Check if user has purchased this product
        $hasPurchased = Order::where('user_id', auth()->id())
            ->where('product_id', $product->id)
            ->where('status', 'completed')
            ->exists();

        if (!$hasPurchased && !$product->is_free) {
            return back()->with('error', 'You must purchase this product before leaving a review.');
        }

        // Allow multiple reviews from the same user

        // Create the review
        Comment::create([
            'commentable_type' => Product::class,
            'commentable_id' => $product->id,
            'user_id' => auth()->id(),
            'title' => $request->title,
            'content' => $request->content,
            'rating' => $request->rating,
            'is_review' => true,
            'status' => 'pending', // Reviews need approval
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return back()->with('success', 'Thank you for your review! It will be published after approval.');
    }
}

