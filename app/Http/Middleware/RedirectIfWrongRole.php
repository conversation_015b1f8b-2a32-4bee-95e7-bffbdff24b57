<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfWrongRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Check if user is active
        if (!$user->is_active) {
            auth()->logout();
            return redirect()->route('login')->withErrors(['email' => 'Your account has been deactivated. Please contact support.']);
        }

        // Redirect users to their appropriate dashboard if they're accessing the wrong one
        $currentRoute = $request->route()->getName();

        if ($user->role === 'admin' && !str_starts_with($currentRoute, 'admin.')) {
            return redirect()->route('admin.dashboard');
        }

        if ($user->role === 'vendor' && !str_starts_with($currentRoute, 'vendor.')) {
            return redirect()->route('vendor.dashboard');
        }

        if ($user->role === 'member' && (str_starts_with($currentRoute, 'admin.') || str_starts_with($currentRoute, 'vendor.'))) {
            return redirect()->route('dashboard');
        }

        return $next($request);
    }
}
