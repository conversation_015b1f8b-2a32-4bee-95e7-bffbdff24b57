<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SEOMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Add security headers
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        // Add cache headers for static assets
        if ($request->is('css/*') || $request->is('js/*') || $request->is('images/*')) {
            $response->headers->set('Cache-Control', 'public, max-age=31536000'); // 1 year
        }

        // Add canonical URL header
        if (method_exists($response, 'getContent') && $response->getContent()) {
            $content = $response->getContent();

            // Add preload hints for critical resources
            $preloadHints = [
                '</css/app.css>; rel=preload; as=style',
                '</js/app.js>; rel=preload; as=script',
                '</fonts/inter.woff2>; rel=preload; as=font; type=font/woff2; crossorigin',
            ];

            $response->headers->set('Link', implode(', ', $preloadHints));
        }

        return $response;
    }
}
