<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BlogComment extends Model
{
    use HasFactory;

    protected $fillable = [
        'blog_post_id',
        'user_id',
        'parent_id',
        'author_name',
        'author_email',
        'author_website',
        'content',
        'status',
        'ip_address',
        'user_agent',
        'like_count',
        'dislike_count',
        'is_pinned',
        'approved_at',
        'approved_by',
    ];

    protected function casts(): array
    {
        return [
            'like_count' => 'integer',
            'dislike_count' => 'integer',
            'is_pinned' => 'boolean',
            'approved_at' => 'datetime',
        ];
    }

    /**
     * Relationships
     */
    public function post()
    {
        return $this->belongsTo(BlogPost::class, 'blog_post_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function parent()
    {
        return $this->belongsTo(BlogComment::class, 'parent_id');
    }

    public function replies()
    {
        return $this->hasMany(BlogComment::class, 'parent_id');
    }

    public function approvedReplies()
    {
        return $this->hasMany(BlogComment::class, 'parent_id')->where('status', 'approved');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scopes
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopeSpam($query)
    {
        return $query->where('status', 'spam');
    }

    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    public function scopeReplies($query)
    {
        return $query->whereNotNull('parent_id');
    }

    public function scopePinned($query)
    {
        return $query->where('is_pinned', true);
    }

    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    public function scopeOldest($query)
    {
        return $query->orderBy('created_at', 'asc');
    }

    /**
     * Accessors
     */
    public function getAuthorDisplayNameAttribute()
    {
        if ($this->user) {
            return $this->user->name;
        }

        return $this->author_name ?: 'Anonymous';
    }

    public function getAuthorAvatarAttribute()
    {
        if ($this->user && $this->user->avatar) {
            return $this->user->avatar_url;
        }

        // Generate Gravatar URL for guest comments
        if ($this->author_email) {
            $hash = md5(strtolower(trim($this->author_email)));
            return "https://www.gravatar.com/avatar/{$hash}?d=identicon&s=40";
        }

        return asset('images/default-avatar.png');
    }

    public function getIsGuestAttribute()
    {
        return is_null($this->user_id);
    }

    public function getCanReplyAttribute()
    {
        return $this->status === 'approved' && $this->post->allow_comments;
    }

    /**
     * Methods
     */
    public function isApproved()
    {
        return $this->status === 'approved';
    }

    public function isPending()
    {
        return $this->status === 'pending';
    }

    public function isRejected()
    {
        return $this->status === 'rejected';
    }

    public function isSpam()
    {
        return $this->status === 'spam';
    }

    public function approve($approvedBy = null)
    {
        $this->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => $approvedBy,
        ]);

        // Update post comment count
        $this->post->increment('comment_count');
    }

    public function reject()
    {
        $this->update(['status' => 'rejected']);
    }

    public function markAsSpam()
    {
        $this->update(['status' => 'spam']);
    }

    public function pin()
    {
        $this->update(['is_pinned' => true]);
    }

    public function unpin()
    {
        $this->update(['is_pinned' => false]);
    }

    public function incrementLikes()
    {
        $this->increment('like_count');
    }

    public function incrementDislikes()
    {
        $this->increment('dislike_count');
    }

    public function getDepthLevel()
    {
        $depth = 0;
        $parent = $this->parent;
        
        while ($parent) {
            $depth++;
            $parent = $parent->parent;
        }
        
        return $depth;
    }
}
