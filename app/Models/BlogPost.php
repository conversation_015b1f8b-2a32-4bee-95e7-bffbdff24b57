<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BlogPost extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'excerpt',
        'content',
        'featured_image',
        'gallery_images',
        'author_id',
        'blog_category_id',
        'status',
        'is_featured',
        'allow_comments',
        'tags',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'canonical_url',
        'social_meta',
        'view_count',
        'comment_count',
        'like_count',
        'published_at',
    ];

    protected function casts(): array
    {
        return [
            'gallery_images' => 'array',
            'tags' => 'array',
            'meta_keywords' => 'array',
            'social_meta' => 'array',
            'is_featured' => 'boolean',
            'allow_comments' => 'boolean',
            'view_count' => 'integer',
            'comment_count' => 'integer',
            'like_count' => 'integer',
            'published_at' => 'datetime',
        ];
    }

    /**
     * Relationships
     */
    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    public function category()
    {
        return $this->belongsTo(BlogCategory::class, 'blog_category_id');
    }

    public function comments()
    {
        return $this->hasMany(BlogComment::class);
    }

    public function approvedComments()
    {
        return $this->hasMany(BlogComment::class)->where('status', 'approved');
    }

    public function pendingComments()
    {
        return $this->hasMany(BlogComment::class)->where('status', 'pending');
    }

    /**
     * Scopes
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published')->whereNotNull('published_at');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeArchived($query)
    {
        return $query->where('status', 'archived');
    }

    public function scopeLatest($query)
    {
        return $query->orderBy('published_at', 'desc');
    }

    public function scopeMostViewed($query)
    {
        return $query->orderBy('view_count', 'desc');
    }

    /**
     * Accessors
     */
    public function getFeaturedImageUrlAttribute()
    {
        if ($this->featured_image) {
            return \Storage::url('blog/featured/' . $this->featured_image);
        }

        return asset('images/placeholder-blog.svg');
    }

    public function getExcerptAttribute($value)
    {
        if ($value) {
            return $value;
        }

        return \Str::limit(strip_tags($this->content), 150);
    }

    public function getReadingTimeAttribute()
    {
        $wordCount = str_word_count(strip_tags($this->content));
        $readingTime = ceil($wordCount / 200); // Average reading speed: 200 words per minute

        return $readingTime . ' min read';
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Methods
     */
    public function incrementViewCount()
    {
        $this->increment('view_count');
    }

    public function isPublished()
    {
        return $this->status === 'published' && $this->published_at && $this->published_at->isPast();
    }

    public function isDraft()
    {
        return $this->status === 'draft';
    }

    public function isArchived()
    {
        return $this->status === 'archived';
    }
}
