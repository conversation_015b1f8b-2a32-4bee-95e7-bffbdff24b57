<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Lara<PERSON>\Socialite\Facades\Socialite;
use SocialiteProviders\PayPal\Provider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register PayPal OAuth provider
        Socialite::extend('paypal', function ($app) {
            $config = $app['config']['services.paypal'];
            return Socialite::buildProvider(Provider::class, $config);
        });

        // Share settings with all views
        view()->composer('*', function ($view) {
            $view->with('settings', [
                'site_name' => \App\Models\Setting::get('site_name', config('templatescave.site.name')),
                'site_tagline' => \App\Models\Setting::get('site_tagline', config('templatescave.site.tagline')),
                'site_description' => \App\Models\Setting::get('site_description', config('templatescave.site.description')),
                'contact_email' => \App\Models\Setting::get('contact_email', config('templatescave.email.admin_email')),
                'support_email' => \App\Models\Setting::get('support_email', config('templatescave.email.support_email')),
                'facebook_url' => \App\Models\Setting::get('facebook_url', ''),
                'twitter_url' => \App\Models\Setting::get('twitter_url', ''),
                'linkedin_url' => \App\Models\Setting::get('linkedin_url', ''),
                'youtube_url' => \App\Models\Setting::get('youtube_url', ''),
                'instagram_url' => \App\Models\Setting::get('instagram_url', ''),
                'meta_title_suffix' => \App\Models\Setting::get('meta_title_suffix', config('templatescave.seo.meta_title_suffix')),
                'meta_description_default' => \App\Models\Setting::get('meta_description_default', config('templatescave.seo.meta_description_default')),
                'google_analytics_id' => \App\Models\Setting::get('google_analytics_id', ''),
                'google_analytics_enabled' => \App\Models\Setting::get('google_analytics_enabled', false),
                'currency_symbol' => \App\Models\Setting::get('currency_symbol', '$'),
            ]);
        });
    }
}
