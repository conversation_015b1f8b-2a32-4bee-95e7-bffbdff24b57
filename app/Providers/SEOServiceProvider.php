<?php

namespace App\Providers;

use App\Services\SEOService;
use App\Services\PerformanceService;
use App\Services\AnalyticsService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Blade;

class SEOServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(SEOService::class, function ($app) {
            return new SEOService();
        });

        $this->app->singleton(PerformanceService::class, function ($app) {
            return new PerformanceService();
        });

        $this->app->singleton(AnalyticsService::class, function ($app) {
            return new AnalyticsService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share SEO service with all views
        View::composer('*', function ($view) {
            $view->with('seoService', app(SEOService::class));
            $view->with('analyticsService', app(AnalyticsService::class));
        });

        // Register Blade directives for SEO
        Blade::directive('seo', function ($expression) {
            return "<?php echo app('App\\Services\\SEOService')->generateMetaTags($expression); ?>";
        });

        Blade::directive('analytics', function () {
            return "<?php echo app('App\\Services\\AnalyticsService')->getTrackingCode(); ?>";
        });

        // Performance tracking middleware
        $this->app['router']->pushMiddlewareToGroup('web', \App\Http\Middleware\SEOMiddleware::class);
    }
}
