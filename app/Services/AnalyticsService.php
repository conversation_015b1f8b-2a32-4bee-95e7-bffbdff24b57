<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AnalyticsService
{
    protected $trackingId;
    protected $measurementId;

    public function __construct()
    {
        $this->trackingId = config('services.google_analytics.tracking_id');
        $this->measurementId = config('services.google_analytics.measurement_id');
    }

    /**
     * Track page view
     */
    public function trackPageView($page, $title = null, $userId = null)
    {
        if (!$this->trackingId) {
            return false;
        }

        $data = [
            'page_title' => $title ?: $page,
            'page_location' => request()->fullUrl(),
            'page_referrer' => request()->header('referer'),
            'user_id' => $userId,
            'session_id' => session()->getId(),
            'timestamp' => now(),
        ];

        // Store locally for backup analytics
        $this->storeLocalAnalytics('page_view', $data);

        return true;
    }

    /**
     * Track event
     */
    public function trackEvent($action, $category = 'general', $label = null, $value = null, $userId = null)
    {
        if (!$this->trackingId) {
            return false;
        }

        $data = [
            'event_category' => $category,
            'event_action' => $action,
            'event_label' => $label,
            'event_value' => $value,
            'user_id' => $userId,
            'session_id' => session()->getId(),
            'timestamp' => now(),
        ];

        // Store locally for backup analytics
        $this->storeLocalAnalytics('event', $data);

        return true;
    }

    /**
     * Track product view
     */
    public function trackProductView($product, $userId = null)
    {
        $this->trackEvent('view_item', 'ecommerce', $product->name, $product->price, $userId);
        
        // Store product-specific analytics
        $this->storeLocalAnalytics('product_view', [
            'product_id' => $product->id,
            'product_name' => $product->name,
            'product_category' => $product->category->name ?? 'Uncategorized',
            'product_price' => $product->price,
            'user_id' => $userId,
            'timestamp' => now(),
        ]);

        // Increment view count
        $product->increment('view_count');
    }

    /**
     * Track download
     */
    public function trackDownload($product, $user, $orderId = null)
    {
        $this->trackEvent('download', 'ecommerce', $product->name, $product->price, $user->id);
        
        $this->storeLocalAnalytics('download', [
            'product_id' => $product->id,
            'product_name' => $product->name,
            'product_category' => $product->category->name ?? 'Uncategorized',
            'product_price' => $product->price,
            'user_id' => $user->id,
            'order_id' => $orderId,
            'timestamp' => now(),
        ]);

        // Increment download count
        $product->increment('download_count');
    }

    /**
     * Track purchase
     */
    public function trackPurchase($order)
    {
        $this->trackEvent('purchase', 'ecommerce', "Order #{$order->id}", $order->total, $order->user_id);
        
        $items = $order->items->map(function ($item) {
            return [
                'item_id' => $item->product_id,
                'item_name' => $item->product_name,
                'item_category' => $item->product->category->name ?? 'Uncategorized',
                'price' => $item->price,
                'quantity' => $item->quantity,
            ];
        });

        $this->storeLocalAnalytics('purchase', [
            'order_id' => $order->id,
            'transaction_id' => $order->transaction_id,
            'total' => $order->total,
            'tax' => $order->tax_amount,
            'shipping' => $order->shipping_amount,
            'currency' => 'USD',
            'user_id' => $order->user_id,
            'items' => $items,
            'timestamp' => now(),
        ]);
    }

    /**
     * Track search
     */
    public function trackSearch($query, $category = null, $resultsCount = 0, $userId = null)
    {
        $this->trackEvent('search', 'site_search', $query, $resultsCount, $userId);
        
        $this->storeLocalAnalytics('search', [
            'query' => $query,
            'category' => $category,
            'results_count' => $resultsCount,
            'user_id' => $userId,
            'timestamp' => now(),
        ]);
    }

    /**
     * Track blog post view
     */
    public function trackBlogView($post, $userId = null)
    {
        $this->trackEvent('view_blog_post', 'content', $post->title, null, $userId);
        
        $this->storeLocalAnalytics('blog_view', [
            'post_id' => $post->id,
            'post_title' => $post->title,
            'post_category' => $post->category->name ?? 'Uncategorized',
            'author_id' => $post->author_id,
            'user_id' => $userId,
            'timestamp' => now(),
        ]);

        // Increment view count
        $post->increment('view_count');
    }

    /**
     * Store analytics data locally
     */
    private function storeLocalAnalytics($type, $data)
    {
        $cacheKey = 'analytics_' . $type . '_' . date('Y-m-d');
        $existing = Cache::get($cacheKey, []);
        $existing[] = $data;
        
        // Keep only last 1000 entries per day
        if (count($existing) > 1000) {
            $existing = array_slice($existing, -1000);
        }
        
        Cache::put($cacheKey, $existing, 86400); // 24 hours
    }

    /**
     * Get analytics data
     */
    public function getAnalyticsData($type, $days = 7)
    {
        $data = [];
        
        for ($i = 0; $i < $days; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $cacheKey = 'analytics_' . $type . '_' . $date;
            $dayData = Cache::get($cacheKey, []);
            
            if (!empty($dayData)) {
                $data[$date] = $dayData;
            }
        }
        
        return $data;
    }

    /**
     * Get popular products
     */
    public function getPopularProducts($days = 30, $limit = 10)
    {
        return Cache::remember("popular_products_{$days}d", 3600, function () use ($days, $limit) {
            $startDate = now()->subDays($days);
            
            return \App\Models\Product::where('created_at', '>=', $startDate)
                ->where('status', 'published')
                ->orderBy('view_count', 'desc')
                ->orderBy('download_count', 'desc')
                ->limit($limit)
                ->with(['category', 'images'])
                ->get();
        });
    }

    /**
     * Get search analytics
     */
    public function getSearchAnalytics($days = 7)
    {
        $searchData = $this->getAnalyticsData('search', $days);
        $allSearches = collect($searchData)->flatten(1);
        
        return [
            'total_searches' => $allSearches->count(),
            'unique_queries' => $allSearches->pluck('query')->unique()->count(),
            'top_queries' => $allSearches->groupBy('query')
                ->map(function ($searches) {
                    return [
                        'query' => $searches->first()['query'],
                        'count' => $searches->count(),
                        'avg_results' => $searches->avg('results_count'),
                    ];
                })
                ->sortByDesc('count')
                ->take(10)
                ->values(),
            'zero_result_queries' => $allSearches->where('results_count', 0)
                ->pluck('query')
                ->unique()
                ->take(10)
                ->values(),
        ];
    }

    /**
     * Get user behavior analytics
     */
    public function getUserBehaviorAnalytics($days = 7)
    {
        $pageViews = $this->getAnalyticsData('page_view', $days);
        $events = $this->getAnalyticsData('event', $days);
        
        $allPageViews = collect($pageViews)->flatten(1);
        $allEvents = collect($events)->flatten(1);
        
        return [
            'total_page_views' => $allPageViews->count(),
            'unique_visitors' => $allPageViews->pluck('session_id')->unique()->count(),
            'top_pages' => $allPageViews->groupBy('page_location')
                ->map(function ($views) {
                    return [
                        'page' => $views->first()['page_location'],
                        'views' => $views->count(),
                        'unique_visitors' => $views->pluck('session_id')->unique()->count(),
                    ];
                })
                ->sortByDesc('views')
                ->take(10)
                ->values(),
            'top_events' => $allEvents->groupBy('event_action')
                ->map(function ($events) {
                    return [
                        'action' => $events->first()['event_action'],
                        'count' => $events->count(),
                        'category' => $events->first()['event_category'],
                    ];
                })
                ->sortByDesc('count')
                ->take(10)
                ->values(),
        ];
    }

    /**
     * Generate analytics report
     */
    public function generateAnalyticsReport($days = 7)
    {
        return [
            'period' => "{$days} days",
            'generated_at' => now(),
            'user_behavior' => $this->getUserBehaviorAnalytics($days),
            'search_analytics' => $this->getSearchAnalytics($days),
            'popular_products' => $this->getPopularProducts($days),
            'conversion_metrics' => $this->getConversionMetrics($days),
        ];
    }

    /**
     * Get conversion metrics
     */
    public function getConversionMetrics($days = 7)
    {
        $startDate = now()->subDays($days);
        
        $totalVisitors = \App\Models\User::where('created_at', '>=', $startDate)->count();
        $totalOrders = \App\Models\Order::where('created_at', '>=', $startDate)->count();
        $totalRevenue = \App\Models\Order::where('created_at', '>=', $startDate)
            ->where('status', 'completed')
            ->sum('total');
        
        return [
            'total_visitors' => $totalVisitors,
            'total_orders' => $totalOrders,
            'conversion_rate' => $totalVisitors > 0 ? round(($totalOrders / $totalVisitors) * 100, 2) : 0,
            'total_revenue' => $totalRevenue,
            'average_order_value' => $totalOrders > 0 ? round($totalRevenue / $totalOrders, 2) : 0,
        ];
    }

    /**
     * Get Google Analytics tracking code
     */
    public function getTrackingCode()
    {
        if (!$this->measurementId) {
            return '';
        }

        return "
        <!-- Google tag (gtag.js) -->
        <script async src=\"https://www.googletagmanager.com/gtag/js?id={$this->measurementId}\"></script>
        <script>
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '{$this->measurementId}');
        </script>
        ";
    }
}
