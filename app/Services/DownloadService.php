<?php

namespace App\Services;

use App\Models\Product;
use App\Models\User;
use App\Models\Order;
use App\Models\Download;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Response;
use Carbon\Carbon;
use Exception;

class DownloadService
{
    /**
     * Process product download with tracking and limits
     */
    public function processDownload(Product $product, User $user, Order $order)
    {
        try {
            // Check download limits
            $limitCheck = $this->checkDownloadLimits($product, $user, $order);
            if (!$limitCheck['allowed']) {
                return [
                    'success' => false,
                    'error' => $limitCheck['error']
                ];
            }

            // Create download record
            $download = $this->createDownloadRecord($product, $user, $order);

            // Get download response
            $response = $this->getDownloadResponse($product, $download);

            if (!$response) {
                return [
                    'success' => false,
                    'error' => 'Download file not found or not available.'
                ];
            }

            // Update statistics
            $this->updateDownloadStatistics($product, $user);

            return [
                'success' => true,
                'response' => $response,
                'download' => $download
            ];

        } catch (Exception $e) {
            Log::error('Download processing failed', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'user_id' => $user->id,
            ]);

            return [
                'success' => false,
                'error' => 'Download processing failed. Please try again.'
            ];
        }
    }

    /**
     * Check download limits and restrictions
     */
    private function checkDownloadLimits(Product $product, User $user, Order $order)
    {
        $maxAttempts = config('templatescave.downloads.max_attempts', 5);
        $expiryHours = config('templatescave.downloads.expiry_hours', 24);

        // Check if order has expired download access
        if ($order && $order->paid_at) {
            $expiryDate = $order->paid_at->addHours($expiryHours);
            if (now()->gt($expiryDate) && !$product->is_free) {
                return [
                    'allowed' => false,
                    'error' => 'Download access has expired. Please contact support for assistance.'
                ];
            }
        }

        // Check download attempts for paid products
        if (!$product->is_free) {
            $downloadCount = Download::where('product_id', $product->id)
                ->where('user_id', $user->id)
                ->where('order_id', $order->id)
                ->count();

            if ($downloadCount >= $maxAttempts) {
                return [
                    'allowed' => false,
                    'error' => "Maximum download attempts ({$maxAttempts}) exceeded. Please contact support."
                ];
            }
        }

        return ['allowed' => true];
    }

    /**
     * Create download tracking record
     */
    private function createDownloadRecord(Product $product, User $user, Order $order)
    {
        return Download::create([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'order_id' => $order->id,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'downloaded_at' => now(),
        ]);
    }

    /**
     * Get appropriate download response
     */
    private function getDownloadResponse(Product $product, Download $download)
    {
        // Priority: Local file > External URLs
        if ($product->local_file_path && Storage::exists($product->local_file_path)) {
            return $this->createFileDownloadResponse($product, $download);
        }

        // Check for external file URLs
        if ($product->file_urls && is_array($product->file_urls) && count($product->file_urls) > 0) {
            return $this->createExternalDownloadResponse($product, $download);
        }

        return null;
    }

    /**
     * Create file download response for local files
     */
    private function createFileDownloadResponse(Product $product, Download $download)
    {
        $filePath = Storage::path($product->local_file_path);
        $fileName = $this->generateDownloadFileName($product);

        // Update download record with file info
        $download->update([
            'file_type' => 'local',
            'file_path' => $product->local_file_path,
            'file_size' => Storage::size($product->local_file_path),
        ]);

        return response()->download($filePath, $fileName, [
            'Content-Type' => Storage::mimeType($product->local_file_path),
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        ]);
    }

    /**
     * Create external download response
     */
    private function createExternalDownloadResponse(Product $product, Download $download)
    {
        $externalUrl = $product->file_urls[0]; // Use first URL

        // Update download record
        $download->update([
            'file_type' => 'external',
            'external_url' => $externalUrl,
        ]);

        // For external URLs, redirect to the URL
        return redirect()->away($externalUrl);
    }

    /**
     * Generate download filename
     */
    private function generateDownloadFileName(Product $product)
    {
        $extension = pathinfo($product->local_file_path, PATHINFO_EXTENSION);
        $cleanTitle = preg_replace('/[^A-Za-z0-9\-_]/', '_', $product->title);
        
        return $cleanTitle . '_' . $product->id . '.' . $extension;
    }

    /**
     * Update download statistics
     */
    private function updateDownloadStatistics(Product $product, User $user)
    {
        // Increment product download count
        $product->increment('download_count');

        // Log for analytics (optional)
        Log::info('Product downloaded', [
            'product_id' => $product->id,
            'product_title' => $product->title,
            'user_id' => $user->id,
            'user_email' => $user->email,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get user's download history
     */
    public function getUserDownloadHistory(User $user, $limit = 50)
    {
        return Download::with(['product', 'order'])
            ->where('user_id', $user->id)
            ->orderBy('downloaded_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get product download statistics
     */
    public function getProductDownloadStats(Product $product)
    {
        $downloads = Download::where('product_id', $product->id);

        return [
            'total_downloads' => $downloads->count(),
            'unique_users' => $downloads->distinct('user_id')->count(),
            'recent_downloads' => $downloads->where('downloaded_at', '>=', now()->subDays(7))->count(),
            'download_by_day' => $downloads
                ->selectRaw('DATE(downloaded_at) as date, COUNT(*) as count')
                ->where('downloaded_at', '>=', now()->subDays(30))
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
        ];
    }

    /**
     * Clean up old download records
     */
    public function cleanupOldDownloads($daysToKeep = 90)
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        $deletedCount = Download::where('downloaded_at', '<', $cutoffDate)->delete();
        
        Log::info('Cleaned up old download records', [
            'deleted_count' => $deletedCount,
            'cutoff_date' => $cutoffDate->toDateString(),
        ]);

        return $deletedCount;
    }

    /**
     * Generate download analytics report
     */
    public function generateDownloadReport($startDate = null, $endDate = null)
    {
        $startDate = $startDate ?: now()->subDays(30);
        $endDate = $endDate ?: now();

        $downloads = Download::with(['product', 'user'])
            ->whereBetween('downloaded_at', [$startDate, $endDate]);

        return [
            'period' => [
                'start' => $startDate->toDateString(),
                'end' => $endDate->toDateString(),
            ],
            'total_downloads' => $downloads->count(),
            'unique_users' => $downloads->distinct('user_id')->count(),
            'unique_products' => $downloads->distinct('product_id')->count(),
            'top_products' => $downloads
                ->selectRaw('product_id, COUNT(*) as download_count')
                ->groupBy('product_id')
                ->orderBy('download_count', 'desc')
                ->limit(10)
                ->with('product')
                ->get(),
            'downloads_by_day' => $downloads
                ->selectRaw('DATE(downloaded_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
        ];
    }
}
