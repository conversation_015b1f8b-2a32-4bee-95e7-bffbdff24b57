<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Exception\ApiErrorException;
use Illuminate\Support\Facades\Log;
use Exception;

class PaymentService
{
    public function __construct()
    {
        // Set Stripe API key
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Create a payment intent for Stripe
     */
    public function createStripePaymentIntent(Product $product, User $user, array $metadata = [])
    {
        try {
            // Create order first
            $order = Order::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'amount' => $product->price,
                'currency' => config('templatescave.payments.currency', 'usd'),
                'status' => 'pending',
                'payment_method' => 'stripe',
            ]);

            // Create Stripe Payment Intent
            $paymentIntent = PaymentIntent::create([
                'amount' => $product->price * 100, // Stripe expects amount in cents
                'currency' => config('templatescave.payments.currency', 'usd'),
                'metadata' => array_merge([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'user_id' => $user->id,
                    'product_title' => $product->title,
                ], $metadata),
                'description' => "Purchase of {$product->title}",
            ]);

            // Update order with payment intent ID
            $order->update([
                'payment_id' => $paymentIntent->id,
                'payment_details' => [
                    'payment_intent_id' => $paymentIntent->id,
                    'client_secret' => $paymentIntent->client_secret,
                ],
            ]);

            return [
                'success' => true,
                'order' => $order,
                'payment_intent' => $paymentIntent,
                'client_secret' => $paymentIntent->client_secret,
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe Payment Intent creation failed', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'user_id' => $user->id,
            ]);

            return [
                'success' => false,
                'error' => 'Payment processing failed. Please try again.',
                'details' => $e->getMessage(),
            ];

        } catch (Exception $e) {
            Log::error('Payment Intent creation failed', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'user_id' => $user->id,
            ]);

            return [
                'success' => false,
                'error' => 'An unexpected error occurred. Please try again.',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Confirm Stripe payment
     */
    public function confirmStripePayment($paymentIntentId)
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            
            // Find the order
            $order = Order::where('payment_id', $paymentIntentId)->first();
            
            if (!$order) {
                throw new Exception('Order not found for payment intent: ' . $paymentIntentId);
            }

            if ($paymentIntent->status === 'succeeded') {
                $order->markAsCompleted();
                
                // Update payment details
                $order->update([
                    'payment_details' => array_merge($order->payment_details ?? [], [
                        'payment_intent_status' => $paymentIntent->status,
                        'payment_method_id' => $paymentIntent->payment_method,
                        'confirmed_at' => now()->toISOString(),
                    ]),
                ]);

                return [
                    'success' => true,
                    'order' => $order,
                    'payment_intent' => $paymentIntent,
                ];
            } else {
                $order->markAsFailed();
                
                return [
                    'success' => false,
                    'error' => 'Payment was not successful',
                    'status' => $paymentIntent->status,
                ];
            }

        } catch (ApiErrorException $e) {
            Log::error('Stripe payment confirmation failed', [
                'error' => $e->getMessage(),
                'payment_intent_id' => $paymentIntentId,
            ]);

            return [
                'success' => false,
                'error' => 'Payment confirmation failed',
                'details' => $e->getMessage(),
            ];

        } catch (Exception $e) {
            Log::error('Payment confirmation failed', [
                'error' => $e->getMessage(),
                'payment_intent_id' => $paymentIntentId,
            ]);

            return [
                'success' => false,
                'error' => 'An unexpected error occurred',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create manual bank transfer order
     */
    public function createBankTransferOrder(Product $product, User $user, array $bankDetails = [])
    {
        try {
            $order = Order::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'amount' => $product->price,
                'currency' => config('templatescave.payments.currency', 'usd'),
                'status' => 'pending',
                'payment_method' => 'bank_transfer',
                'payment_details' => [
                    'bank_details' => $bankDetails,
                    'instructions' => config('templatescave.payments.bank_transfer.instructions'),
                    'created_at' => now()->toISOString(),
                ],
                'notes' => 'Awaiting bank transfer confirmation',
            ]);

            return [
                'success' => true,
                'order' => $order,
                'bank_details' => config('templatescave.payments.bank_transfer'),
            ];

        } catch (Exception $e) {
            Log::error('Bank transfer order creation failed', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'user_id' => $user->id,
            ]);

            return [
                'success' => false,
                'error' => 'Failed to create bank transfer order',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Process free product download
     */
    public function processFreeProduct(Product $product, User $user)
    {
        try {
            $order = Order::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'amount' => 0.00,
                'currency' => config('templatescave.payments.currency', 'usd'),
                'status' => 'completed',
                'payment_method' => 'free',
                'paid_at' => now(),
                'payment_details' => [
                    'type' => 'free_product',
                    'processed_at' => now()->toISOString(),
                ],
            ]);

            return [
                'success' => true,
                'order' => $order,
            ];

        } catch (Exception $e) {
            Log::error('Free product processing failed', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'user_id' => $user->id,
            ]);

            return [
                'success' => false,
                'error' => 'Failed to process free product',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get payment methods configuration
     */
    public function getPaymentMethods()
    {
        return [
            'stripe' => [
                'enabled' => config('services.stripe.enabled', false),
                'name' => 'Credit/Debit Card',
                'description' => 'Pay securely with your credit or debit card',
                'icon' => 'fas fa-credit-card',
            ],
            'paypal' => [
                'enabled' => config('services.paypal.enabled', false),
                'name' => 'PayPal',
                'description' => 'Pay with your PayPal account',
                'icon' => 'fab fa-paypal',
            ],
            'bank_transfer' => [
                'enabled' => config('templatescave.payments.bank_transfer.enabled', false),
                'name' => 'Bank Transfer',
                'description' => 'Pay via direct bank transfer',
                'icon' => 'fas fa-university',
            ],
        ];
    }

    /**
     * Validate payment method
     */
    public function isPaymentMethodEnabled($method)
    {
        $methods = $this->getPaymentMethods();
        return isset($methods[$method]) && $methods[$method]['enabled'];
    }
}
