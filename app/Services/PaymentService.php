<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Models\Setting;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Exception\ApiErrorException;
use PayPal\Api\Amount;
use PayPal\Api\Details;
use PayPal\Api\Item;
use PayPal\Api\ItemList;
use PayPal\Api\Payer;
use PayPal\Api\Payment;
use PayPal\Api\PaymentExecution;
use PayPal\Api\RedirectUrls;
use PayPal\Api\Transaction;
use PayPal\Auth\OAuthTokenCredential;
use PayPal\Rest\ApiContext;
use Illuminate\Support\Facades\Log;
use PrevailExcel\Nowpayments\Facades\Nowpayments;
use Exception;

class PaymentService
{
    private $paypalApiContext;

    public function __construct()
    {
        // Set Stripe API key
        Stripe::setApiKey(config('services.stripe.secret'));

        // Initialize PayPal API context
        $this->initializePayPal();
    }

    /**
     * Initialize PayPal API context
     */
    private function initializePayPal()
    {
        $clientId = Setting::get('paypal_client_id') ?: config('services.paypal.client_id');
        $clientSecret = Setting::get('paypal_client_secret') ?: config('services.paypal.client_secret');
        $mode = Setting::get('paypal_mode', 'sandbox');

        if ($clientId && $clientSecret) {
            $this->paypalApiContext = new ApiContext(
                new OAuthTokenCredential($clientId, $clientSecret)
            );

            $this->paypalApiContext->setConfig([
                'mode' => $mode,
                'log.LogEnabled' => true,
                'log.FileName' => storage_path('logs/paypal.log'),
                'log.LogLevel' => 'ERROR',
                'cache.enabled' => true,
            ]);
        }
    }

    /**
     * Create a payment intent for Stripe
     */
    public function createStripePaymentIntent(Product $product, User $user, array $metadata = [])
    {
        try {
            // Create order first
            $order = Order::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'amount' => $product->price,
                'currency' => config('templatescave.payments.currency', 'usd'),
                'status' => 'pending',
                'payment_method' => 'stripe',
            ]);

            // Create Stripe Payment Intent
            $paymentIntent = PaymentIntent::create([
                'amount' => $product->price * 100, // Stripe expects amount in cents
                'currency' => config('templatescave.payments.currency', 'usd'),
                'metadata' => array_merge([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'user_id' => $user->id,
                    'product_title' => $product->title,
                ], $metadata),
                'description' => "Purchase of {$product->title}",
            ]);

            // Update order with payment intent ID
            $order->update([
                'payment_id' => $paymentIntent->id,
                'payment_details' => [
                    'payment_intent_id' => $paymentIntent->id,
                    'client_secret' => $paymentIntent->client_secret,
                ],
            ]);

            return [
                'success' => true,
                'order' => $order,
                'payment_intent' => $paymentIntent,
                'client_secret' => $paymentIntent->client_secret,
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe Payment Intent creation failed', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'user_id' => $user->id,
            ]);

            return [
                'success' => false,
                'error' => 'Payment processing failed. Please try again.',
                'details' => $e->getMessage(),
            ];

        } catch (Exception $e) {
            Log::error('Payment Intent creation failed', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'user_id' => $user->id,
            ]);

            return [
                'success' => false,
                'error' => 'An unexpected error occurred. Please try again.',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Confirm Stripe payment
     */
    public function confirmStripePayment($paymentIntentId)
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            
            // Find the order
            $order = Order::where('payment_id', $paymentIntentId)->first();
            
            if (!$order) {
                throw new Exception('Order not found for payment intent: ' . $paymentIntentId);
            }

            if ($paymentIntent->status === 'succeeded') {
                $order->markAsCompleted();
                
                // Update payment details
                $order->update([
                    'payment_details' => array_merge($order->payment_details ?? [], [
                        'payment_intent_status' => $paymentIntent->status,
                        'payment_method_id' => $paymentIntent->payment_method,
                        'confirmed_at' => now()->toISOString(),
                    ]),
                ]);

                return [
                    'success' => true,
                    'order' => $order,
                    'payment_intent' => $paymentIntent,
                ];
            } else {
                $order->markAsFailed();
                
                return [
                    'success' => false,
                    'error' => 'Payment was not successful',
                    'status' => $paymentIntent->status,
                ];
            }

        } catch (ApiErrorException $e) {
            Log::error('Stripe payment confirmation failed', [
                'error' => $e->getMessage(),
                'payment_intent_id' => $paymentIntentId,
            ]);

            return [
                'success' => false,
                'error' => 'Payment confirmation failed',
                'details' => $e->getMessage(),
            ];

        } catch (Exception $e) {
            Log::error('Payment confirmation failed', [
                'error' => $e->getMessage(),
                'payment_intent_id' => $paymentIntentId,
            ]);

            return [
                'success' => false,
                'error' => 'An unexpected error occurred',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create PayPal payment
     */
    public function createPayPalPayment(Product $product, User $user)
    {
        try {
            if (!$this->paypalApiContext) {
                throw new Exception('PayPal is not configured');
            }

            // Create order first
            $order = Order::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'amount' => $product->price,
                'currency' => config('templatescave.payments.currency', 'usd'),
                'status' => 'pending',
                'payment_method' => 'paypal',
            ]);

            // Create PayPal payment
            $payer = new Payer();
            $payer->setPaymentMethod('paypal');

            $item = new Item();
            $item->setName($product->title)
                ->setCurrency(strtoupper(config('templatescave.payments.currency', 'usd')))
                ->setQuantity(1)
                ->setPrice($product->price);

            $itemList = new ItemList();
            $itemList->setItems([$item]);

            $amount = new Amount();
            $amount->setCurrency(strtoupper(config('templatescave.payments.currency', 'usd')))
                ->setTotal($product->price);

            $transaction = new Transaction();
            $transaction->setAmount($amount)
                ->setItemList($itemList)
                ->setDescription("Purchase of {$product->title}")
                ->setInvoiceNumber($order->order_number);

            $redirectUrls = new RedirectUrls();
            $redirectUrls->setReturnUrl(route('payment.paypal.success', $order->id))
                ->setCancelUrl(route('payment.paypal.cancel', $order->id));

            $payment = new Payment();
            $payment->setIntent('sale')
                ->setPayer($payer)
                ->setRedirectUrls($redirectUrls)
                ->setTransactions([$transaction]);

            $payment->create($this->paypalApiContext);

            // Update order with PayPal payment ID
            $order->update([
                'payment_id' => $payment->getId(),
                'payment_details' => [
                    'paypal_payment_id' => $payment->getId(),
                    'approval_url' => $payment->getApprovalLink(),
                ],
            ]);

            return [
                'success' => true,
                'order' => $order,
                'approval_url' => $payment->getApprovalLink(),
                'payment_id' => $payment->getId(),
            ];

        } catch (Exception $e) {
            // Update PayPal error status
            Setting::set('paypal_last_error', 'Payment creation failed: ' . $e->getMessage(), 'string', 'payment');

            Log::error('PayPal payment creation failed', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'user_id' => $user->id,
                'trace' => $e->getTraceAsString(),
                'paypal_configured' => $this->paypalApiContext ? 'Yes' : 'No',
            ]);

            return [
                'success' => false,
                'error' => 'Failed to create PayPal payment. Please try again or contact support.',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Execute PayPal payment
     */
    public function executePayPalPayment($paymentId, $payerId, Order $order)
    {
        try {
            if (!$this->paypalApiContext) {
                throw new Exception('PayPal is not configured');
            }

            $payment = Payment::get($paymentId, $this->paypalApiContext);

            $execution = new PaymentExecution();
            $execution->setPayerId($payerId);

            $result = $payment->execute($execution, $this->paypalApiContext);

            if ($result->getState() === 'approved') {
                // Update order as completed
                $order->update([
                    'status' => 'completed',
                    'paid_at' => now(),
                    'payment_details' => array_merge($order->payment_details ?? [], [
                        'paypal_payment_id' => $paymentId,
                        'paypal_payer_id' => $payerId,
                        'paypal_state' => $result->getState(),
                        'executed_at' => now()->toISOString(),
                    ]),
                ]);

                return [
                    'success' => true,
                    'order' => $order,
                    'payment' => $result,
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'PayPal payment was not approved',
                ];
            }

        } catch (Exception $e) {
            // Update PayPal error status
            Setting::set('paypal_last_error', 'Payment execution failed: ' . $e->getMessage(), 'string', 'payment');

            // Update order with error
            $order->update([
                'status' => 'failed',
                'notes' => 'PayPal payment execution failed: ' . $e->getMessage(),
            ]);

            Log::error('PayPal payment execution failed', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
                'payer_id' => $payerId,
                'order_id' => $order->id,
                'trace' => $e->getTraceAsString(),
                'paypal_configured' => $this->paypalApiContext ? 'Yes' : 'No',
            ]);

            return [
                'success' => false,
                'error' => 'Failed to complete PayPal payment. Please try again or contact support.',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create manual bank transfer order
     */
    public function createBankTransferOrder(Product $product, User $user, array $bankDetails = [])
    {
        try {
            $order = Order::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'amount' => $product->price,
                'currency' => config('templatescave.payments.currency', 'usd'),
                'status' => 'pending',
                'payment_method' => 'bank_transfer',
                'payment_details' => [
                    'bank_details' => $bankDetails,
                    'instructions' => config('templatescave.payments.bank_transfer.instructions'),
                    'created_at' => now()->toISOString(),
                ],
                'notes' => 'Awaiting bank transfer confirmation',
            ]);

            return [
                'success' => true,
                'order' => $order,
                'bank_details' => config('templatescave.payments.bank_transfer'),
            ];

        } catch (Exception $e) {
            Log::error('Bank transfer order creation failed', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'user_id' => $user->id,
            ]);

            return [
                'success' => false,
                'error' => 'Failed to create bank transfer order',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create cryptocurrency payment
     */
    public function createCryptoPayment(Product $product, User $user, $payCurrency = 'btc')
    {
        try {
            // Create order first
            $order = Order::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'amount' => $product->price,
                'currency' => config('templatescave.payments.currency', 'usd'),
                'status' => 'pending',
                'payment_method' => 'crypto',
            ]);

            // Prepare payment data for NOWPayments
            $paymentData = [
                'price_amount' => $product->price,
                'price_currency' => strtolower(config('templatescave.payments.currency', 'usd')),
                'pay_currency' => strtolower($payCurrency),
                'order_id' => $order->order_number,
                'order_description' => "Purchase of {$product->title}",
                'ipn_callback_url' => config('nowpayments.callbackUrl'),
                'success_url' => route('payment.crypto.success', $order->id),
                'cancel_url' => route('payment.crypto.cancel', $order->id),
            ];

            // Create payment with NOWPayments
            $paymentDetails = Nowpayments::createPayment($paymentData);

            if (isset($paymentDetails['payment_id'])) {
                // Update order with crypto payment details
                $order->update([
                    'payment_id' => $paymentDetails['payment_id'],
                    'payment_details' => [
                        'crypto_payment_id' => $paymentDetails['payment_id'],
                        'pay_address' => $paymentDetails['pay_address'],
                        'pay_amount' => $paymentDetails['pay_amount'],
                        'pay_currency' => $paymentDetails['pay_currency'],
                        'price_amount' => $paymentDetails['price_amount'],
                        'price_currency' => $paymentDetails['price_currency'],
                        'payment_status' => $paymentDetails['payment_status'],
                        'created_at' => now()->toISOString(),
                    ],
                ]);

                return [
                    'success' => true,
                    'order' => $order,
                    'payment_details' => $paymentDetails,
                    'pay_address' => $paymentDetails['pay_address'],
                    'pay_amount' => $paymentDetails['pay_amount'],
                    'pay_currency' => $paymentDetails['pay_currency'],
                ];
            } else {
                throw new Exception('Failed to create crypto payment: Invalid response from payment gateway');
            }

        } catch (Exception $e) {
            Log::error('Crypto payment creation failed', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'user_id' => $user->id,
                'pay_currency' => $payCurrency,
            ]);

            return [
                'success' => false,
                'error' => 'Failed to create cryptocurrency payment. Please try again.',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check crypto payment status
     */
    public function checkCryptoPaymentStatus($paymentId)
    {
        try {
            $paymentStatus = Nowpayments::getPaymentStatus($paymentId);

            // Find the order
            $order = Order::where('payment_id', $paymentId)->first();

            if (!$order) {
                throw new Exception('Order not found for payment ID: ' . $paymentId);
            }

            // Update order based on payment status
            $order->update([
                'payment_details' => array_merge($order->payment_details ?? [], [
                    'payment_status' => $paymentStatus['payment_status'],
                    'actually_paid' => $paymentStatus['actually_paid'] ?? null,
                    'outcome_amount' => $paymentStatus['outcome_amount'] ?? null,
                    'outcome_currency' => $paymentStatus['outcome_currency'] ?? null,
                    'updated_at' => now()->toISOString(),
                ]),
            ]);

            // Update order status based on payment status
            switch ($paymentStatus['payment_status']) {
                case 'finished':
                case 'confirmed':
                    $order->markAsCompleted();
                    break;
                case 'failed':
                case 'expired':
                case 'refunded':
                    $order->markAsFailed();
                    break;
                case 'partially_paid':
                    $order->update(['status' => 'partially_paid']);
                    break;
                // 'waiting', 'confirming', 'sending' remain as pending
            }

            return [
                'success' => true,
                'order' => $order,
                'payment_status' => $paymentStatus,
            ];

        } catch (Exception $e) {
            Log::error('Crypto payment status check failed', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);

            return [
                'success' => false,
                'error' => 'Failed to check payment status',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get available cryptocurrencies
     */
    public function getAvailableCryptocurrencies()
    {
        try {
            $currencies = Nowpayments::getCurrencies();

            // Filter to popular cryptocurrencies for better UX
            $popularCryptos = [
                'btc' => 'Bitcoin',
                'eth' => 'Ethereum',
                'ltc' => 'Litecoin',
                'usdt' => 'Tether',
                'usdc' => 'USD Coin',
                'bnb' => 'Binance Coin',
                'ada' => 'Cardano',
                'dot' => 'Polkadot',
                'sol' => 'Solana',
                'matic' => 'Polygon',
            ];

            $availableCryptos = [];
            foreach ($currencies['currencies'] ?? [] as $currency) {
                $code = strtolower($currency);
                if (isset($popularCryptos[$code])) {
                    $availableCryptos[$code] = $popularCryptos[$code];
                }
            }

            return $availableCryptos;

        } catch (Exception $e) {
            Log::error('Failed to get available cryptocurrencies', [
                'error' => $e->getMessage(),
            ]);

            // Return default popular cryptocurrencies as fallback
            return [
                'btc' => 'Bitcoin',
                'eth' => 'Ethereum',
                'ltc' => 'Litecoin',
                'usdt' => 'Tether',
            ];
        }
    }

    /**
     * Process free product download
     */
    public function processFreeProduct(Product $product, User $user)
    {
        try {
            $order = Order::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'amount' => 0.00,
                'currency' => config('templatescave.payments.currency', 'usd'),
                'status' => 'completed',
                'payment_method' => 'free',
                'paid_at' => now(),
                'payment_details' => [
                    'type' => 'free_product',
                    'processed_at' => now()->toISOString(),
                ],
            ]);

            return [
                'success' => true,
                'order' => $order,
            ];

        } catch (Exception $e) {
            Log::error('Free product processing failed', [
                'error' => $e->getMessage(),
                'product_id' => $product->id,
                'user_id' => $user->id,
            ]);

            return [
                'success' => false,
                'error' => 'Failed to process free product',
                'details' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get payment methods configuration
     */
    public function getPaymentMethods()
    {
        return [
            'stripe' => [
                'enabled' => config('services.stripe.enabled', false),
                'name' => 'Credit/Debit Card',
                'description' => 'Pay securely with your credit or debit card',
                'icon' => 'fas fa-credit-card',
            ],
            'paypal' => [
                'enabled' => Setting::get('paypal_enabled', false),
                'name' => 'PayPal',
                'description' => 'Pay with your PayPal account',
                'icon' => 'fab fa-paypal',
            ],
            'bank_transfer' => [
                'enabled' => config('templatescave.payments.bank_transfer.enabled', false),
                'name' => 'Bank Transfer',
                'description' => 'Pay via direct bank transfer',
                'icon' => 'fas fa-university',
            ],
            'crypto' => [
                'enabled' => Setting::get('nowpayments_enabled', false) && !empty(Setting::get('nowpayments_api_key')),
                'name' => 'Cryptocurrency',
                'description' => 'Pay with Bitcoin, Ethereum, and other cryptocurrencies',
                'icon' => 'fab fa-bitcoin',
            ],
        ];
    }

    /**
     * Validate payment method
     */
    public function isPaymentMethodEnabled($method)
    {
        $methods = $this->getPaymentMethods();
        return isset($methods[$method]) && $methods[$method]['enabled'];
    }
}
