<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PerformanceService
{
    /**
     * Track page load time
     */
    public function trackPageLoad($route, $loadTime, $memoryUsage = null)
    {
        $data = [
            'route' => $route,
            'load_time' => $loadTime,
            'memory_usage' => $memoryUsage ?: memory_get_peak_usage(true),
            'timestamp' => now(),
            'user_agent' => request()->userAgent(),
            'ip' => request()->ip(),
        ];

        // Store in cache for real-time monitoring
        $cacheKey = 'performance_' . date('Y-m-d-H');
        $existing = Cache::get($cacheKey, []);
        $existing[] = $data;
        
        // Keep only last 100 entries per hour
        if (count($existing) > 100) {
            $existing = array_slice($existing, -100);
        }
        
        Cache::put($cacheKey, $existing, 3600); // 1 hour

        // Log slow pages
        if ($loadTime > 2000) { // 2 seconds
            Log::warning('Slow page detected', $data);
        }
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics($hours = 24)
    {
        $metrics = [];
        $now = now();
        
        for ($i = 0; $i < $hours; $i++) {
            $hour = $now->copy()->subHours($i);
            $cacheKey = 'performance_' . $hour->format('Y-m-d-H');
            $hourData = Cache::get($cacheKey, []);
            
            if (!empty($hourData)) {
                $loadTimes = array_column($hourData, 'load_time');
                $memoryUsages = array_column($hourData, 'memory_usage');
                
                $metrics[$hour->format('Y-m-d H:00')] = [
                    'requests' => count($hourData),
                    'avg_load_time' => round(array_sum($loadTimes) / count($loadTimes), 2),
                    'max_load_time' => max($loadTimes),
                    'min_load_time' => min($loadTimes),
                    'avg_memory' => round(array_sum($memoryUsages) / count($memoryUsages)),
                    'max_memory' => max($memoryUsages),
                ];
            }
        }
        
        return array_reverse($metrics, true);
    }

    /**
     * Get slow queries
     */
    public function getSlowQueries($limit = 10)
    {
        return Cache::remember('slow_queries', 300, function () use ($limit) {
            // This would typically come from query log analysis
            // For now, we'll return a placeholder structure
            return [
                [
                    'query' => 'SELECT * FROM products WHERE status = ? ORDER BY created_at DESC',
                    'time' => 1.25,
                    'count' => 45,
                    'route' => 'products.index',
                ],
                [
                    'query' => 'SELECT * FROM blog_posts WHERE status = ? AND published_at <= ?',
                    'time' => 0.89,
                    'count' => 23,
                    'route' => 'blog.index',
                ],
            ];
        });
    }

    /**
     * Optimize database queries
     */
    public function optimizeQueries()
    {
        $optimizations = [];

        // Check for missing indexes
        $tables = ['products', 'blog_posts', 'orders', 'downloads'];
        
        foreach ($tables as $table) {
            $indexes = DB::select("SHOW INDEX FROM {$table}");
            $indexedColumns = array_column($indexes, 'Column_name');
            
            // Common columns that should be indexed
            $shouldBeIndexed = ['status', 'created_at', 'updated_at', 'slug', 'user_id'];
            
            foreach ($shouldBeIndexed as $column) {
                if (!in_array($column, $indexedColumns)) {
                    $optimizations[] = "Missing index on {$table}.{$column}";
                }
            }
        }

        return $optimizations;
    }

    /**
     * Cache popular content
     */
    public function cachePopularContent()
    {
        // Cache popular products
        $popularProducts = Cache::remember('popular_products', 3600, function () {
            return \App\Models\Product::where('status', 'published')
                ->orderBy('download_count', 'desc')
                ->limit(12)
                ->with(['category', 'images'])
                ->get();
        });

        // Cache popular blog posts
        $popularPosts = Cache::remember('popular_blog_posts', 3600, function () {
            return \App\Models\BlogPost::where('status', 'published')
                ->orderBy('view_count', 'desc')
                ->limit(6)
                ->with(['author', 'category'])
                ->get();
        });

        // Cache categories with product counts
        $categoriesWithCounts = Cache::remember('categories_with_counts', 1800, function () {
            return \App\Models\Category::where('is_active', true)
                ->withCount(['products' => function ($query) {
                    $query->where('status', 'published');
                }])
                ->orderBy('products_count', 'desc')
                ->get();
        });

        return [
            'popular_products' => $popularProducts,
            'popular_posts' => $popularPosts,
            'categories_with_counts' => $categoriesWithCounts,
        ];
    }

    /**
     * Clear performance caches
     */
    public function clearPerformanceCaches()
    {
        $patterns = [
            'popular_*',
            'categories_*',
            'performance_*',
            'slow_queries',
            'sitemap',
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }

        return true;
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats()
    {
        $stats = [
            'total_keys' => 0,
            'memory_usage' => 0,
            'hit_rate' => 0,
            'popular_keys' => [],
        ];

        // This would depend on your cache driver
        // For Redis, you could use Redis commands
        // For file cache, you could scan the cache directory
        
        return $stats;
    }

    /**
     * Monitor critical metrics
     */
    public function getCriticalMetrics()
    {
        return [
            'database' => [
                'connections' => DB::select('SHOW STATUS LIKE "Threads_connected"')[0]->Value ?? 0,
                'slow_queries' => DB::select('SHOW STATUS LIKE "Slow_queries"')[0]->Value ?? 0,
                'uptime' => DB::select('SHOW STATUS LIKE "Uptime"')[0]->Value ?? 0,
            ],
            'application' => [
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true),
                'load_average' => sys_getloadavg()[0] ?? 0,
            ],
            'cache' => [
                'status' => Cache::getStore() instanceof \Illuminate\Cache\NullStore ? 'disabled' : 'enabled',
                'driver' => config('cache.default'),
            ],
        ];
    }

    /**
     * Generate performance report
     */
    public function generatePerformanceReport($period = '24h')
    {
        $metrics = $this->getPerformanceMetrics(24);
        $slowQueries = $this->getSlowQueries();
        $criticalMetrics = $this->getCriticalMetrics();
        $optimizations = $this->optimizeQueries();

        return [
            'period' => $period,
            'generated_at' => now(),
            'summary' => [
                'total_requests' => array_sum(array_column($metrics, 'requests')),
                'avg_response_time' => round(array_sum(array_column($metrics, 'avg_load_time')) / count($metrics), 2),
                'slowest_page' => max(array_column($metrics, 'max_load_time')),
                'memory_peak' => max(array_column($metrics, 'max_memory')),
            ],
            'hourly_metrics' => $metrics,
            'slow_queries' => $slowQueries,
            'critical_metrics' => $criticalMetrics,
            'optimization_suggestions' => $optimizations,
        ];
    }

    /**
     * Image optimization suggestions
     */
    public function getImageOptimizationSuggestions()
    {
        $suggestions = [];
        
        // Check for large images
        $largeImages = \App\Models\ProductImage::whereRaw('LENGTH(image_path) > 0')
            ->get()
            ->filter(function ($image) {
                $path = storage_path('app/public/' . $image->image_path);
                if (file_exists($path)) {
                    $size = filesize($path);
                    return $size > 500000; // 500KB
                }
                return false;
            });

        if ($largeImages->count() > 0) {
            $suggestions[] = [
                'type' => 'image_compression',
                'message' => "Found {$largeImages->count()} images larger than 500KB that could be compressed",
                'priority' => 'medium',
            ];
        }

        // Check for missing WebP versions
        $suggestions[] = [
            'type' => 'webp_conversion',
            'message' => 'Consider converting images to WebP format for better compression',
            'priority' => 'low',
        ];

        return $suggestions;
    }
}
