<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class SEOService
{
    /**
     * Generate meta tags for a page
     */
    public function generateMetaTags($data = [])
    {
        $defaults = [
            'title' => config('app.name') . ' - Premium Digital Templates & Resources',
            'description' => 'Discover premium website templates, UI kits, and digital resources for your next project. High-quality designs for developers and designers.',
            'keywords' => ['templates', 'web design', 'UI kits', 'digital resources', 'website templates'],
            'canonical' => request()->url(),
            'image' => asset('images/og-default.jpg'),
            'type' => 'website',
            'locale' => 'en_US',
            'site_name' => config('app.name'),
        ];

        $meta = array_merge($defaults, $data);

        return [
            'title' => $this->cleanTitle($meta['title']),
            'description' => $this->cleanDescription($meta['description']),
            'keywords' => is_array($meta['keywords']) ? implode(', ', $meta['keywords']) : $meta['keywords'],
            'canonical' => $meta['canonical'],
            'og' => [
                'title' => $this->cleanTitle($meta['title']),
                'description' => $this->cleanDescription($meta['description']),
                'image' => $meta['image'],
                'url' => $meta['canonical'],
                'type' => $meta['type'],
                'locale' => $meta['locale'],
                'site_name' => $meta['site_name'],
            ],
            'twitter' => [
                'card' => 'summary_large_image',
                'title' => $this->cleanTitle($meta['title']),
                'description' => $this->cleanDescription($meta['description']),
                'image' => $meta['image'],
                'site' => '@templatescave',
            ],
        ];
    }

    /**
     * Generate product-specific meta tags
     */
    public function generateProductMeta($product)
    {
        $title = $product->name . ' - ' . config('app.name');
        $description = $product->description ? 
            strip_tags($product->description) : 
            "Download {$product->name} - Premium digital template from Templates Cave";
        
        $keywords = array_merge(
            [$product->name, $product->category->name ?? 'template'],
            $product->tags ?? [],
            ['download', 'digital', 'template', 'design']
        );

        $image = $product->primary_image ? 
            $product->primary_image->image_url : 
            asset('images/og-product-default.jpg');

        return $this->generateMetaTags([
            'title' => $title,
            'description' => $description,
            'keywords' => $keywords,
            'canonical' => route('products.show', $product->slug),
            'image' => $image,
            'type' => 'product',
        ]);
    }

    /**
     * Generate blog post meta tags
     */
    public function generateBlogMeta($post)
    {
        $title = $post->meta_title ?: ($post->title . ' - ' . config('app.name') . ' Blog');
        $description = $post->meta_description ?: $post->excerpt;
        $keywords = array_merge(
            $post->meta_keywords ?? [],
            $post->tags ?? [],
            [$post->category->name ?? 'blog', 'tutorial', 'guide']
        );

        $image = $post->featured_image ? 
            asset('uploads/blog/' . $post->featured_image) : 
            asset('images/og-blog-default.jpg');

        $meta = $this->generateMetaTags([
            'title' => $title,
            'description' => $description,
            'keywords' => $keywords,
            'canonical' => $post->canonical_url ?: route('blog.show', $post->slug),
            'image' => $image,
            'type' => 'article',
        ]);

        // Add article-specific Open Graph tags
        $meta['og']['article'] = [
            'author' => $post->author->name,
            'published_time' => $post->published_at->toISOString(),
            'modified_time' => $post->updated_at->toISOString(),
            'section' => $post->category->name ?? 'Blog',
            'tag' => $post->tags ?? [],
        ];

        // Merge social meta if available
        if ($post->social_meta) {
            $meta['og'] = array_merge($meta['og'], $post->social_meta['og'] ?? []);
            $meta['twitter'] = array_merge($meta['twitter'], $post->social_meta['twitter'] ?? []);
        }

        return $meta;
    }

    /**
     * Generate category meta tags
     */
    public function generateCategoryMeta($category)
    {
        $title = $category->name . ' Templates - ' . config('app.name');
        $description = $category->description ?: 
            "Browse our collection of {$category->name} templates and digital resources. High-quality designs for your projects.";
        
        $keywords = [
            $category->name,
            'templates',
            'digital resources',
            'web design',
            'UI kits'
        ];

        return $this->generateMetaTags([
            'title' => $title,
            'description' => $description,
            'keywords' => $keywords,
            'canonical' => route('categories.show', $category->slug),
            'type' => 'website',
        ]);
    }

    /**
     * Generate XML sitemap
     */
    public function generateSitemap()
    {
        return Cache::remember('sitemap', 3600, function () {
            $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
            $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

            // Homepage
            $sitemap .= $this->addSitemapUrl(route('home'), now(), 'daily', '1.0');

            // Static pages
            $sitemap .= $this->addSitemapUrl(route('products.index'), now(), 'daily', '0.9');
            $sitemap .= $this->addSitemapUrl(route('categories.index'), now(), 'weekly', '0.8');
            $sitemap .= $this->addSitemapUrl(route('blog.index'), now(), 'daily', '0.8');

            // Products
            \App\Models\Product::where('status', 'published')
                ->orderBy('updated_at', 'desc')
                ->chunk(100, function ($products) use (&$sitemap) {
                    foreach ($products as $product) {
                        $sitemap .= $this->addSitemapUrl(
                            route('products.show', $product->slug),
                            $product->updated_at,
                            'weekly',
                            '0.7'
                        );
                    }
                });

            // Categories
            \App\Models\Category::where('is_active', true)
                ->orderBy('updated_at', 'desc')
                ->chunk(50, function ($categories) use (&$sitemap) {
                    foreach ($categories as $category) {
                        $sitemap .= $this->addSitemapUrl(
                            route('categories.show', $category->slug),
                            $category->updated_at,
                            'weekly',
                            '0.6'
                        );
                    }
                });

            // Blog posts
            \App\Models\BlogPost::where('status', 'published')
                ->orderBy('updated_at', 'desc')
                ->chunk(100, function ($posts) use (&$sitemap) {
                    foreach ($posts as $post) {
                        $sitemap .= $this->addSitemapUrl(
                            route('blog.show', $post->slug),
                            $post->updated_at,
                            'monthly',
                            '0.6'
                        );
                    }
                });

            // Blog categories
            \App\Models\BlogCategory::where('is_active', true)
                ->orderBy('updated_at', 'desc')
                ->chunk(50, function ($categories) use (&$sitemap) {
                    foreach ($categories as $category) {
                        $sitemap .= $this->addSitemapUrl(
                            route('blog.category', $category->slug),
                            $category->updated_at,
                            'weekly',
                            '0.5'
                        );
                    }
                });

            $sitemap .= '</urlset>';

            return $sitemap;
        });
    }

    /**
     * Add URL to sitemap
     */
    private function addSitemapUrl($url, $lastmod, $changefreq, $priority)
    {
        return sprintf(
            "  <url>\n    <loc>%s</loc>\n    <lastmod>%s</lastmod>\n    <changefreq>%s</changefreq>\n    <priority>%s</priority>\n  </url>\n",
            htmlspecialchars($url),
            $lastmod->toISOString(),
            $changefreq,
            $priority
        );
    }

    /**
     * Clean and optimize title
     */
    private function cleanTitle($title)
    {
        $title = strip_tags($title);
        return strlen($title) > 60 ? substr($title, 0, 57) . '...' : $title;
    }

    /**
     * Clean and optimize description
     */
    private function cleanDescription($description)
    {
        $description = strip_tags($description);
        $description = preg_replace('/\s+/', ' ', $description);
        return strlen($description) > 160 ? substr($description, 0, 157) . '...' : $description;
    }

    /**
     * Generate robots.txt content
     */
    public function generateRobotsTxt()
    {
        $content = "User-agent: *\n";
        $content .= "Allow: /\n";
        $content .= "Disallow: /admin/\n";
        $content .= "Disallow: /dashboard/\n";
        $content .= "Disallow: /api/\n";
        $content .= "Disallow: /storage/\n";
        $content .= "\n";
        $content .= "Sitemap: " . route('sitemap') . "\n";

        return $content;
    }

    /**
     * Generate structured data for products
     */
    public function generateProductStructuredData($product)
    {
        return [
            '@context' => 'https://schema.org/',
            '@type' => 'Product',
            'name' => $product->name,
            'description' => strip_tags($product->description),
            'image' => $product->primary_image ? $product->primary_image->image_url : null,
            'brand' => [
                '@type' => 'Brand',
                'name' => config('app.name')
            ],
            'offers' => [
                '@type' => 'Offer',
                'price' => $product->price,
                'priceCurrency' => 'USD',
                'availability' => 'https://schema.org/InStock',
                'seller' => [
                    '@type' => 'Organization',
                    'name' => config('app.name')
                ]
            ],
            'category' => $product->category->name ?? 'Digital Template',
            'sku' => 'PROD-' . $product->id,
        ];
    }
}
