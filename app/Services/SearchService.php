<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SearchService
{
    /**
     * Perform advanced product search with full-text and filtering
     */
    public function searchProducts(Request $request)
    {
        $query = Product::with(['category', 'primaryImage', 'user'])
            ->where('status', 'published');

        // Apply search filters
        $this->applySearchFilters($query, $request);
        $this->applyAdvancedFilters($query, $request);
        $this->applySorting($query, $request);

        // Get pagination settings
        $perPage = $this->getPerPageValue($request);
        
        return $query->paginate($perPage)->withQueryString();
    }

    /**
     * Apply search filters (text search)
     */
    private function applySearchFilters(Builder $query, Request $request)
    {
        if (!$request->filled('q')) {
            return;
        }

        $searchTerm = trim($request->get('q'));
        
        if (strlen($searchTerm) < 2) {
            return;
        }

        // Use full-text search for better performance and relevance
        if ($this->supportsFullTextSearch()) {
            $this->applyFullTextSearch($query, $searchTerm);
        } else {
            $this->applyLikeSearch($query, $searchTerm);
        }
    }

    /**
     * Apply full-text search using MySQL MATCH AGAINST
     */
    private function applyFullTextSearch(Builder $query, string $searchTerm)
    {
        // Escape special characters for full-text search
        $escapedTerm = $this->escapeFullTextSearch($searchTerm);
        
        $query->whereRaw(
            "MATCH(title, short_description, description) AGAINST(? IN BOOLEAN MODE)",
            [$escapedTerm]
        )->orWhere(function ($q) use ($searchTerm) {
            // Also search in tags and category names
            $this->applyTagAndCategorySearch($q, $searchTerm);
        });

        // Order by relevance score
        $query->selectRaw(
            "products.*, MATCH(title, short_description, description) AGAINST(? IN BOOLEAN MODE) as relevance_score",
            [$escapedTerm]
        )->orderByDesc('relevance_score');
    }

    /**
     * Apply LIKE-based search for databases without full-text support
     */
    private function applyLikeSearch(Builder $query, string $searchTerm)
    {
        $query->where(function ($q) use ($searchTerm) {
            // Search in product fields with weighted relevance
            $q->where('title', 'like', "%{$searchTerm}%")
              ->orWhere('short_description', 'like', "%{$searchTerm}%")
              ->orWhere('description', 'like', "%{$searchTerm}%");
            
            $this->applyTagAndCategorySearch($q, $searchTerm);
        });

        // Order by title relevance first
        $query->orderByRaw("
            CASE 
                WHEN title LIKE ? THEN 1
                WHEN short_description LIKE ? THEN 2
                WHEN description LIKE ? THEN 3
                ELSE 4
            END
        ", ["%{$searchTerm}%", "%{$searchTerm}%", "%{$searchTerm}%"]);
    }

    /**
     * Apply tag and category search
     */
    private function applyTagAndCategorySearch(Builder $query, string $searchTerm)
    {
        // Tag search with JSON support
        if (strpos($searchTerm, ',') !== false) {
            $tags = array_map('trim', explode(',', $searchTerm));
            foreach ($tags as $tag) {
                $query->orWhereJsonContains('tags', $tag)
                      ->orWhere('tags', 'like', "%{$tag}%");
            }
        } else {
            $query->orWhereJsonContains('tags', $searchTerm)
                  ->orWhere('tags', 'like', "%{$searchTerm}%");
        }

        // Category name search
        $query->orWhereHas('category', function ($categoryQuery) use ($searchTerm) {
            $categoryQuery->where('name', 'like', "%{$searchTerm}%")
                         ->orWhere('description', 'like', "%{$searchTerm}%");
        });
    }

    /**
     * Apply advanced filters
     */
    private function applyAdvancedFilters(Builder $query, Request $request)
    {
        // Category filter
        if ($request->filled('category')) {
            $this->applyCategoryFilter($query, $request->get('category'));
        }

        // Price filters
        $this->applyPriceFilters($query, $request);

        // Feature filters
        if ($request->boolean('featured')) {
            $query->where('is_featured', true);
        }

        if ($request->boolean('trending')) {
            $query->where('is_trending', true);
        }

        // Tag filters
        if ($request->filled('tags')) {
            $this->applyTagFilters($query, $request->get('tags'));
        }

        // Date range filters
        $this->applyDateFilters($query, $request);

        // Rating filter (if implemented)
        if ($request->filled('min_rating')) {
            $query->where('average_rating', '>=', $request->get('min_rating'));
        }
    }

    /**
     * Apply category filter
     */
    private function applyCategoryFilter(Builder $query, $category)
    {
        if (is_numeric($category)) {
            $query->where('category_id', $category);
        } else {
            $categoryModel = Category::where('slug', $category)->first();
            if ($categoryModel) {
                $query->where('category_id', $categoryModel->id);
            }
        }
    }

    /**
     * Apply price filters
     */
    private function applyPriceFilters(Builder $query, Request $request)
    {
        // Price type filter
        if ($request->filled('price')) {
            $priceFilter = $request->get('price');
            if ($priceFilter === 'free') {
                $query->where('is_free', true);
            } elseif ($priceFilter === 'paid') {
                $query->where('is_free', false);
            } elseif (strpos($priceFilter, '-') !== false) {
                $this->applyPriceRange($query, $priceFilter);
            }
        }

        // Custom price range
        if ($request->filled('min_price')) {
            $query->where('is_free', false)
                  ->where('price', '>=', (float) $request->get('min_price'));
        }

        if ($request->filled('max_price')) {
            $query->where('is_free', false)
                  ->where('price', '<=', (float) $request->get('max_price'));
        }
    }

    /**
     * Apply price range filter
     */
    private function applyPriceRange(Builder $query, string $priceFilter)
    {
        $range = explode('-', $priceFilter);
        if (count($range) === 2) {
            $min = (float) $range[0];
            $max = (float) $range[1];
            $query->where('is_free', false)
                  ->whereBetween('price', [$min, $max]);
        }
    }

    /**
     * Apply tag filters
     */
    private function applyTagFilters(Builder $query, $tags)
    {
        if (is_string($tags)) {
            $tags = explode(',', $tags);
        }

        $tags = array_map('trim', $tags);
        $tags = array_filter($tags);

        if (!empty($tags)) {
            $query->where(function ($q) use ($tags) {
                foreach ($tags as $tag) {
                    $q->orWhereJsonContains('tags', $tag);
                }
            });
        }
    }

    /**
     * Apply date filters
     */
    private function applyDateFilters(Builder $query, Request $request)
    {
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        // Quick date filters
        if ($request->filled('date_range')) {
            $this->applyQuickDateFilter($query, $request->get('date_range'));
        }
    }

    /**
     * Apply quick date filters
     */
    private function applyQuickDateFilter(Builder $query, string $range)
    {
        switch ($range) {
            case 'today':
                $query->whereDate('created_at', today());
                break;
            case 'week':
                $query->where('created_at', '>=', now()->subWeek());
                break;
            case 'month':
                $query->where('created_at', '>=', now()->subMonth());
                break;
            case 'year':
                $query->where('created_at', '>=', now()->subYear());
                break;
        }
    }

    /**
     * Apply sorting
     */
    private function applySorting(Builder $query, Request $request)
    {
        $sort = $request->get('sort', 'latest');

        switch ($sort) {
            case 'price_low':
                $query->orderByRaw('is_free DESC, price ASC');
                break;
            case 'price_high':
                $query->orderByRaw('is_free ASC, price DESC');
                break;
            case 'popular':
                $query->orderBy('view_count', 'desc');
                break;
            case 'downloads':
                $query->orderBy('download_count', 'desc');
                break;
            case 'name':
                $query->orderBy('title', 'asc');
                break;
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'latest':
            default:
                // If we have relevance score from full-text search, use it
                if (!$query->getQuery()->orders) {
                    $query->orderBy('created_at', 'desc');
                }
                break;
        }
    }

    /**
     * Get pagination value
     */
    private function getPerPageValue(Request $request): int
    {
        $perPage = $request->get('per_page', 12);
        return in_array($perPage, [12, 24, 48, 96]) ? $perPage : 12;
    }

    /**
     * Check if database supports full-text search
     */
    private function supportsFullTextSearch(): bool
    {
        try {
            return DB::connection()->getDriverName() === 'mysql';
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Escape special characters for full-text search
     */
    private function escapeFullTextSearch(string $term): string
    {
        // Escape MySQL full-text special characters
        $term = str_replace(['+', '-', '>', '<', '(', ')', '~', '*', '"', '@'], '', $term);
        
        // Add wildcard for partial matching
        $words = explode(' ', trim($term));
        $words = array_filter($words);
        
        if (empty($words)) {
            return '';
        }

        // Add + prefix for required words and * suffix for partial matching
        $escapedWords = array_map(function ($word) {
            return '+' . $word . '*';
        }, $words);

        return implode(' ', $escapedWords);
    }
}
