<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Templates Cave Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options specific to Templates Cave
    | marketplace functionality.
    |
    */

    'site' => [
        'name' => env('APP_NAME', 'Templates Cave'),
        'tagline' => 'Digital Product Marketplace for Creatives',
        'description' => 'Discover and download high-quality digital assets such as themes, templates, and plugins.',
        'keywords' => 'templates, themes, plugins, digital products, marketplace',
        'logo' => '/images/logo.png',
        'favicon' => '/favicon.ico',
    ],

    'social' => [
        'facebook' => env('SOCIAL_FACEBOOK', '#'),
        'twitter' => env('SOCIAL_TWITTER', '#'),
        'linkedin' => env('SOCIAL_LINKEDIN', '#'),
        'youtube' => env('SOCIAL_YOUTUBE', '#'),
    ],

    'pagination' => [
        'products_per_page' => 12,
        'blog_posts_per_page' => 6,
        'admin_items_per_page' => 20,
    ],

    'uploads' => [
        'max_file_size' => 50 * 1024 * 1024, // 50MB in bytes
        'allowed_extensions' => ['zip', 'rar', '7z', 'tar', 'gz'],
        'image_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'max_images_per_product' => 10,
    ],

    'payments' => [
        'currency' => env('PAYMENT_CURRENCY', 'usd'),
        'currency_symbol' => env('PAYMENT_CURRENCY_SYMBOL', '$'),

        'bank_transfer' => [
            'enabled' => env('BANK_TRANSFER_ENABLED', false),
            'account_name' => env('BANK_ACCOUNT_NAME', 'Templates Cave'),
            'account_number' => env('BANK_ACCOUNT_NUMBER'),
            'bank_name' => env('BANK_NAME'),
            'bank_code' => env('BANK_CODE'),
            'swift_code' => env('BANK_SWIFT_CODE'),
            'iban' => env('BANK_IBAN'),
            'instructions' => env('BANK_TRANSFER_INSTRUCTIONS', 'Please include your order number in the transfer reference and email us the transfer receipt.'),
        ],
    ],

    'downloads' => [
        'max_attempts' => env('MAX_DOWNLOAD_ATTEMPTS', 5),
        'expiry_hours' => env('DOWNLOAD_EXPIRY_HOURS', 24),
        'track_downloads' => env('TRACK_DOWNLOADS', true),
    ],

    'features' => [
        'user_registration' => true,
        'vendor_submissions' => false, // Future feature
        'affiliate_program' => false, // Future feature
        'dark_mode' => true,
        'blog_enabled' => true,
        'comments_enabled' => true,
        'ratings_enabled' => false, // Future feature
    ],

    'seo' => [
        'meta_title_suffix' => ' - Templates Cave',
        'meta_description_default' => 'Discover and download high-quality digital assets such as themes, templates, and plugins at Templates Cave.',
        'sitemap_enabled' => true,
        'robots_txt_enabled' => true,
    ],

    'email' => [
        'admin_email' => env('ADMIN_EMAIL', '<EMAIL>'),
        'support_email' => env('SUPPORT_EMAIL', '<EMAIL>'),
        'noreply_email' => env('NOREPLY_EMAIL', '<EMAIL>'),
    ],

    'analytics' => [
        'google_analytics_id' => env('GOOGLE_ANALYTICS_ID', null),
        'track_downloads' => true,
        'track_views' => true,
    ],

    'cache' => [
        'popular_products_ttl' => 3600, // 1 hour
        'trending_products_ttl' => 1800, // 30 minutes
        'categories_ttl' => 7200, // 2 hours
    ],
];
