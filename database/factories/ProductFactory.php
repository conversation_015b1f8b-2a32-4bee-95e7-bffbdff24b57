<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->words(3, true);

        return [
            'user_id' => User::factory()->state(['role' => 'admin']),
            'category_id' => Category::factory(),
            'title' => $title,
            'slug' => Str::slug($title),
            'description' => $this->faker->paragraph(),
            'short_description' => $this->faker->sentence(),
            'price' => $this->faker->randomFloat(2, 5, 500),
            'is_free' => $this->faker->boolean(20),
            'demo_url' => $this->faker->optional()->url(),
            'file_urls' => $this->faker->optional()->randomElement([
                json_encode(['https://example.com/file1.zip']),
                json_encode(['https://dropbox.com/file.zip', 'https://drive.google.com/file.zip']),
            ]),
            'local_file_path' => $this->faker->optional()->filePath(),
            'tags' => json_encode($this->faker->words(3)),
            'status' => $this->faker->randomElement(['draft', 'published', 'archived']),
            'is_featured' => $this->faker->boolean(20),
            'is_trending' => $this->faker->boolean(10),
            'view_count' => $this->faker->numberBetween(0, 5000),
            'download_count' => $this->faker->numberBetween(0, 1000),
            'meta_title' => $title,
            'meta_description' => $this->faker->sentence(),
            'meta_keywords' => json_encode($this->faker->words(5)),
            'published_at' => $this->faker->optional()->dateTimeBetween('-1 year', 'now'),
        ];
    }
}
