<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('downloads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null'); // Null for free downloads
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->string('download_token')->unique(); // For secure download links
            $table->timestamp('expires_at')->nullable(); // For time-limited downloads
            $table->boolean('is_completed')->default(false);
            $table->timestamps();

            $table->index(['user_id', 'product_id']);
            $table->index(['product_id', 'created_at']);
            $table->index('download_token');
            $table->index('expires_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('downloads');
    }
};
