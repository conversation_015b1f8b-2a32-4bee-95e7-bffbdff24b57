<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update any existing vendor users to admin
        DB::table('users')->where('role', 'vendor')->update(['role' => 'admin']);

        // For SQLite, we need to recreate the table with the new enum constraint
        if (DB::getDriverName() === 'sqlite') {
            // SQLite approach: Add a check constraint (SQLite doesn't have native ENUM)
            // The original migration already handles this, so we just update existing data
        } else {
            // MySQL approach: Modify the enum
            DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'member') DEFAULT 'member'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (DB::getDriverName() !== 'sqlite') {
            // Restore the original enum with vendor role for MySQL
            DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'vendor', 'member') DEFAULT 'member'");
        }
    }
};
