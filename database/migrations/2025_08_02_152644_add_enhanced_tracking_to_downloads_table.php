<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('downloads', function (Blueprint $table) {
            $table->timestamp('downloaded_at')->nullable()->after('is_completed');
            $table->string('file_type')->nullable()->after('downloaded_at'); // 'local' or 'external'
            $table->string('file_path')->nullable()->after('file_type'); // For local files
            $table->bigInteger('file_size')->nullable()->after('file_path'); // File size in bytes
            $table->text('external_url')->nullable()->after('file_size'); // For external downloads

            $table->index('downloaded_at');
            $table->index('file_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('downloads', function (Blueprint $table) {
            $table->dropIndex(['downloaded_at']);
            $table->dropIndex(['file_type']);
            $table->dropColumn([
                'downloaded_at',
                'file_type',
                'file_path',
                'file_size',
                'external_url'
            ]);
        });
    }
};
