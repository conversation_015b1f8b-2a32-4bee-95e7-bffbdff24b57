<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('blog_posts', function (Blueprint $table) {
            // Add new fields for enhanced blog functionality
            $table->json('gallery_images')->nullable()->after('featured_image');
            $table->renameColumn('user_id', 'author_id');
            $table->foreignId('blog_category_id')->nullable()->constrained()->onDelete('set null')->after('author_id');
            $table->boolean('is_featured')->default(false)->after('status');
            $table->boolean('allow_comments')->default(true)->after('is_featured');
            $table->string('canonical_url')->nullable()->after('meta_keywords');
            $table->json('social_meta')->nullable()->after('canonical_url');
            $table->integer('comment_count')->default(0)->after('view_count');
            $table->integer('like_count')->default(0)->after('comment_count');

            // Add new indexes
            $table->index(['blog_category_id', 'status']);
            $table->index(['author_id', 'status']);
            $table->index('is_featured');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('blog_posts', function (Blueprint $table) {
            // Remove added fields
            $table->dropColumn([
                'gallery_images',
                'blog_category_id',
                'is_featured',
                'allow_comments',
                'canonical_url',
                'social_meta',
                'comment_count',
                'like_count'
            ]);

            // Rename back
            $table->renameColumn('author_id', 'user_id');

            // Drop indexes
            $table->dropIndex(['blog_category_id', 'status']);
            $table->dropIndex(['author_id', 'status']);
            $table->dropIndex(['is_featured']);
        });
    }
};
