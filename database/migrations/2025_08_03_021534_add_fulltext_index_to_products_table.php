<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add FULLTEXT index to products table for search functionality
        // Using raw SQL because Lara<PERSON>'s Blueprint doesn't support FULLTEXT indexes directly
        DB::statement('ALTER TABLE products ADD FULLTEXT search_index (title, short_description, description)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the FULLTEXT index
        DB::statement('ALTER TABLE products DROP INDEX search_index');
    }
};
