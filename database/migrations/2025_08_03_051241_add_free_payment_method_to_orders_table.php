<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Change the payment_method enum to include 'free' for free products
            $table->enum('payment_method', ['stripe', 'paypal', 'bank_transfer', 'free'])->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Revert back to original enum values
            $table->enum('payment_method', ['stripe', 'paypal', 'bank_transfer'])->nullable()->change();
        });
    }
};
