<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('comments', function (Blueprint $table) {
            // Add rating field for product reviews (1-5 stars)
            $table->tinyInteger('rating')->nullable()->after('content');
            $table->boolean('is_review')->default(false)->after('rating');
            $table->string('title')->nullable()->after('is_review'); // Review title
            
            // Add indexes for better performance
            $table->index(['commentable_type', 'commentable_id', 'is_review']);
            $table->index('rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('comments', function (Blueprint $table) {
            $table->dropIndex(['commentable_type', 'commentable_id', 'is_review']);
            $table->dropIndex(['rating']);
            
            $table->dropColumn(['rating', 'is_review', 'title']);
        });
    }
};
