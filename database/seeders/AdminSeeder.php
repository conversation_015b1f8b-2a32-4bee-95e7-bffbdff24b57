<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Setting;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );

        // Create basic settings
        $settings = [
            // General Settings
            ['key' => 'site_name', 'value' => 'Templates Cave', 'type' => 'string', 'group' => 'general'],
            ['key' => 'site_description', 'value' => 'Premium digital templates marketplace', 'type' => 'string', 'group' => 'general'],
            ['key' => 'site_keywords', 'value' => 'templates, digital, marketplace, design', 'type' => 'string', 'group' => 'general'],
            ['key' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'string', 'group' => 'general'],
            ['key' => 'contact_phone', 'value' => '+****************', 'type' => 'string', 'group' => 'general'],
            ['key' => 'contact_address', 'value' => '123 Business St, City, State 12345', 'type' => 'string', 'group' => 'general'],
            ['key' => 'timezone', 'value' => 'UTC', 'type' => 'string', 'group' => 'general'],
            ['key' => 'currency', 'value' => 'USD', 'type' => 'string', 'group' => 'general'],
            ['key' => 'currency_symbol', 'value' => '$', 'type' => 'string', 'group' => 'general'],
            
            // Marketplace Settings
            ['key' => 'commission_rate', 'value' => '10', 'type' => 'integer', 'group' => 'general'],
            ['key' => 'min_payout_amount', 'value' => '50', 'type' => 'integer', 'group' => 'general'],
            ['key' => 'auto_approve_products', 'value' => 'false', 'type' => 'boolean', 'group' => 'general'],
            ['key' => 'allow_guest_checkout', 'value' => 'true', 'type' => 'boolean', 'group' => 'general'],
            
            // Social Media
            ['key' => 'facebook_url', 'value' => '', 'type' => 'string', 'group' => 'general'],
            ['key' => 'twitter_url', 'value' => '', 'type' => 'string', 'group' => 'general'],
            ['key' => 'instagram_url', 'value' => '', 'type' => 'string', 'group' => 'general'],
            ['key' => 'linkedin_url', 'value' => '', 'type' => 'string', 'group' => 'general'],
            
            // Email Settings
            ['key' => 'mail_driver', 'value' => 'smtp', 'type' => 'string', 'group' => 'email'],
            ['key' => 'mail_host', 'value' => 'smtp.mailtrap.io', 'type' => 'string', 'group' => 'email'],
            ['key' => 'mail_port', 'value' => '587', 'type' => 'string', 'group' => 'email'],
            ['key' => 'mail_username', 'value' => '', 'type' => 'string', 'group' => 'email'],
            ['key' => 'mail_password', 'value' => '', 'type' => 'string', 'group' => 'email'],
            ['key' => 'mail_encryption', 'value' => 'tls', 'type' => 'string', 'group' => 'email'],
            ['key' => 'mail_from_address', 'value' => '<EMAIL>', 'type' => 'string', 'group' => 'email'],
            ['key' => 'mail_from_name', 'value' => 'Templates Cave', 'type' => 'string', 'group' => 'email'],
            
            // Payment Settings
            ['key' => 'stripe_enabled', 'value' => 'false', 'type' => 'boolean', 'group' => 'payment'],
            ['key' => 'stripe_public_key', 'value' => '', 'type' => 'string', 'group' => 'payment'],
            ['key' => 'stripe_secret_key', 'value' => '', 'type' => 'string', 'group' => 'payment'],
            ['key' => 'paypal_enabled', 'value' => 'false', 'type' => 'boolean', 'group' => 'payment'],
            ['key' => 'paypal_client_id', 'value' => '', 'type' => 'string', 'group' => 'payment'],
            ['key' => 'paypal_client_secret', 'value' => '', 'type' => 'string', 'group' => 'payment'],
            ['key' => 'paypal_mode', 'value' => 'sandbox', 'type' => 'string', 'group' => 'payment'],
            ['key' => 'bank_transfer_enabled', 'value' => 'true', 'type' => 'boolean', 'group' => 'payment'],
            ['key' => 'bank_transfer_instructions', 'value' => 'Please contact us for bank transfer details.', 'type' => 'string', 'group' => 'payment'],
            
            // SEO Settings
            ['key' => 'meta_title', 'value' => 'Templates Cave - Premium Digital Templates', 'type' => 'string', 'group' => 'seo'],
            ['key' => 'meta_description', 'value' => 'Discover premium digital templates for your projects. High-quality designs for websites, presentations, and more.', 'type' => 'string', 'group' => 'seo'],
            ['key' => 'meta_keywords', 'value' => 'templates, digital, premium, design, marketplace', 'type' => 'string', 'group' => 'seo'],
            ['key' => 'google_analytics_id', 'value' => '', 'type' => 'string', 'group' => 'seo'],
            ['key' => 'google_search_console', 'value' => '', 'type' => 'string', 'group' => 'seo'],
            ['key' => 'facebook_pixel_id', 'value' => '', 'type' => 'string', 'group' => 'seo'],
            ['key' => 'og_title', 'value' => 'Templates Cave - Premium Digital Templates', 'type' => 'string', 'group' => 'seo'],
            ['key' => 'og_description', 'value' => 'Discover premium digital templates for your projects.', 'type' => 'string', 'group' => 'seo'],
            ['key' => 'og_image', 'value' => '', 'type' => 'string', 'group' => 'seo'],
            ['key' => 'twitter_card_type', 'value' => 'summary_large_image', 'type' => 'string', 'group' => 'seo'],
            ['key' => 'twitter_site', 'value' => '', 'type' => 'string', 'group' => 'seo'],
        ];

        foreach ($settings as $setting) {
            Setting::firstOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        $this->command->info('Admin user and basic settings created successfully!');
        $this->command->info('Admin Login: <EMAIL>');
        $this->command->info('Admin Password: password');
    }
}
