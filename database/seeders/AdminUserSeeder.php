<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        DB::table('users')->insert([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
            'bio' => 'Administrator of Templates Cave marketplace',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);



        // Create sample member user
        DB::table('users')->insert([
            'name' => 'Jane Member',
            'email' => '<EMAIL>',
            'role' => 'member',
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
            'bio' => 'Web designer looking for quality templates',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
