<?php

namespace Database\Seeders;

use App\Models\BlogCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BlogCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Web Design',
                'slug' => 'web-design',
                'description' => 'Latest trends and techniques in web design, UI/UX best practices, and design inspiration.',
                'color' => '#3B82F6',
                'icon' => 'fas fa-palette',
                'is_active' => true,
                'sort_order' => 1,
                'meta_title' => 'Web Design Articles & Tutorials',
                'meta_description' => 'Discover the latest web design trends, UI/UX best practices, and design inspiration for modern websites.',
            ],
            [
                'name' => 'Development',
                'slug' => 'development',
                'description' => 'Programming tutorials, coding best practices, and development tools for modern web applications.',
                'color' => '#10B981',
                'icon' => 'fas fa-code',
                'is_active' => true,
                'sort_order' => 2,
                'meta_title' => 'Web Development Tutorials & Tips',
                'meta_description' => 'Learn web development with our comprehensive tutorials covering HTML, CSS, JavaScript, PHP, and more.',
            ],
            [
                'name' => 'Templates',
                'slug' => 'templates',
                'description' => 'Template showcases, customization guides, and tips for choosing the right template for your project.',
                'color' => '#8B5CF6',
                'icon' => 'fas fa-layer-group',
                'is_active' => true,
                'sort_order' => 3,
                'meta_title' => 'Template Guides & Showcases',
                'meta_description' => 'Explore our template collection with detailed guides, customization tips, and project showcases.',
            ],
            [
                'name' => 'Business',
                'slug' => 'business',
                'description' => 'Business insights, marketing strategies, and entrepreneurship tips for digital creators.',
                'color' => '#F59E0B',
                'icon' => 'fas fa-briefcase',
                'is_active' => true,
                'sort_order' => 4,
                'meta_title' => 'Business & Marketing Insights',
                'meta_description' => 'Get business insights, marketing strategies, and entrepreneurship tips for digital creators and agencies.',
            ],
            [
                'name' => 'Tutorials',
                'slug' => 'tutorials',
                'description' => 'Step-by-step tutorials and how-to guides for web development and design.',
                'color' => '#EF4444',
                'icon' => 'fas fa-graduation-cap',
                'is_active' => true,
                'sort_order' => 5,
                'meta_title' => 'Web Development & Design Tutorials',
                'meta_description' => 'Follow our step-by-step tutorials and how-to guides for web development and design mastery.',
            ],
            [
                'name' => 'News',
                'slug' => 'news',
                'description' => 'Latest news and updates from the web development and design industry.',
                'color' => '#6366F1',
                'icon' => 'fas fa-newspaper',
                'is_active' => true,
                'sort_order' => 6,
                'meta_title' => 'Web Industry News & Updates',
                'meta_description' => 'Stay updated with the latest news and trends in web development, design, and digital technology.',
            ],
        ];

        foreach ($categories as $category) {
            BlogCategory::create($category);
        }
    }
}
