<?php

namespace Database\Seeders;

use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BlogPostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('role', 'admin')->first();
        $categories = BlogCategory::all();

        $posts = [
            [
                'title' => 'Modern Web Design Trends for 2025',
                'slug' => 'modern-web-design-trends-2025',
                'excerpt' => 'Discover the latest web design trends that will dominate 2025, from minimalist layouts to interactive animations.',
                'content' => $this->getWebDesignContent(),
                'author_id' => $admin->id,
                'blog_category_id' => $categories->where('slug', 'web-design')->first()->id,
                'status' => 'published',
                'is_featured' => true,
                'allow_comments' => true,
                'tags' => ['web design', 'trends', '2025', 'UI/UX', 'minimalism'],
                'meta_title' => 'Modern Web Design Trends for 2025 - Templates Cave',
                'meta_description' => 'Explore the cutting-edge web design trends for 2025 including minimalist layouts, dark mode, and interactive animations.',
                'meta_keywords' => ['web design', 'trends', '2025', 'UI', 'UX', 'minimalism'],
                'view_count' => 245,
                'comment_count' => 12,
                'like_count' => 38,
                'published_at' => now()->subDays(2),
            ],
            [
                'title' => 'Getting Started with Laravel 11: A Complete Guide',
                'slug' => 'getting-started-laravel-11-complete-guide',
                'excerpt' => 'Learn how to build modern web applications with Laravel 11, covering installation, routing, and best practices.',
                'content' => $this->getLaravelContent(),
                'author_id' => $admin->id,
                'blog_category_id' => $categories->where('slug', 'development')->first()->id,
                'status' => 'published',
                'is_featured' => true,
                'allow_comments' => true,
                'tags' => ['Laravel', 'PHP', 'development', 'tutorial', 'framework'],
                'meta_title' => 'Laravel 11 Complete Guide - Templates Cave',
                'meta_description' => 'Master Laravel 11 with our comprehensive guide covering installation, routing, models, and advanced features.',
                'meta_keywords' => ['Laravel', 'PHP', 'framework', 'tutorial', 'web development'],
                'view_count' => 189,
                'comment_count' => 8,
                'like_count' => 25,
                'published_at' => now()->subDays(5),
            ],
            [
                'title' => 'Choosing the Perfect Template for Your Business',
                'slug' => 'choosing-perfect-template-business',
                'excerpt' => 'A comprehensive guide to selecting the right template that aligns with your business goals and brand identity.',
                'content' => $this->getTemplateContent(),
                'author_id' => $admin->id,
                'blog_category_id' => $categories->where('slug', 'templates')->first()->id,
                'status' => 'published',
                'is_featured' => false,
                'allow_comments' => true,
                'tags' => ['templates', 'business', 'branding', 'selection guide'],
                'meta_title' => 'How to Choose the Perfect Template for Your Business',
                'meta_description' => 'Learn how to select the ideal template for your business with our comprehensive selection guide.',
                'meta_keywords' => ['templates', 'business', 'selection', 'guide', 'branding'],
                'view_count' => 156,
                'comment_count' => 6,
                'like_count' => 19,
                'published_at' => now()->subDays(7),
            ],
            [
                'title' => 'Building a Successful Digital Agency in 2025',
                'slug' => 'building-successful-digital-agency-2025',
                'excerpt' => 'Essential strategies and insights for starting and growing a profitable digital agency in today\'s competitive market.',
                'content' => $this->getBusinessContent(),
                'author_id' => $admin->id,
                'blog_category_id' => $categories->where('slug', 'business')->first()->id,
                'status' => 'published',
                'is_featured' => false,
                'allow_comments' => true,
                'tags' => ['digital agency', 'business', 'entrepreneurship', 'strategy'],
                'meta_title' => 'Building a Successful Digital Agency in 2025',
                'meta_description' => 'Discover proven strategies for starting and scaling a profitable digital agency in 2025.',
                'meta_keywords' => ['digital agency', 'business', 'entrepreneurship', 'strategy', '2025'],
                'view_count' => 203,
                'comment_count' => 15,
                'like_count' => 42,
                'published_at' => now()->subDays(10),
            ],
            [
                'title' => 'CSS Grid vs Flexbox: When to Use Each',
                'slug' => 'css-grid-vs-flexbox-when-to-use',
                'excerpt' => 'Understanding the differences between CSS Grid and Flexbox, and knowing when to use each layout method.',
                'content' => $this->getCSSContent(),
                'author_id' => $admin->id,
                'blog_category_id' => $categories->where('slug', 'tutorials')->first()->id,
                'status' => 'published',
                'is_featured' => false,
                'allow_comments' => true,
                'tags' => ['CSS', 'Grid', 'Flexbox', 'layout', 'tutorial'],
                'meta_title' => 'CSS Grid vs Flexbox: Complete Comparison Guide',
                'meta_description' => 'Learn when to use CSS Grid vs Flexbox with practical examples and best practices.',
                'meta_keywords' => ['CSS', 'Grid', 'Flexbox', 'layout', 'comparison'],
                'view_count' => 178,
                'comment_count' => 9,
                'like_count' => 31,
                'published_at' => now()->subDays(12),
            ],
        ];

        foreach ($posts as $post) {
            BlogPost::create($post);
        }
    }

    private function getWebDesignContent()
    {
        return '<h2>The Evolution of Web Design</h2>
<p>Web design continues to evolve at a rapid pace, with 2025 bringing exciting new trends that prioritize user experience, accessibility, and visual appeal. In this comprehensive guide, we\'ll explore the most significant trends shaping the digital landscape.</p>

<h3>1. Minimalist Design with Maximum Impact</h3>
<p>Minimalism remains a dominant force in web design, but 2025 takes it to new heights. Clean layouts, ample white space, and strategic use of typography create powerful visual hierarchies that guide users effortlessly through content.</p>

<h3>2. Dark Mode as Standard</h3>
<p>Dark mode is no longer an afterthought—it\'s becoming the default choice for many websites. This trend reduces eye strain, saves battery life on mobile devices, and creates a sophisticated, modern aesthetic.</p>

<h3>3. Interactive Animations and Micro-interactions</h3>
<p>Subtle animations and micro-interactions enhance user engagement without overwhelming the experience. From hover effects to loading animations, these elements add personality and feedback to user actions.</p>

<h3>4. Accessibility-First Design</h3>
<p>2025 marks a turning point where accessibility is built into the design process from the beginning, not added as an afterthought. This includes proper color contrast, keyboard navigation, and screen reader compatibility.</p>

<h2>Implementation Tips</h2>
<p>To implement these trends effectively, consider your target audience, brand identity, and technical constraints. Remember that trends should enhance, not overshadow, your content and user experience.</p>';
    }

    private function getLaravelContent()
    {
        return '<h2>Introduction to Laravel 11</h2>
<p>Laravel 11 represents a significant milestone in PHP web development, offering enhanced performance, improved developer experience, and powerful new features that streamline application development.</p>

<h3>Installation and Setup</h3>
<p>Getting started with Laravel 11 is straightforward. Use Composer to create a new project:</p>
<pre><code>composer create-project laravel/laravel my-app</code></pre>

<h3>Key Features in Laravel 11</h3>
<ul>
<li><strong>Improved Performance:</strong> Faster routing and optimized query handling</li>
<li><strong>Enhanced Security:</strong> Better CSRF protection and input validation</li>
<li><strong>Developer Tools:</strong> Improved debugging and testing capabilities</li>
<li><strong>Modern PHP:</strong> Full support for PHP 8.3 features</li>
</ul>

<h3>Building Your First Application</h3>
<p>Start with the basics: routing, controllers, and views. Laravel\'s MVC architecture makes it easy to organize your code and maintain clean separation of concerns.</p>

<h3>Best Practices</h3>
<p>Follow Laravel conventions, use Eloquent ORM effectively, implement proper validation, and leverage Laravel\'s built-in security features to build robust applications.</p>';
    }

    private function getTemplateContent()
    {
        return '<h2>Understanding Your Business Needs</h2>
<p>Selecting the right template is crucial for your business success. The template you choose will serve as the foundation for your online presence, affecting everything from user experience to search engine optimization.</p>

<h3>Key Factors to Consider</h3>
<ul>
<li><strong>Industry Alignment:</strong> Choose templates designed for your specific industry</li>
<li><strong>Responsive Design:</strong> Ensure mobile compatibility across all devices</li>
<li><strong>Customization Options:</strong> Look for flexible layouts and color schemes</li>
<li><strong>Performance:</strong> Fast-loading templates improve user experience and SEO</li>
<li><strong>Support and Updates:</strong> Regular updates and reliable support are essential</li>
</ul>

<h3>Template Categories</h3>
<p>Different business types require different template approaches. E-commerce sites need product showcases and shopping carts, while service businesses focus on portfolios and contact forms.</p>

<h3>Making the Final Decision</h3>
<p>Test templates thoroughly, consider long-term scalability, and ensure the template aligns with your brand identity and business goals.</p>';
    }

    private function getBusinessContent()
    {
        return '<h2>The Digital Agency Landscape in 2025</h2>
<p>The digital agency market continues to grow, presenting both opportunities and challenges for new entrepreneurs. Success requires strategic planning, exceptional service delivery, and adaptability to changing market conditions.</p>

<h3>Essential Services to Offer</h3>
<ul>
<li><strong>Web Development:</strong> Custom websites and web applications</li>
<li><strong>Digital Marketing:</strong> SEO, social media, and content marketing</li>
<li><strong>Design Services:</strong> Branding, UI/UX, and graphic design</li>
<li><strong>Consulting:</strong> Digital strategy and technology advisory</li>
</ul>

<h3>Building Your Team</h3>
<p>Start with core competencies and gradually expand. Focus on hiring skilled professionals who share your vision and commitment to quality.</p>

<h3>Client Acquisition Strategies</h3>
<p>Develop a strong portfolio, leverage networking opportunities, and invest in content marketing to establish thought leadership in your niche.</p>

<h3>Scaling Your Operations</h3>
<p>Implement efficient processes, use project management tools, and maintain high service standards as you grow.</p>';
    }

    private function getCSSContent()
    {
        return '<h2>Understanding CSS Layout Methods</h2>
<p>CSS Grid and Flexbox are powerful layout tools that solve different problems. Understanding when to use each will make you a more effective developer.</p>

<h3>CSS Grid: Two-Dimensional Layouts</h3>
<p>CSS Grid excels at creating complex, two-dimensional layouts. Use Grid when you need to control both rows and columns simultaneously.</p>
<pre><code>.grid-container {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  grid-template-rows: auto 1fr auto;
  gap: 20px;
}</code></pre>

<h3>Flexbox: One-Dimensional Layouts</h3>
<p>Flexbox is perfect for one-dimensional layouts, whether in a row or column. It\'s ideal for component-level layouts and alignment.</p>
<pre><code>.flex-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}</code></pre>

<h3>When to Use Each</h3>
<ul>
<li><strong>Use Grid for:</strong> Page layouts, complex arrangements, overlapping elements</li>
<li><strong>Use Flexbox for:</strong> Navigation bars, card layouts, centering content</li>
</ul>

<h3>Combining Both Methods</h3>
<p>The most powerful approach is using Grid and Flexbox together, leveraging each method\'s strengths for optimal layouts.</p>';
    }
}
