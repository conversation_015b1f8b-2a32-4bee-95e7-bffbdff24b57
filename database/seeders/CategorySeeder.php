<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'WordPress Themes',
                'slug' => 'wordpress-themes',
                'description' => 'Professional WordPress themes for all types of websites',
                'icon' => 'wordpress',
                'color' => '#21759B',
                'sort_order' => 1,
            ],
            [
                'name' => 'WordPress Plugins',
                'slug' => 'wordpress-plugins',
                'description' => 'Extend your WordPress site with powerful plugins',
                'icon' => 'plugin',
                'color' => '#00A0D2',
                'sort_order' => 2,
            ],
            [
                'name' => 'Template Kits',
                'slug' => 'template-kits',
                'description' => 'Complete design packages for various platforms',
                'icon' => 'template',
                'color' => '#FF6B6B',
                'sort_order' => 3,
            ],
            [
                'name' => 'Scripts & Code',
                'slug' => 'scripts-code',
                'description' => 'Useful scripts and code snippets for developers',
                'icon' => 'code',
                'color' => '#4ECDC4',
                'sort_order' => 4,
            ],
            [
                'name' => 'Mobile Apps',
                'slug' => 'mobile-apps',
                'description' => 'Mobile application templates and source codes',
                'icon' => 'mobile',
                'color' => '#45B7D1',
                'sort_order' => 5,
            ],
            [
                'name' => 'HTML Templates',
                'slug' => 'html-templates',
                'description' => 'Clean and modern HTML/CSS templates',
                'icon' => 'html',
                'color' => '#E67E22',
                'sort_order' => 6,
            ],
            [
                'name' => 'Figma Templates',
                'slug' => 'figma-templates',
                'description' => 'Professional Figma design templates',
                'icon' => 'figma',
                'color' => '#F24E1E',
                'sort_order' => 7,
            ],
            [
                'name' => 'Admin Templates',
                'slug' => 'admin-templates',
                'description' => 'Dashboard and admin panel templates',
                'icon' => 'dashboard',
                'color' => '#9B59B6',
                'sort_order' => 8,
            ],
        ];

        foreach ($categories as $category) {
            DB::table('categories')->insert([
                'name' => $category['name'],
                'slug' => $category['slug'],
                'description' => $category['description'],
                'icon' => $category['icon'],
                'color' => $category['color'],
                'sort_order' => $category['sort_order'],
                'is_active' => true,
                'meta_title' => $category['name'] . ' - Templates Cave',
                'meta_description' => $category['description'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
