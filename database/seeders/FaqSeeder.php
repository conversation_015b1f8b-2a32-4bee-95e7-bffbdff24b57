<?php

namespace Database\Seeders;

use App\Models\Faq;
use Illuminate\Database\Seeder;

class FaqSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faqs = [
            [
                'question' => 'What types of templates do you offer?',
                'answer' => 'We offer a wide variety of templates including website templates, landing pages, admin dashboards, email templates, and mobile app UI kits. All templates are modern, responsive, and built with the latest web technologies.',
                'category' => 'General',
                'sort_order' => 1,
            ],
            [
                'question' => 'Are the templates responsive and mobile-friendly?',
                'answer' => 'Yes! All our templates are fully responsive and optimized for mobile devices. They work perfectly on desktops, tablets, and smartphones, ensuring your website looks great on any screen size.',
                'category' => 'Technical',
                'sort_order' => 2,
            ],
            [
                'question' => 'Can I customize the templates?',
                'answer' => 'Absolutely! Our templates are designed to be easily customizable. You can modify colors, fonts, layouts, and content to match your brand. Most templates come with detailed documentation to help you customize them.',
                'category' => 'Customization',
                'sort_order' => 3,
            ],
            [
                'question' => 'What is your refund policy?',
                'answer' => 'We offer a 30-day money-back guarantee. If you\'re not satisfied with your purchase, you can request a full refund within 30 days of purchase. Please contact our support team for assistance.',
                'category' => 'Billing',
                'sort_order' => 4,
            ],
            [
                'question' => 'Do you provide support for the templates?',
                'answer' => 'Yes, we provide comprehensive support for all our templates. This includes documentation, installation guides, and email support. Premium templates also include priority support and free updates.',
                'category' => 'Support',
                'sort_order' => 5,
            ],
            [
                'question' => 'Can I use the templates for commercial projects?',
                'answer' => 'Yes, all our templates come with a commercial license that allows you to use them for both personal and commercial projects. You can use them for client work, your own business, or any commercial purpose.',
                'category' => 'Licensing',
                'sort_order' => 6,
            ],
            [
                'question' => 'How do I download my purchased templates?',
                'answer' => 'After completing your purchase, you\'ll receive an email with download links. You can also access your downloads from your account dashboard. All files are available for immediate download.',
                'category' => 'Downloads',
                'sort_order' => 7,
            ],
            [
                'question' => 'Do you offer free templates?',
                'answer' => 'Yes! We have a selection of free templates available for download. These are fully functional templates that you can use for your projects. Check our free templates section for the latest offerings.',
                'category' => 'General',
                'sort_order' => 8,
            ],
        ];

        foreach ($faqs as $faq) {
            Faq::create($faq);
        }
    }
}
