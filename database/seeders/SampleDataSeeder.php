<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Category;
use App\Models\Product;
use App\Models\Order;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample users
        $users = [];
        for ($i = 1; $i <= 10; $i++) {
            $users[] = User::firstOrCreate(
                ['email' => "user{$i}@example.com"],
                [
                    'name' => "User {$i}",
                    'email' => "user{$i}@example.com",
                    'password' => Hash::make('password'),
                    'role' => 'member',
                    'email_verified_at' => now(),
                ]
            );
        }

        // Create sample categories
        $categories = [
            ['name' => 'Website Templates', 'slug' => 'website-templates', 'description' => 'Professional website templates'],
            ['name' => 'Presentation Templates', 'slug' => 'presentation-templates', 'description' => 'PowerPoint and Keynote templates'],
            ['name' => 'Graphics & Design', 'slug' => 'graphics-design', 'description' => 'Logos, icons, and design elements'],
            ['name' => 'WordPress Themes', 'slug' => 'wordpress-themes', 'description' => 'Premium WordPress themes'],
            ['name' => 'Mobile App Templates', 'slug' => 'mobile-app-templates', 'description' => 'iOS and Android app templates'],
        ];

        $categoryModels = [];
        foreach ($categories as $category) {
            $categoryModels[] = Category::firstOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }

        // Create sample products
        $productTitles = [
            'Modern Business Website Template',
            'Creative Portfolio Template',
            'E-commerce Store Template',
            'Corporate Presentation Template',
            'Startup Pitch Deck Template',
            'Restaurant Website Template',
            'Photography Portfolio Template',
            'Real Estate Website Template',
            'Fashion Store Template',
            'Tech Startup Template',
            'Medical Clinic Template',
            'Education Platform Template',
            'Travel Agency Template',
            'Fitness Gym Template',
            'Digital Agency Template',
        ];

        $products = [];
        foreach ($productTitles as $index => $title) {
            $category = $categoryModels[array_rand($categoryModels)];
            $user = $users[array_rand($users)];
            $price = rand(19, 199);

            $products[] = Product::create([
                'title' => $title,
                'slug' => Str::slug($title) . '-' . rand(1000, 9999), // Add random suffix to avoid duplicates
                'description' => "Professional {$title} with modern design and responsive layout. Perfect for businesses looking to establish a strong online presence.\n\nThis template includes:\n- Responsive design\n- Modern layout\n- Easy customization\n- Cross-browser compatibility\n- SEO optimized\n- Documentation included",
                'short_description' => "Professional {$title} with modern design and responsive layout.",
                'price' => $price,
                'category_id' => $category->id,
                'user_id' => $user->id,
                'status' => rand(0, 10) > 2 ? 'published' : 'draft', // 80% published
                'is_featured' => rand(0, 10) > 7, // 30% featured
                'download_count' => rand(0, 500),
                'view_count' => rand(0, 1000),
                'meta_title' => $title,
                'meta_description' => "Download {$title} - Professional template for your business",
                'meta_keywords' => ['template', 'design', 'website', 'professional'],
                'published_at' => rand(0, 10) > 2 ? now()->subDays(rand(1, 30)) : null,
            ]);
        }

        // Create sample orders
        for ($i = 1; $i <= 25; $i++) {
            $user = $users[array_rand($users)];
            $product = $products[array_rand($products)];
            $status = ['pending', 'completed', 'failed'];
            $paymentMethods = ['stripe', 'paypal', 'bank_transfer'];
            
            $orderStatus = $status[array_rand($status)];
            
            Order::create([
                'order_number' => 'ORD-' . strtoupper(Str::random(8)),
                'user_id' => $user->id,
                'product_id' => $product->id,
                'amount' => $product->price,
                'currency' => 'USD',
                'status' => $orderStatus,
                'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                'payment_id' => $orderStatus === 'completed' ? 'pay_' . Str::random(10) : null,
                'paid_at' => $orderStatus === 'completed' ? now()->subDays(rand(1, 30)) : null,
                'created_at' => now()->subDays(rand(1, 60)),
                'updated_at' => now()->subDays(rand(1, 60)),
            ]);
        }

        $this->command->info('Sample data created successfully!');
        $this->command->info('Created:');
        $this->command->info('- 10 sample users');
        $this->command->info('- 5 categories');
        $this->command->info('- 15 products');
        $this->command->info('- 25 orders');
    }
}
