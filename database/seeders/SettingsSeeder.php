<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            ['key' => 'site_name', 'value' => 'Templates Cave', 'type' => 'string', 'group' => 'general', 'description' => 'Site name'],
            ['key' => 'site_description', 'value' => 'Premium templates and digital assets marketplace', 'type' => 'string', 'group' => 'general', 'description' => 'Site description'],
            ['key' => 'site_keywords', 'value' => 'templates, themes, graphics, digital assets', 'type' => 'string', 'group' => 'general', 'description' => 'Site keywords'],
            ['key' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'string', 'group' => 'general', 'description' => 'Contact email'],
            ['key' => 'contact_phone', 'value' => '+****************', 'type' => 'string', 'group' => 'general', 'description' => 'Contact phone'],
            ['key' => 'contact_address', 'value' => '123 Business St, City, State 12345', 'type' => 'string', 'group' => 'general', 'description' => 'Contact address'],
            ['key' => 'timezone', 'value' => 'UTC', 'type' => 'string', 'group' => 'general', 'description' => 'Default timezone'],
            ['key' => 'default_currency', 'value' => 'USD', 'type' => 'string', 'group' => 'general', 'description' => 'Default currency'],
            ['key' => 'commission_rate', 'value' => '10', 'type' => 'integer', 'group' => 'general', 'description' => 'Commission rate percentage'],
            
            // Social Media
            ['key' => 'facebook_url', 'value' => '', 'type' => 'string', 'group' => 'general', 'description' => 'Facebook URL'],
            ['key' => 'twitter_url', 'value' => '', 'type' => 'string', 'group' => 'general', 'description' => 'Twitter URL'],
            ['key' => 'instagram_url', 'value' => '', 'type' => 'string', 'group' => 'general', 'description' => 'Instagram URL'],
            ['key' => 'linkedin_url', 'value' => '', 'type' => 'string', 'group' => 'general', 'description' => 'LinkedIn URL'],
            ['key' => 'youtube_url', 'value' => '', 'type' => 'string', 'group' => 'general', 'description' => 'YouTube URL'],
            
            // Email Settings
            ['key' => 'mail_driver', 'value' => 'smtp', 'type' => 'string', 'group' => 'email', 'description' => 'Mail driver'],
            ['key' => 'mail_host', 'value' => 'smtp.gmail.com', 'type' => 'string', 'group' => 'email', 'description' => 'SMTP host'],
            ['key' => 'mail_port', 'value' => '587', 'type' => 'string', 'group' => 'email', 'description' => 'SMTP port'],
            ['key' => 'mail_username', 'value' => '', 'type' => 'string', 'group' => 'email', 'description' => 'SMTP username'],
            ['key' => 'mail_password', 'value' => '', 'type' => 'string', 'group' => 'email', 'description' => 'SMTP password'],
            ['key' => 'mail_encryption', 'value' => 'tls', 'type' => 'string', 'group' => 'email', 'description' => 'SMTP encryption'],
            ['key' => 'mail_from_address', 'value' => '<EMAIL>', 'type' => 'string', 'group' => 'email', 'description' => 'From email address'],
            ['key' => 'mail_from_name', 'value' => 'Templates Cave', 'type' => 'string', 'group' => 'email', 'description' => 'From name'],
            
            // Payment Settings
            ['key' => 'stripe_enabled', 'value' => '1', 'type' => 'boolean', 'group' => 'payment', 'description' => 'Enable Stripe payments'],
            ['key' => 'stripe_publishable_key', 'value' => '', 'type' => 'string', 'group' => 'payment', 'description' => 'Stripe publishable key'],
            ['key' => 'stripe_secret_key', 'value' => '', 'type' => 'string', 'group' => 'payment', 'description' => 'Stripe secret key'],
            ['key' => 'paypal_enabled', 'value' => '1', 'type' => 'boolean', 'group' => 'payment', 'description' => 'Enable PayPal payments'],
            ['key' => 'paypal_client_id', 'value' => '', 'type' => 'string', 'group' => 'payment', 'description' => 'PayPal client ID'],
            ['key' => 'paypal_client_secret', 'value' => '', 'type' => 'string', 'group' => 'payment', 'description' => 'PayPal client secret'],
            ['key' => 'paypal_mode', 'value' => 'sandbox', 'type' => 'string', 'group' => 'payment', 'description' => 'PayPal mode (sandbox/live)'],
            ['key' => 'bank_transfer_enabled', 'value' => '1', 'type' => 'boolean', 'group' => 'payment', 'description' => 'Enable bank transfer'],
            ['key' => 'bank_name', 'value' => '', 'type' => 'string', 'group' => 'payment', 'description' => 'Bank name'],
            ['key' => 'bank_account_name', 'value' => '', 'type' => 'string', 'group' => 'payment', 'description' => 'Bank account name'],
            ['key' => 'bank_account_number', 'value' => '', 'type' => 'string', 'group' => 'payment', 'description' => 'Bank account number'],
            ['key' => 'bank_routing_number', 'value' => '', 'type' => 'string', 'group' => 'payment', 'description' => 'Bank routing number'],
            
            // SEO Settings
            ['key' => 'meta_title', 'value' => 'Templates Cave - Premium Digital Assets Marketplace', 'type' => 'string', 'group' => 'seo', 'description' => 'Default meta title'],
            ['key' => 'meta_description', 'value' => 'Discover premium templates, themes, and digital assets at Templates Cave. High-quality designs for your projects.', 'type' => 'string', 'group' => 'seo', 'description' => 'Default meta description'],
            ['key' => 'meta_keywords', 'value' => 'templates, themes, graphics, digital assets, marketplace', 'type' => 'string', 'group' => 'seo', 'description' => 'Default meta keywords'],
            ['key' => 'google_analytics_id', 'value' => '', 'type' => 'string', 'group' => 'seo', 'description' => 'Google Analytics tracking ID'],
            ['key' => 'google_search_console', 'value' => '', 'type' => 'string', 'group' => 'seo', 'description' => 'Google Search Console verification'],
            ['key' => 'bing_verification', 'value' => '', 'type' => 'string', 'group' => 'seo', 'description' => 'Bing verification code'],
            ['key' => 'facebook_app_id', 'value' => '', 'type' => 'string', 'group' => 'seo', 'description' => 'Facebook App ID for Open Graph'],
            ['key' => 'twitter_site', 'value' => '', 'type' => 'string', 'group' => 'seo', 'description' => 'Twitter site handle'],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
