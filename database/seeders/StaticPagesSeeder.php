<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Page;

class StaticPagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pages = [
            [
                'title' => 'Help Center',
                'slug' => 'help-center',
                'content' => $this->getHelpCenterContent(),
                'status' => 'published',
                'meta_title' => 'Help Center - Templates Cave Support',
                'meta_description' => 'Find answers to frequently asked questions, user guides, and troubleshooting tips for Templates Cave marketplace.',
                'meta_keywords' => ['help', 'support', 'faq', 'user guide', 'troubleshooting', 'templates'],
                'show_in_footer' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'Terms of Service',
                'slug' => 'terms-of-service',
                'content' => $this->getTermsOfServiceContent(),
                'status' => 'published',
                'meta_title' => 'Terms of Service - Templates Cave',
                'meta_description' => 'Read our terms of service covering user obligations, prohibited uses, intellectual property, and liability for Templates Cave marketplace.',
                'meta_keywords' => ['terms', 'service', 'legal', 'agreement', 'conditions', 'marketplace'],
                'show_in_footer' => true,
                'sort_order' => 2,
            ],
            [
                'title' => 'Privacy Policy',
                'slug' => 'privacy-policy',
                'content' => $this->getPrivacyPolicyContent(),
                'status' => 'published',
                'meta_title' => 'Privacy Policy - Templates Cave',
                'meta_description' => 'Learn how Templates Cave collects, uses, and protects your personal information. GDPR-compliant privacy policy.',
                'meta_keywords' => ['privacy', 'policy', 'data', 'gdpr', 'personal information', 'cookies'],
                'show_in_footer' => true,
                'sort_order' => 3,
            ],
            [
                'title' => 'Refund Policy',
                'slug' => 'refund-policy',
                'content' => $this->getRefundPolicyContent(),
                'status' => 'published',
                'meta_title' => 'Refund Policy - Templates Cave',
                'meta_description' => 'Understand our refund policy for digital products, processing times, and conditions for Templates Cave purchases.',
                'meta_keywords' => ['refund', 'policy', 'digital products', 'money back', 'guarantee'],
                'show_in_footer' => true,
                'sort_order' => 4,
            ],
            [
                'title' => 'Contact',
                'slug' => 'contact',
                'content' => $this->getContactContent(),
                'status' => 'published',
                'meta_title' => 'Contact Us - Templates Cave',
                'meta_description' => 'Get in touch with Templates Cave support team. Contact form, business address, and support hours.',
                'meta_keywords' => ['contact', 'support', 'help', 'business address', 'customer service'],
                'show_in_footer' => false, // Contact has its own link
                'sort_order' => 5,
            ],
        ];

        foreach ($pages as $pageData) {
            Page::updateOrCreate(
                ['slug' => $pageData['slug']],
                $pageData
            );
        }
    }

    private function getHelpCenterContent(): string
    {
        return '
<h2>Frequently Asked Questions</h2>

<h3>Getting Started</h3>
<h4>How do I create an account?</h4>
<p>Creating an account is simple! Click the "Register" button in the top navigation, fill out the required information, and verify your email address. Once verified, you can start browsing and purchasing templates.</p>

<h4>How do I purchase a template?</h4>
<p>Browse our collection, click on any template you like, and click the "Purchase & Download" button. You\'ll be redirected to our secure checkout process where you can pay with credit card or PayPal.</p>

<h3>Downloads & Files</h3>
<h4>How do I download my purchased templates?</h4>
<p>After purchase, you can download your templates from your dashboard. Go to "My Downloads" to access all your purchased items. Download links are available immediately after payment confirmation.</p>

<h4>What file formats do you provide?</h4>
<p>Our templates come in various formats including HTML, CSS, JavaScript, PSD, Sketch, Figma, and more. Each template listing shows the included file formats.</p>

<h3>Licensing & Usage</h3>
<h4>Can I use templates for commercial projects?</h4>
<p>Yes! All our templates come with commercial licenses, allowing you to use them for client projects and commercial purposes. Check individual template licenses for specific terms.</p>

<h4>Can I modify the templates?</h4>
<p>Absolutely! You can customize, modify, and adapt our templates to fit your needs. That\'s the whole point of purchasing them.</p>

<h3>Technical Support</h3>
<h4>Do you provide technical support?</h4>
<p>We provide basic support for template usage and customization guidance. For complex customizations, we recommend hiring a developer.</p>

<h4>What if I encounter issues with a template?</h4>
<p>Contact our support team through the contact form or email. We\'ll help resolve any technical issues or provide guidance on template usage.</p>

<h3>Account & Billing</h3>
<h4>How do I update my account information?</h4>
<p>Log into your account and go to "Profile Settings" to update your personal information, email address, and password.</p>

<h4>What payment methods do you accept?</h4>
<p>We accept all major credit cards (Visa, MasterCard, American Express) and PayPal for secure and convenient payments.</p>

<h3>Need More Help?</h3>
<p>If you can\'t find the answer to your question here, please don\'t hesitate to <a href="/contact">contact our support team</a>. We\'re here to help!</p>
        ';
    }

    private function getTermsOfServiceContent(): string
    {
        return '
<h2>Terms of Service</h2>
<p><strong>Last updated:</strong> ' . date('F j, Y') . '</p>

<h3>1. Acceptance of Terms</h3>
<p>By accessing and using Templates Cave, you accept and agree to be bound by the terms and provision of this agreement.</p>

<h3>2. User Accounts</h3>
<p>When you create an account with us, you must provide information that is accurate, complete, and current at all times. You are responsible for safeguarding the password and for all activities that occur under your account.</p>

<h3>3. Prohibited Uses</h3>
<p>You may not use our service:</p>
<ul>
<li>For any unlawful purpose or to solicit others to perform unlawful acts</li>
<li>To violate any international, federal, provincial, or state regulations, rules, laws, or local ordinances</li>
<li>To infringe upon or violate our intellectual property rights or the intellectual property rights of others</li>
<li>To harass, abuse, insult, harm, defame, slander, disparage, intimidate, or discriminate</li>
<li>To submit false or misleading information</li>
</ul>

<h3>4. Intellectual Property Rights</h3>
<p>The service and its original content, features, and functionality are and will remain the exclusive property of Templates Cave and its licensors. The service is protected by copyright, trademark, and other laws.</p>

<h3>5. Template Licenses</h3>
<p>When you purchase a template, you receive a license to use that template according to the specific license terms provided with each template. Generally, this includes:</p>
<ul>
<li>Right to use for personal and commercial projects</li>
<li>Right to modify and customize the template</li>
<li>Prohibition on redistribution or resale of the template</li>
</ul>

<h3>6. Payment and Refunds</h3>
<p>All purchases are final. Refunds may be provided at our discretion in cases of technical issues or duplicate purchases. See our Refund Policy for more details.</p>

<h3>7. Limitation of Liability</h3>
<p>In no event shall Templates Cave, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages.</p>

<h3>8. Termination</h3>
<p>We may terminate or suspend your account and bar access to the service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever.</p>

<h3>9. Changes to Terms</h3>
<p>We reserve the right to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect.</p>

<h3>10. Contact Information</h3>
<p>If you have any questions about these Terms of Service, please contact <NAME_EMAIL>.</p>
        ';
    }

    private function getPrivacyPolicyContent(): string
    {
        return '
<h2>Privacy Policy</h2>
<p><strong>Last updated:</strong> ' . date('F j, Y') . '</p>

<h3>1. Information We Collect</h3>
<h4>Personal Information</h4>
<p>We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support:</p>
<ul>
<li>Name and email address</li>
<li>Payment information (processed securely through third-party providers)</li>
<li>Communication preferences</li>
</ul>

<h4>Usage Information</h4>
<p>We automatically collect certain information about your use of our service:</p>
<ul>
<li>IP address and browser type</li>
<li>Pages visited and time spent on our site</li>
<li>Download history and purchase activity</li>
</ul>

<h3>2. How We Use Your Information</h3>
<p>We use the information we collect to:</p>
<ul>
<li>Provide, maintain, and improve our services</li>
<li>Process transactions and send related information</li>
<li>Send technical notices and support messages</li>
<li>Respond to your comments and questions</li>
<li>Monitor and analyze trends and usage</li>
</ul>

<h3>3. Information Sharing</h3>
<p>We do not sell, trade, or otherwise transfer your personal information to third parties except:</p>
<ul>
<li>With your consent</li>
<li>To trusted third parties who assist us in operating our website (payment processors, email services)</li>
<li>When required by law or to protect our rights</li>
</ul>

<h3>4. Cookies and Tracking</h3>
<p>We use cookies and similar tracking technologies to:</p>
<ul>
<li>Remember your preferences and settings</li>
<li>Understand how you use our service</li>
<li>Improve user experience</li>
</ul>
<p>You can control cookies through your browser settings.</p>

<h3>5. Data Security</h3>
<p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

<h3>6. Your Rights (GDPR)</h3>
<p>If you are in the European Union, you have the right to:</p>
<ul>
<li>Access your personal data</li>
<li>Correct inaccurate data</li>
<li>Delete your data</li>
<li>Object to data processing</li>
<li>Data portability</li>
</ul>

<h3>7. Data Retention</h3>
<p>We retain your information for as long as your account is active or as needed to provide services. We may retain certain information as required by law.</p>

<h3>8. Children\'s Privacy</h3>
<p>Our service is not intended for children under 13. We do not knowingly collect personal information from children under 13.</p>

<h3>9. Changes to Privacy Policy</h3>
<p>We may update this privacy policy from time to time. We will notify you of any changes by posting the new policy on this page.</p>

<h3>10. Contact Us</h3>
<p>If you have questions about this privacy policy, please contact <NAME_EMAIL>.</p>
        ';
    }

    private function getRefundPolicyContent(): string
    {
        return '
<h2>Refund Policy</h2>
<p><strong>Last updated:</strong> ' . date('F j, Y') . '</p>

<h3>1. General Policy</h3>
<p>Due to the digital nature of our products, all sales are generally final. However, we understand that sometimes issues arise, and we\'re committed to customer satisfaction.</p>

<h3>2. Eligible Refund Scenarios</h3>
<p>We may provide refunds in the following situations:</p>
<ul>
<li><strong>Technical Issues:</strong> If the downloaded files are corrupted, incomplete, or significantly different from what was advertised</li>
<li><strong>Duplicate Purchases:</strong> If you accidentally purchase the same template multiple times</li>
<li><strong>Payment Errors:</strong> If you were charged incorrectly due to a system error</li>
<li><strong>Misrepresentation:</strong> If the template was significantly misrepresented in its description or preview</li>
</ul>

<h3>3. Non-Refundable Situations</h3>
<p>Refunds will not be provided for:</p>
<ul>
<li>Change of mind after purchase</li>
<li>Lack of technical skills to customize the template</li>
<li>Compatibility issues with your specific setup (unless clearly stated as compatible)</li>
<li>Templates that have been successfully downloaded and are working as described</li>
</ul>

<h3>4. Refund Process</h3>
<h4>How to Request a Refund</h4>
<ol>
<li>Contact our support team within 30 days of purchase</li>
<li>Provide your order number and reason for the refund request</li>
<li>Include any relevant screenshots or documentation</li>
</ol>

<h4>Processing Time</h4>
<p>Approved refunds will be processed within 5-10 business days and credited back to your original payment method.</p>

<h3>5. Partial Refunds</h3>
<p>In some cases, we may offer partial refunds or store credit as an alternative to full refunds, depending on the circumstances.</p>

<h3>6. Chargebacks</h3>
<p>If you initiate a chargeback without first contacting us, your account may be suspended, and you may lose access to all purchased templates.</p>

<h3>7. Free Templates</h3>
<p>Free templates are provided "as-is" without any warranty or guarantee. No refunds or support are provided for free templates.</p>

<h3>8. Contact for Refunds</h3>
<p>To request a refund or discuss any issues with your purchase, please contact our support team:</p>
<ul>
<li>Email: <EMAIL></li>
<li>Contact Form: <a href="/contact">Contact Us</a></li>
</ul>

<h3>9. Policy Updates</h3>
<p>This refund policy may be updated from time to time. Any changes will be posted on this page with an updated revision date.</p>

<p><strong>Note:</strong> This policy is designed to be fair to both customers and Templates Cave. We reserve the right to make final decisions on refund requests based on individual circumstances.</p>
        ';
    }

    private function getContactContent(): string
    {
        return '
<h2>Additional Contact Information</h2>

<h3>Business Hours</h3>
<p>Our support team is available during the following hours:</p>
<ul>
<li><strong>Monday - Friday:</strong> 9:00 AM - 6:00 PM (EST)</li>
<li><strong>Saturday:</strong> 10:00 AM - 4:00 PM (EST)</li>
<li><strong>Sunday:</strong> Closed</li>
</ul>

<h3>Response Times</h3>
<ul>
<li><strong>General Inquiries:</strong> Within 24 hours</li>
<li><strong>Technical Support:</strong> Within 12 hours</li>
<li><strong>Billing Issues:</strong> Within 6 hours</li>
<li><strong>Urgent Matters:</strong> Within 2 hours</li>
</ul>

<h3>What to Include in Your Message</h3>
<p>To help us assist you more effectively, please include:</p>
<ul>
<li>Your account email address</li>
<li>Order number (if applicable)</li>
<li>Detailed description of your issue or question</li>
<li>Screenshots or error messages (if relevant)</li>
<li>Browser and operating system information (for technical issues)</li>
</ul>

<h3>Alternative Contact Methods</h3>
<p>While our contact form is the fastest way to reach us, you can also:</p>
<ul>
<li>Email us directly at: <EMAIL></li>
<li>Follow us on social media for updates and announcements</li>
</ul>

<h3>Before Contacting Support</h3>
<p>Please check our <a href="/help-center">Help Center</a> first - you might find the answer to your question there!</p>
        ';
    }
}
