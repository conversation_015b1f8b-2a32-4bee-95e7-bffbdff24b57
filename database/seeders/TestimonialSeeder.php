<?php

namespace Database\Seeders;

use App\Models\Testimonial;
use Illuminate\Database\Seeder;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $testimonials = [
            [
                'name' => '<PERSON>',
                'position' => 'UI/UX Designer',
                'company' => 'Creative Studio',
                'content' => 'The templates from Templates Cave have saved me countless hours of work. The quality is exceptional and the designs are modern and professional.',
                'rating' => 5,
                'is_featured' => true,
                'sort_order' => 1,
            ],
            [
                'name' => '<PERSON>',
                'position' => 'Frontend Developer',
                'company' => 'Tech Solutions Inc',
                'content' => 'Amazing collection of templates! Clean code, well-documented, and easy to customize. Highly recommended for any web project.',
                'rating' => 5,
                'is_featured' => true,
                'sort_order' => 2,
            ],
            [
                'name' => '<PERSON>',
                'position' => 'Marketing Manager',
                'company' => 'Digital Agency',
                'content' => 'Templates <PERSON> has become my go-to resource for landing page templates. The conversion rates on these designs are fantastic!',
                'rating' => 5,
                'is_featured' => true,
                'sort_order' => 3,
            ],
            [
                'name' => '<PERSON>',
                'position' => 'Freelance Designer',
                'company' => null,
                'content' => 'As a freelancer, these templates help me deliver projects faster without compromising on quality. Great value for money!',
                'rating' => 5,
                'is_featured' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Lisa Wang',
                'position' => 'Product Manager',
                'company' => 'StartupXYZ',
                'content' => 'The dashboard templates are incredibly well-designed. They helped us launch our MVP much faster than expected.',
                'rating' => 5,
                'is_featured' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'James Miller',
                'position' => 'Web Developer',
                'company' => 'Miller Web Solutions',
                'content' => 'Excellent templates with clean, semantic code. The documentation is thorough and the support is responsive.',
                'rating' => 5,
                'is_featured' => true,
                'sort_order' => 6,
            ],
        ];

        foreach ($testimonials as $testimonial) {
            Testimonial::create($testimonial);
        }
    }
}
