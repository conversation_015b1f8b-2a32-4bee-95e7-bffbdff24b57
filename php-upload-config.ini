; Templates Cave - PHP Configuration for File Uploads
; This file contains PHP configuration settings to support large file uploads
; 
; MAMP INSTALLATION INSTRUCTIONS:
; 1. Copy this file to your MAMP PHP configuration directory:
;    - For MAMP: /Applications/MAMP/bin/php/php[version]/conf/
;    - Example: /Applications/MAMP/bin/php/php8.2.0/conf/
; 2. Restart MAMP servers
; 3. Verify settings by checking phpinfo() or the admin panel
;
; ALTERNATIVE METHODS:
; 1. Edit php.ini directly in MAMP PHP configuration directory
; 2. Add these settings to your .htaccess file (already done)
; 3. Use MAMP Pro interface to modify PHP settings

; File Upload Settings
; Maximum allowed size for uploaded files (100MB)
upload_max_filesize = 100M

; Maximum size of POST data that P<PERSON> will accept (100MB)
; This should be larger than upload_max_filesize
post_max_size = 100M

; Maximum amount of memory a script may consume (256MB)
memory_limit = 256M

; Maximum execution time of each script, in seconds (5 minutes)
max_execution_time = 300

; Maximum amount of time each script may spend parsing request data (5 minutes)
max_input_time = 300

; Maximum number of files that can be uploaded via a single request
max_file_uploads = 20

; Security Settings
; Don't expose PHP version in headers
expose_php = Off

; Session Security
session.cookie_httponly = 1
session.use_strict_mode = 1
session.cookie_samesite = "Strict"

; Error Reporting (adjust for production)
display_errors = Off
log_errors = On
error_log = /Applications/MAMP/logs/php_error.log

; Additional Performance Settings
; Enable output compression
zlib.output_compression = On
zlib.output_compression_level = 6

; Increase input variables limit
max_input_vars = 3000

; Increase nested data limit
max_input_nesting_level = 128

; File upload temporary directory
; upload_tmp_dir = /Applications/MAMP/tmp/php

; NOTES:
; - These settings allow for uploading files up to 100MB
; - Product files in Templates Cave can be up to 100MB
; - Product images can be up to 5MB each
; - Blog images can be up to 2MB each
; - Multiple images can be uploaded simultaneously
; 
; TROUBLESHOOTING:
; If you still encounter upload issues:
; 1. Check MAMP error logs: /Applications/MAMP/logs/
; 2. Verify PHP version matches your MAMP installation
; 3. Ensure MAMP has write permissions to upload directories
; 4. Check available disk space
; 5. Restart MAMP completely after making changes
