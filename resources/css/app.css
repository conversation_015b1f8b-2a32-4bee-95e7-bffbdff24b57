@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom responsive utilities */
@layer utilities {
    .container-responsive {
        @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    .grid-responsive {
        @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
    }

    .text-responsive {
        @apply text-sm sm:text-base lg:text-lg;
    }

    .mobile-menu-transition {
        @apply transition-all duration-300 ease-in-out;
    }
}

/* Ensure mobile menu works properly */
@media (max-width: 768px) {
    .mobile-menu {
        @apply block;
    }

    .desktop-menu {
        @apply hidden;
    }
}

@media (min-width: 769px) {
    .mobile-menu {
        @apply hidden;
    }

    .desktop-menu {
        @apply block;
    }
}
