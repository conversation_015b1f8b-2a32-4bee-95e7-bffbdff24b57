@extends('layouts.admin')

@section('title', $category->name . ' - ' . config('templatescave.site.name'))
@section('description', 'View blog category details')

@section('content')
    <div class="p-6">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <div class="flex items-center">
                        @if($category->icon)
                            <div class="w-10 h-10 rounded-lg flex items-center justify-center mr-3" style="background-color: {{ $category->getColorWithOpacity(0.1) }}; color: {{ $category->color }};">
                                <i class="{{ $category->icon }}"></i>
                            </div>
                        @endif
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $category->name }}</h1>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">Blog Category Details</p>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-3 mt-4 sm:mt-0">
                    <a href="{{ route('admin.blog.categories.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Categories
                    </a>
                    <a href="{{ route('admin.blog.categories.edit', $category) }}"
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Category
                    </a>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Category Information -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Category Information</h3>
                        
                        @if($category->description)
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Description</h4>
                            <p class="text-gray-700 dark:text-gray-300">{{ $category->description }}</p>
                        </div>
                        @endif

                        <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Slug</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">{{ $category->slug }}</dd>
                            </div>

                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                                <dd class="mt-1">
                                    @if($category->is_active)
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                            Active
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                            Inactive
                                        </span>
                                    @endif
                                </dd>
                            </div>

                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Color</dt>
                                <dd class="mt-1 flex items-center">
                                    <div class="w-6 h-6 rounded border border-gray-300 dark:border-gray-600 mr-2" style="background-color: {{ $category->color }};"></div>
                                    <span class="text-sm text-gray-900 dark:text-white font-mono">{{ $category->color }}</span>
                                </dd>
                            </div>

                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Sort Order</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $category->sort_order }}</dd>
                            </div>

                            @if($category->icon)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Icon</dt>
                                <dd class="mt-1 flex items-center">
                                    <i class="{{ $category->icon }} mr-2" style="color: {{ $category->color }};"></i>
                                    <span class="text-sm text-gray-900 dark:text-white font-mono">{{ $category->icon }}</span>
                                </dd>
                            </div>
                            @endif

                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $category->created_at->format('M j, Y g:i A') }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Recent Posts -->
                @if($category->posts->count() > 0)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Posts</h3>
                            <a href="{{ route('admin.blog.posts.index', ['category' => $category->id]) }}" 
                               class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium text-sm">
                                View all posts →
                            </a>
                        </div>
                        
                        <div class="space-y-4">
                            @foreach($category->posts->take(5) as $post)
                                <div class="border-l-4 pl-4 py-2" style="border-color: {{ $category->color }};">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="font-medium text-gray-900 dark:text-white">
                                            <a href="{{ route('admin.blog.posts.show', $post) }}" class="hover:text-blue-600 dark:hover:text-blue-400">
                                                {{ $post->title }}
                                            </a>
                                        </h4>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                            {{ $post->status === 'published' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 
                                               ($post->status === 'draft' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 
                                                'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200') }}">
                                            {{ ucfirst($post->status) }}
                                        </span>
                                    </div>
                                    @if($post->excerpt)
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{{ Str::limit($post->excerpt, 150) }}</p>
                                    @endif
                                    <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                        <span>{{ $post->author->name }}</span>
                                        <span class="mx-2">•</span>
                                        <span>{{ $post->created_at->diffForHumans() }}</span>
                                        @if($post->is_featured)
                                            <span class="mx-2">•</span>
                                            <span class="text-yellow-600 dark:text-yellow-400">Featured</span>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Statistics -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Statistics</h3>
                        
                        <dl class="space-y-3">
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Posts</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ number_format($category->posts_count) }}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Published Posts</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ number_format($category->published_posts_count) }}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Draft Posts</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ number_format($category->posts_count - $category->published_posts_count) }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- SEO Information -->
                @if($category->meta_title || $category->meta_description)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">SEO Information</h3>
                        
                        <dl class="space-y-3">
                            @if($category->meta_title)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Meta Title</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $category->meta_title }}</dd>
                            </div>
                            @endif

                            @if($category->meta_description)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Meta Description</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $category->meta_description }}</dd>
                            </div>
                            @endif
                        </dl>
                    </div>
                </div>
                @endif

                <!-- Actions -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Actions</h3>
                        
                        <div class="space-y-3">
                            <form method="POST" action="{{ route('admin.blog.categories.toggle-active', $category) }}" class="w-full">
                                @csrf
                                <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 {{ $category->is_active ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700' }} text-white font-medium rounded-lg transition-colors duration-200">
                                    @if($category->is_active)
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636"></path>
                                        </svg>
                                        Deactivate Category
                                    @else
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Activate Category
                                    @endif
                                </button>
                            </form>

                            <a href="{{ route('admin.blog.posts.create', ['category' => $category->id]) }}" 
                               class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create Post in Category
                            </a>

                            <form method="POST" action="{{ route('admin.blog.categories.destroy', $category) }}" class="w-full" onsubmit="return confirm('Are you sure you want to delete this category? This action cannot be undone and will affect all posts in this category.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors duration-200">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    Delete Category
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
