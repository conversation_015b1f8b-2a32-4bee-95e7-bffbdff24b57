@extends('layouts.admin')

@section('title', 'Blog Comments - ' . config('templatescave.site.name'))
@section('description', 'Manage blog comments and moderation')

@section('content')
    <div class="p-6">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Blog Comments</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">Moderate and manage blog comments</p>
                </div>
                <div class="flex space-x-3 mt-4 sm:mt-0">
                    <!-- Comment Stats -->
                    <div class="flex space-x-4 text-sm">
                        <div class="text-center">
                            <div class="font-semibold text-yellow-600 dark:text-yellow-400">{{ $stats['pending'] ?? 0 }}</div>
                            <div class="text-gray-500 dark:text-gray-400">Pending</div>
                        </div>
                        <div class="text-center">
                            <div class="font-semibold text-green-600 dark:text-green-400">{{ $stats['approved'] ?? 0 }}</div>
                            <div class="text-gray-500 dark:text-gray-400">Approved</div>
                        </div>
                        <div class="text-center">
                            <div class="font-semibold text-red-600 dark:text-red-400">{{ $stats['spam'] ?? 0 }}</div>
                            <div class="text-gray-500 dark:text-gray-400">Spam</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
            <div class="p-6">
                <form method="GET" action="{{ route('admin.blog.comments.index') }}" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- Search -->
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                            <input type="text" name="search" id="search" value="{{ request('search') }}" 
                                   placeholder="Search comments..."
                                   class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        </div>

                        <!-- Status Filter -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                            <select name="status" id="status" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <option value="">All Statuses</option>
                                <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Approved</option>
                                <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Rejected</option>
                                <option value="spam" {{ request('status') === 'spam' ? 'selected' : '' }}>Spam</option>
                            </select>
                        </div>

                        <!-- Post Filter -->
                        <div>
                            <label for="post" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Blog Post</label>
                            <select name="post" id="post" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <option value="">All Posts</option>
                                @foreach($posts as $post)
                                    <option value="{{ $post->id }}" {{ request('post') == $post->id ? 'selected' : '' }}>
                                        {{ Str::limit($post->title, 50) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Date Filter -->
                        <div>
                            <label for="date_range" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date Range</label>
                            <select name="date_range" id="date_range" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <option value="">All Time</option>
                                <option value="today" {{ request('date_range') === 'today' ? 'selected' : '' }}>Today</option>
                                <option value="week" {{ request('date_range') === 'week' ? 'selected' : '' }}>This Week</option>
                                <option value="month" {{ request('date_range') === 'month' ? 'selected' : '' }}>This Month</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div class="flex space-x-2">
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                Filter
                            </button>
                            <a href="{{ route('admin.blog.comments.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200">
                                Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Comments List -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="p-6">
                @if($comments->count() > 0)
                    <!-- Bulk Actions -->
                    <form id="bulk-action-form" method="POST" action="{{ route('admin.blog.comments.bulk-action') }}" class="mb-6">
                        @csrf
                        <div class="flex items-center space-x-4">
                            <select name="action" id="bulk-action" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <option value="">Bulk Actions</option>
                                <option value="approve">Approve</option>
                                <option value="reject">Reject</option>
                                <option value="spam">Mark as Spam</option>
                                <option value="delete">Delete</option>
                            </select>
                            <button type="submit" id="apply-bulk-action" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200" disabled>
                                Apply
                            </button>
                        </div>
                    </form>

                    <!-- Comments List -->
                    <div class="space-y-6">
                        @foreach($comments as $comment)
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                            <div class="flex items-start space-x-4">
                                <!-- Checkbox -->
                                <div class="flex-shrink-0 pt-1">
                                    <input type="checkbox" name="comment_ids[]" value="{{ $comment->id }}" class="comment-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </div>

                                <!-- Avatar -->
                                <div class="flex-shrink-0">
                                    @if($comment->user && $comment->user->avatar)
                                        <img src="{{ Storage::url($comment->user->avatar) }}" alt="{{ $comment->author_name }}" class="w-10 h-10 rounded-full">
                                    @else
                                        <div class="w-10 h-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                            <span class="text-gray-600 dark:text-gray-300 font-medium">{{ substr($comment->author_name, 0, 1) }}</span>
                                        </div>
                                    @endif
                                </div>

                                <!-- Comment Content -->
                                <div class="flex-1 min-w-0">
                                    <!-- Header -->
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center space-x-2">
                                            <h4 class="font-medium text-gray-900 dark:text-white">{{ $comment->author_name }}</h4>
                                            @if($comment->author_email)
                                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ $comment->author_email }}</span>
                                            @endif
                                            @if($comment->is_pinned)
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"></path>
                                                    </svg>
                                                    Pinned
                                                </span>
                                            @endif
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <!-- Status Badge -->
                                            @if($comment->status === 'approved')
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                    Approved
                                                </span>
                                            @elseif($comment->status === 'pending')
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                                    Pending
                                                </span>
                                            @elseif($comment->status === 'rejected')
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                                    Rejected
                                                </span>
                                            @elseif($comment->status === 'spam')
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                                    Spam
                                                </span>
                                            @endif
                                            
                                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ $comment->created_at->diffForHumans() }}</span>
                                        </div>
                                    </div>

                                    <!-- Post Reference -->
                                    <div class="mb-3">
                                        <span class="text-sm text-gray-500 dark:text-gray-400">On:</span>
                                        <a href="{{ route('admin.blog.posts.show', $comment->post) }}" class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 ml-1">
                                            {{ $comment->post->title }}
                                        </a>
                                    </div>

                                    <!-- Comment Text -->
                                    <div class="prose prose-sm max-w-none dark:prose-invert mb-4">
                                        <p class="text-gray-700 dark:text-gray-300">{{ $comment->content }}</p>
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex items-center space-x-4">
                                        @if($comment->status !== 'approved')
                                            <form method="POST" action="{{ route('admin.blog.comments.approve', $comment) }}" class="inline">
                                                @csrf
                                                <button type="submit" class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 text-sm font-medium">
                                                    Approve
                                                </button>
                                            </form>
                                        @endif

                                        @if($comment->status !== 'rejected')
                                            <form method="POST" action="{{ route('admin.blog.comments.reject', $comment) }}" class="inline">
                                                @csrf
                                                <button type="submit" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm font-medium">
                                                    Reject
                                                </button>
                                            </form>
                                        @endif

                                        @if($comment->status !== 'spam')
                                            <form method="POST" action="{{ route('admin.blog.comments.spam', $comment) }}" class="inline">
                                                @csrf
                                                <button type="submit" class="text-orange-600 hover:text-orange-800 dark:text-orange-400 dark:hover:text-orange-300 text-sm font-medium">
                                                    Spam
                                                </button>
                                            </form>
                                        @endif

                                        <form method="POST" action="{{ route('admin.blog.comments.toggle-pin', $comment) }}" class="inline">
                                            @csrf
                                            <button type="submit" class="text-yellow-600 hover:text-yellow-800 dark:text-yellow-400 dark:hover:text-yellow-300 text-sm font-medium">
                                                {{ $comment->is_pinned ? 'Unpin' : 'Pin' }}
                                            </button>
                                        </form>

                                        <a href="{{ route('admin.blog.comments.show', $comment) }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                                            View Details
                                        </a>

                                        <form method="POST" action="{{ route('admin.blog.comments.destroy', $comment) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this comment?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm font-medium">
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $comments->links() }}
                    </div>
                @else
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No comments found</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Comments will appear here when users start engaging with your blog posts.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <script>
    // Bulk actions functionality
    document.addEventListener('DOMContentLoaded', function() {
        const commentCheckboxes = document.querySelectorAll('.comment-checkbox');
        const bulkActionSelect = document.getElementById('bulk-action');
        const applyBulkActionBtn = document.getElementById('apply-bulk-action');
        const bulkActionForm = document.getElementById('bulk-action-form');

        // Individual checkbox functionality
        commentCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateBulkActionButton();
            });
        });

        // Update bulk action button state
        function updateBulkActionButton() {
            const checkedCount = document.querySelectorAll('.comment-checkbox:checked').length;
            const hasAction = bulkActionSelect.value !== '';
            
            if (applyBulkActionBtn) {
                applyBulkActionBtn.disabled = checkedCount === 0 || !hasAction;
            }
        }

        // Bulk action select change
        if (bulkActionSelect) {
            bulkActionSelect.addEventListener('change', updateBulkActionButton);
        }

        // Bulk action form submission
        if (bulkActionForm) {
            bulkActionForm.addEventListener('submit', function(e) {
                const checkedCheckboxes = document.querySelectorAll('.comment-checkbox:checked');
                const action = bulkActionSelect.value;
                
                if (checkedCheckboxes.length === 0) {
                    e.preventDefault();
                    alert('Please select at least one comment.');
                    return;
                }

                if (!action) {
                    e.preventDefault();
                    alert('Please select an action.');
                    return;
                }

                if (action === 'delete') {
                    if (!confirm(`Are you sure you want to delete ${checkedCheckboxes.length} comment(s)? This action cannot be undone.`)) {
                        e.preventDefault();
                        return;
                    }
                }

                // Add selected comment IDs to form
                checkedCheckboxes.forEach(checkbox => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'comment_ids[]';
                    input.value = checkbox.value;
                    bulkActionForm.appendChild(input);
                });
            });
        }
    });
    </script>
@endsection
