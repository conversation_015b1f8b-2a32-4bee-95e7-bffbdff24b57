@extends('layouts.admin')

@section('title', 'Comment Details - ' . config('templatescave.site.name'))
@section('description', 'View comment details')

@section('content')
    <div class="p-6">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Comment Details</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">View and manage comment</p>
                </div>
                <div class="flex space-x-3 mt-4 sm:mt-0">
                    <a href="{{ route('admin.blog.comments.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Comments
                    </a>
                </div>
            </div>
        </div>

        <!-- Comment Details -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="p-6">
                <!-- Comment Header -->
                <div class="flex items-start justify-between mb-6">
                    <div class="flex items-center space-x-4">
                        <!-- Avatar -->
                        <div class="flex-shrink-0">
                            @if($comment->user && $comment->user->avatar)
                                <img src="{{ Storage::url($comment->user->avatar) }}" alt="{{ $comment->author_name }}" class="w-12 h-12 rounded-full">
                            @else
                                <div class="w-12 h-12 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                    <span class="text-gray-600 dark:text-gray-300 font-medium text-lg">{{ substr($comment->author_name, 0, 1) }}</span>
                                </div>
                            @endif
                        </div>

                        <!-- Author Info -->
                        <div>
                            <h3 class="font-medium text-gray-900 dark:text-white">{{ $comment->author_name }}</h3>
                            @if($comment->author_email)
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $comment->author_email }}</p>
                            @endif
                            @if($comment->author_url)
                                <p class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400">
                                    <a href="{{ $comment->author_url }}" target="_blank" rel="noopener noreferrer">{{ $comment->author_url }}</a>
                                </p>
                            @endif
                        </div>
                    </div>

                    <!-- Status Badge -->
                    <div class="flex items-center space-x-2">
                        @if($comment->status === 'approved')
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                Approved
                            </span>
                        @elseif($comment->status === 'pending')
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                Pending
                            </span>
                        @elseif($comment->status === 'rejected')
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                Rejected
                            </span>
                        @elseif($comment->status === 'spam')
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                Spam
                            </span>
                        @endif

                        @if($comment->is_pinned)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"></path>
                                </svg>
                                Pinned
                            </span>
                        @endif
                    </div>
                </div>

                <!-- Post Reference -->
                <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-2">Comment on:</h4>
                    <a href="{{ route('admin.blog.posts.show', $comment->post) }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                        {{ $comment->post->title }}
                    </a>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        Published {{ $comment->post->published_at ? $comment->post->published_at->format('M j, Y') : 'Not published' }}
                    </p>
                </div>

                <!-- Comment Content -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">Comment:</h4>
                    <div class="prose prose-sm max-w-none dark:prose-invert bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                        <p class="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{{ $comment->content }}</p>
                    </div>
                </div>

                <!-- Comment Meta -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white mb-3">Comment Information</h4>
                        <dl class="space-y-2">
                            <div class="flex justify-between">
                                <dt class="text-sm text-gray-500 dark:text-gray-400">Submitted:</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $comment->created_at->format('M j, Y g:i A') }}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm text-gray-500 dark:text-gray-400">IP Address:</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $comment->ip_address ?? 'Not recorded' }}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm text-gray-500 dark:text-gray-400">User Agent:</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ Str::limit($comment->user_agent ?? 'Not recorded', 50) }}</dd>
                            </div>
                        </dl>
                    </div>

                    @if($comment->approved_at)
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white mb-3">Approval Information</h4>
                        <dl class="space-y-2">
                            <div class="flex justify-between">
                                <dt class="text-sm text-gray-500 dark:text-gray-400">Approved:</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $comment->approved_at->format('M j, Y g:i A') }}</dd>
                            </div>
                            @if($comment->approvedBy)
                            <div class="flex justify-between">
                                <dt class="text-sm text-gray-500 dark:text-gray-400">Approved by:</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $comment->approvedBy->name }}</dd>
                            </div>
                            @endif
                        </dl>
                    </div>
                    @endif
                </div>

                <!-- Parent Comment -->
                @if($comment->parent)
                <div class="mb-6">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">Replying to:</h4>
                    <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border-l-4 border-blue-500">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="font-medium text-gray-900 dark:text-white">{{ $comment->parent->author_name }}</span>
                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ $comment->parent->created_at->diffForHumans() }}</span>
                        </div>
                        <p class="text-gray-700 dark:text-gray-300">{{ Str::limit($comment->parent->content, 200) }}</p>
                    </div>
                </div>
                @endif

                <!-- Replies -->
                @if($comment->replies->count() > 0)
                <div class="mb-6">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">Replies ({{ $comment->replies->count() }}):</h4>
                    <div class="space-y-4">
                        @foreach($comment->replies as $reply)
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border-l-4 border-green-500">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-2">
                                    <span class="font-medium text-gray-900 dark:text-white">{{ $reply->author_name }}</span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $reply->created_at->diffForHumans() }}</span>
                                </div>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                    {{ $reply->status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 
                                       ($reply->status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 
                                        'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200') }}">
                                    {{ ucfirst($reply->status) }}
                                </span>
                            </div>
                            <p class="text-gray-700 dark:text-gray-300">{{ $reply->content }}</p>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Actions -->
                <div class="flex flex-wrap items-center gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                    @if($comment->status !== 'approved')
                        <form method="POST" action="{{ route('admin.blog.comments.approve', $comment) }}" class="inline">
                            @csrf
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Approve
                            </button>
                        </form>
                    @endif

                    @if($comment->status !== 'rejected')
                        <form method="POST" action="{{ route('admin.blog.comments.reject', $comment) }}" class="inline">
                            @csrf
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Reject
                            </button>
                        </form>
                    @endif

                    @if($comment->status !== 'spam')
                        <form method="POST" action="{{ route('admin.blog.comments.spam', $comment) }}" class="inline">
                            @csrf
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                Mark as Spam
                            </button>
                        </form>
                    @endif

                    <form method="POST" action="{{ route('admin.blog.comments.toggle-pin', $comment) }}" class="inline">
                        @csrf
                        <button type="submit" class="inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white font-medium rounded-lg transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"></path>
                            </svg>
                            {{ $comment->is_pinned ? 'Unpin' : 'Pin' }}
                        </button>
                    </form>

                    <form method="POST" action="{{ route('admin.blog.comments.destroy', $comment) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this comment? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
