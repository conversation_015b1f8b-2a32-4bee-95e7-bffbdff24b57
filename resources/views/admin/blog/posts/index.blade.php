@extends('layouts.admin')

@section('title', 'Blog Posts - ' . config('templatescave.site.name'))
@section('description', 'Manage blog posts')

@section('content')
    <div class="p-6">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Blog Posts</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">Manage your blog posts and content</p>
                </div>
                <div class="flex space-x-3 mt-4 sm:mt-0">
                    <a href="{{ route('admin.blog.posts.create') }}"
                       class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg shadow-sm transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create Post
                    </a>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
            <div class="p-6">
                <form method="GET" action="{{ route('admin.blog.posts.index') }}" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- Search -->
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                            <input type="text" name="search" id="search" value="{{ request('search') }}" 
                                   placeholder="Search posts..."
                                   class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        </div>

                        <!-- Status Filter -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                            <select name="status" id="status" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <option value="">All Statuses</option>
                                <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>Draft</option>
                                <option value="published" {{ request('status') === 'published' ? 'selected' : '' }}>Published</option>
                                <option value="archived" {{ request('status') === 'archived' ? 'selected' : '' }}>Archived</option>
                            </select>
                        </div>

                        <!-- Category Filter -->
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category</label>
                            <select name="category" id="category" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <option value="">All Categories</option>
                                @foreach($categories as $cat)
                                    <option value="{{ $cat->id }}" {{ request('category') == $cat->id ? 'selected' : '' }}>
                                        {{ $cat->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Author Filter -->
                        <div>
                            <label for="author" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Author</label>
                            <select name="author" id="author" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <option value="">All Authors</option>
                                @foreach($authors as $user)
                                    <option value="{{ $user->id }}" {{ request('author') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div class="flex space-x-2">
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                Filter
                            </button>
                            <a href="{{ route('admin.blog.posts.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200">
                                Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Posts Table -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="p-6">
                @if($posts->count() > 0)
                    <!-- Bulk Actions -->
                    <form id="bulk-action-form" method="POST" action="{{ route('admin.blog.posts.bulk-action') }}" class="mb-6">
                        @csrf
                        <div class="flex items-center space-x-4">
                            <select name="action" id="bulk-action" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <option value="">Bulk Actions</option>
                                <option value="publish">Publish</option>
                                <option value="draft">Move to Draft</option>
                                <option value="archive">Archive</option>
                                <option value="delete">Delete</option>
                            </select>
                            <button type="submit" id="apply-bulk-action" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200" disabled>
                                Apply
                            </button>
                        </div>
                    </form>

                    <!-- Desktop Table -->
                    <div class="hidden lg:block overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200 dark:border-gray-700">
                                    <th class="text-left py-3 px-4">
                                        <input type="checkbox" id="select-all" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Title</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Author</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Category</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Status</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Views</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Date</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($posts as $post)
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                                    <td class="py-4 px-4">
                                        <input type="checkbox" name="post_ids[]" value="{{ $post->id }}" class="post-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </td>
                                    <td class="py-4 px-4">
                                        <div class="flex items-center">
                                            @if($post->featured_image)
                                                <img src="{{ Storage::url($post->featured_image) }}" alt="{{ $post->title }}" class="w-12 h-12 object-cover rounded-lg mr-3">
                                            @endif
                                            <div>
                                                <div class="font-medium text-gray-900 dark:text-white">{{ $post->title }}</div>
                                                @if($post->is_featured)
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 mt-1">
                                                        Featured
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-4 px-4 text-gray-600 dark:text-gray-400">{{ $post->author->name }}</td>
                                    <td class="py-4 px-4">
                                        @if($post->category)
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" style="background-color: {{ $post->category->getColorWithOpacity(0.1) }}; color: {{ $post->category->color }};">
                                                {{ $post->category->name }}
                                            </span>
                                        @else
                                            <span class="text-gray-400 dark:text-gray-500">Uncategorized</span>
                                        @endif
                                    </td>
                                    <td class="py-4 px-4">
                                        @if($post->status === 'published')
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                Published
                                            </span>
                                        @elseif($post->status === 'draft')
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                                Draft
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                                Archived
                                            </span>
                                        @endif
                                    </td>
                                    <td class="py-4 px-4 text-gray-600 dark:text-gray-400">{{ number_format($post->view_count) }}</td>
                                    <td class="py-4 px-4 text-gray-600 dark:text-gray-400">{{ $post->created_at->format('M j, Y') }}</td>
                                    <td class="py-4 px-4">
                                        <div class="flex items-center space-x-2">
                                            <a href="{{ route('admin.blog.posts.show', $post) }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                            </a>
                                            <a href="{{ route('admin.blog.posts.edit', $post) }}" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </a>
                                            <form method="POST" action="{{ route('admin.blog.posts.destroy', $post) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this post?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Mobile Cards -->
                    <div class="lg:hidden space-y-4">
                        @foreach($posts as $post)
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center">
                                    <input type="checkbox" name="post_ids[]" value="{{ $post->id }}" class="post-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3">
                                    @if($post->featured_image)
                                        <img src="{{ Storage::url($post->featured_image) }}" alt="{{ $post->title }}" class="w-12 h-12 object-cover rounded-lg mr-3">
                                    @endif
                                    <div>
                                        <h3 class="font-medium text-gray-900 dark:text-white">{{ $post->title }}</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">by {{ $post->author->name }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4 text-sm mb-3">
                                <div>
                                    <span class="text-gray-500 dark:text-gray-400">Category:</span>
                                    @if($post->category)
                                        <span class="ml-1 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" style="background-color: {{ $post->category->getColorWithOpacity(0.1) }}; color: {{ $post->category->color }};">
                                            {{ $post->category->name }}
                                        </span>
                                    @else
                                        <span class="ml-1 text-gray-400 dark:text-gray-500">Uncategorized</span>
                                    @endif
                                </div>
                                <div>
                                    <span class="text-gray-500 dark:text-gray-400">Status:</span>
                                    @if($post->status === 'published')
                                        <span class="ml-1 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                            Published
                                        </span>
                                    @elseif($post->status === 'draft')
                                        <span class="ml-1 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                            Draft
                                        </span>
                                    @else
                                        <span class="ml-1 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                            Archived
                                        </span>
                                    @endif
                                </div>
                                <div>
                                    <span class="text-gray-500 dark:text-gray-400">Views:</span>
                                    <span class="ml-1 text-gray-900 dark:text-white">{{ number_format($post->view_count) }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500 dark:text-gray-400">Date:</span>
                                    <span class="ml-1 text-gray-900 dark:text-white">{{ $post->created_at->format('M j, Y') }}</span>
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                @if($post->is_featured)
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                        Featured
                                    </span>
                                @else
                                    <div></div>
                                @endif
                                
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('admin.blog.posts.show', $post) }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </a>
                                    <a href="{{ route('admin.blog.posts.edit', $post) }}" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </a>
                                    <form method="POST" action="{{ route('admin.blog.posts.destroy', $post) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this post?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $posts->links() }}
                    </div>
                @else
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No blog posts</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating your first blog post.</p>
                        <div class="mt-6">
                            <a href="{{ route('admin.blog.posts.create') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create Post
                            </a>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <script>
    // Bulk actions functionality
    document.addEventListener('DOMContentLoaded', function() {
        const selectAllCheckbox = document.getElementById('select-all');
        const postCheckboxes = document.querySelectorAll('.post-checkbox');
        const bulkActionSelect = document.getElementById('bulk-action');
        const applyBulkActionBtn = document.getElementById('apply-bulk-action');
        const bulkActionForm = document.getElementById('bulk-action-form');

        // Select all functionality
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                postCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActionButton();
            });
        }

        // Individual checkbox functionality
        postCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateSelectAllCheckbox();
                updateBulkActionButton();
            });
        });

        // Update select all checkbox state
        function updateSelectAllCheckbox() {
            if (selectAllCheckbox) {
                const checkedCount = document.querySelectorAll('.post-checkbox:checked').length;
                selectAllCheckbox.checked = checkedCount === postCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < postCheckboxes.length;
            }
        }

        // Update bulk action button state
        function updateBulkActionButton() {
            const checkedCount = document.querySelectorAll('.post-checkbox:checked').length;
            const hasAction = bulkActionSelect.value !== '';
            
            if (applyBulkActionBtn) {
                applyBulkActionBtn.disabled = checkedCount === 0 || !hasAction;
            }
        }

        // Bulk action select change
        if (bulkActionSelect) {
            bulkActionSelect.addEventListener('change', updateBulkActionButton);
        }

        // Bulk action form submission
        if (bulkActionForm) {
            bulkActionForm.addEventListener('submit', function(e) {
                const checkedCheckboxes = document.querySelectorAll('.post-checkbox:checked');
                const action = bulkActionSelect.value;
                
                if (checkedCheckboxes.length === 0) {
                    e.preventDefault();
                    alert('Please select at least one post.');
                    return;
                }

                if (!action) {
                    e.preventDefault();
                    alert('Please select an action.');
                    return;
                }

                if (action === 'delete') {
                    if (!confirm(`Are you sure you want to delete ${checkedCheckboxes.length} post(s)? This action cannot be undone.`)) {
                        e.preventDefault();
                        return;
                    }
                }

                // Add selected post IDs to form
                checkedCheckboxes.forEach(checkbox => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'post_ids[]';
                    input.value = checkbox.value;
                    bulkActionForm.appendChild(input);
                });
            });
        }
    });
    </script>
@endsection
