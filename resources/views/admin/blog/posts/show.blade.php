@extends('layouts.admin')

@section('title', $post->title . ' - ' . config('templatescave.site.name'))
@section('description', 'View blog post details')

@section('content')
    <div class="p-6">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $post->title }}</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">Blog Post Details</p>
                </div>
                <div class="flex space-x-3 mt-4 sm:mt-0">
                    <a href="{{ route('admin.blog.posts.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Posts
                    </a>
                    <a href="{{ route('admin.blog.posts.edit', $post) }}"
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Post
                    </a>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Post Content -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <!-- Featured Image -->
                        @if($post->featured_image)
                            <div class="mb-6">
                                <img src="{{ Storage::url($post->featured_image) }}" alt="{{ $post->title }}" 
                                     class="w-full h-64 object-cover rounded-lg">
                            </div>
                        @endif

                        <!-- Content -->
                        <div class="prose prose-lg max-w-none dark:prose-invert">
                            {!! nl2br(e($post->content)) !!}
                        </div>

                        <!-- Tags -->
                        @if($post->tags && is_array($post->tags))
                            <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Tags</h4>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($post->tags as $tag)
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                            {{ trim($tag) }}
                                        </span>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Comments Section -->
                @if($post->allow_comments && $post->comments->count() > 0)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            Comments ({{ $post->comments->count() }})
                        </h3>
                        
                        <div class="space-y-4">
                            @foreach($post->comments->take(5) as $comment)
                                <div class="border-l-4 border-blue-500 pl-4 py-2">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center space-x-2">
                                            <span class="font-medium text-gray-900 dark:text-white">{{ $comment->author_name }}</span>
                                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ $comment->created_at->diffForHumans() }}</span>
                                        </div>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                            {{ $comment->status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 
                                               ($comment->status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 
                                                'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200') }}">
                                            {{ ucfirst($comment->status) }}
                                        </span>
                                    </div>
                                    <p class="text-gray-700 dark:text-gray-300">{{ Str::limit($comment->content, 200) }}</p>
                                </div>
                            @endforeach
                        </div>

                        @if($post->comments->count() > 5)
                            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                                <a href="{{ route('admin.blog.comments.index', ['post' => $post->id]) }}" 
                                   class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                                    View all {{ $post->comments->count() }} comments →
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Post Information -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Post Information</h3>
                        
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                                <dd class="mt-1">
                                    @if($post->status === 'published')
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                            Published
                                        </span>
                                    @elseif($post->status === 'draft')
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                            Draft
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                            {{ ucfirst($post->status) }}
                                        </span>
                                    @endif
                                </dd>
                            </div>

                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Author</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $post->author->name }}</dd>
                            </div>

                            @if($post->category)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Category</dt>
                                <dd class="mt-1">
                                    <div class="flex items-center">
                                        @if($post->category->icon)
                                            <div class="w-6 h-6 rounded flex items-center justify-center mr-2" style="background-color: {{ $post->category->getColorWithOpacity(0.1) }}; color: {{ $post->category->color }};">
                                                <i class="{{ $post->category->icon }} text-xs"></i>
                                            </div>
                                        @endif
                                        <span class="text-sm text-gray-900 dark:text-white">{{ $post->category->name }}</span>
                                    </div>
                                </dd>
                            </div>
                            @endif

                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $post->created_at->format('M j, Y g:i A') }}</dd>
                            </div>

                            @if($post->published_at)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Published</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $post->published_at->format('M j, Y g:i A') }}</dd>
                            </div>
                            @endif

                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $post->updated_at->format('M j, Y g:i A') }}</dd>
                            </div>

                            @if($post->is_featured)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Featured</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                        Featured
                                    </span>
                                </dd>
                            </div>
                            @endif
                        </dl>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Statistics</h3>
                        
                        <dl class="space-y-3">
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Views</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ number_format($post->view_count) }}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Comments</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ number_format($post->comment_count) }}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Likes</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ number_format($post->like_count) }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- SEO Information -->
                @if($post->meta_title || $post->meta_description || $post->meta_keywords)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">SEO Information</h3>
                        
                        <dl class="space-y-3">
                            @if($post->meta_title)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Meta Title</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $post->meta_title }}</dd>
                            </div>
                            @endif

                            @if($post->meta_description)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Meta Description</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $post->meta_description }}</dd>
                            </div>
                            @endif

                            @if($post->meta_keywords && is_array($post->meta_keywords))
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Meta Keywords</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ implode(', ', $post->meta_keywords) }}</dd>
                            </div>
                            @endif
                        </dl>
                    </div>
                </div>
                @endif

                <!-- Actions -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Actions</h3>
                        
                        <div class="space-y-3">
                            <form method="POST" action="{{ route('admin.blog.posts.toggle-featured', $post) }}" class="w-full">
                                @csrf
                                <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white font-medium rounded-lg transition-colors duration-200">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    {{ $post->is_featured ? 'Remove from Featured' : 'Mark as Featured' }}
                                </button>
                            </form>

                            <form method="POST" action="{{ route('admin.blog.posts.destroy', $post) }}" class="w-full" onsubmit="return confirm('Are you sure you want to delete this post? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors duration-200">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    Delete Post
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
