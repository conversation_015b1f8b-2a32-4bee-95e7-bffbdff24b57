@extends('layouts.admin')

@section('title', 'Contact Submission - ' . $contact->name)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <div class="flex items-center space-x-2">
                <a href="{{ route('admin.contact.index') }}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Contact Submission</h1>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $contact->status_badge }}">
                    {{ $contact->status_label }}
                </span>
            </div>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Submitted {{ $contact->created_at->format('F j, Y \a\t g:i A') }}</p>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Contact Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Message Content -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Message Details</h3>
                </div>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Subject</label>
                        <p class="text-gray-900 dark:text-white">{{ $contact->subject }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Message</label>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <p class="text-gray-900 dark:text-white whitespace-pre-wrap">{{ $contact->message }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Send Reply -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Send Reply</h3>

                <form method="POST" action="{{ route('admin.contact.send-reply', $contact) }}" class="space-y-4">
                    @csrf

                    <div>
                        <label for="reply_subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Subject <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="reply_subject" id="reply_subject" 
                               value="{{ old('reply_subject', 'Re: ' . $contact->subject) }}" required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                        @error('reply_subject')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="reply_message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Reply Message <span class="text-red-500">*</span>
                        </label>
                        <textarea name="reply_message" id="reply_message" rows="8" required
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="Type your reply here...">{{ old('reply_message') }}</textarea>
                        @error('reply_message')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="admin_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Admin Notes (Internal)
                        </label>
                        <textarea name="admin_notes" id="admin_notes" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="Add internal notes about this submission...">{{ old('admin_notes', $contact->admin_notes) }}</textarea>
                        @error('admin_notes')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="flex items-center space-x-3">
                        <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                            <i class="fas fa-paper-plane mr-2"></i>Send Reply
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Contact Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Contact Information</h3>

                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                        <p class="text-gray-900 dark:text-white">{{ $contact->name }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                        <p class="text-gray-900 dark:text-white">
                            <a href="mailto:{{ $contact->email }}" class="text-blue-600 dark:text-blue-400 hover:underline">
                                {{ $contact->email }}
                            </a>
                        </p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">IP Address</label>
                        <p class="text-gray-900 dark:text-white">{{ $contact->ip_address ?? 'N/A' }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">User Agent</label>
                        <p class="text-gray-900 dark:text-white text-xs">{{ Str::limit($contact->user_agent ?? 'N/A', 50) }}</p>
                    </div>
                </div>
            </div>

            <!-- Status Management -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Status Management</h3>

                <form method="POST" action="{{ route('admin.contact.update-status', $contact) }}" class="space-y-4">
                    @csrf
                    @method('PATCH')

                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Status
                        </label>
                        <select name="status" id="status" 
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="new" {{ $contact->status === 'new' ? 'selected' : '' }}>New</option>
                            <option value="read" {{ $contact->status === 'read' ? 'selected' : '' }}>Read</option>
                            <option value="replied" {{ $contact->status === 'replied' ? 'selected' : '' }}>Replied</option>
                            <option value="archived" {{ $contact->status === 'archived' ? 'selected' : '' }}>Archived</option>
                        </select>
                    </div>

                    <div>
                        <label for="admin_notes_status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Admin Notes
                        </label>
                        <textarea name="admin_notes" id="admin_notes_status" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="Add internal notes...">{{ $contact->admin_notes }}</textarea>
                    </div>

                    <button type="submit" class="w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                        <i class="fas fa-save mr-2"></i>Update Status
                    </button>
                </form>
            </div>

            <!-- Timestamps -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Timeline</h3>

                <div class="space-y-3 text-sm">
                    <div>
                        <label class="block font-medium text-gray-700 dark:text-gray-300">Submitted</label>
                        <p class="text-gray-900 dark:text-white">{{ $contact->created_at->format('M j, Y g:i A') }}</p>
                    </div>

                    @if($contact->read_at)
                    <div>
                        <label class="block font-medium text-gray-700 dark:text-gray-300">Read</label>
                        <p class="text-gray-900 dark:text-white">{{ $contact->read_at->format('M j, Y g:i A') }}</p>
                    </div>
                    @endif

                    @if($contact->replied_at)
                    <div>
                        <label class="block font-medium text-gray-700 dark:text-gray-300">Replied</label>
                        <p class="text-gray-900 dark:text-white">{{ $contact->replied_at->format('M j, Y g:i A') }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Actions</h3>

                <div class="space-y-2">
                    <form method="POST" action="{{ route('admin.contact.destroy', $contact) }}" 
                          onsubmit="return confirm('Are you sure you want to delete this contact submission? This action cannot be undone.')" 
                          class="w-full">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                            <i class="fas fa-trash mr-2"></i>Delete Submission
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
