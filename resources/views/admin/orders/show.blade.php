<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Order Details - ') . $order->order_number }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('admin.orders.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Orders
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Order Information -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Order Summary -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <dl class="space-y-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Order Number</dt>
                                        <dd class="text-sm text-gray-900 font-mono">{{ $order->order_number }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Status</dt>
                                        <dd>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                                @if($order->status === 'completed') bg-green-100 text-green-800
                                                @elseif($order->status === 'pending') bg-yellow-100 text-yellow-800
                                                @elseif($order->status === 'failed') bg-red-100 text-red-800
                                                @elseif($order->status === 'refunded') bg-purple-100 text-purple-800
                                                @else bg-gray-100 text-gray-800
                                                @endif">
                                                {{ ucfirst($order->status) }}
                                            </span>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Amount</dt>
                                        <dd class="text-lg font-semibold text-gray-900">${{ number_format($order->amount, 2) }} {{ strtoupper($order->currency) }}</dd>
                                    </div>
                                </dl>
                            </div>
                            
                            <div>
                                <dl class="space-y-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Payment Method</dt>
                                        <dd class="text-sm text-gray-900">{{ ucfirst(str_replace('_', ' ', $order->payment_method)) }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Order Date</dt>
                                        <dd class="text-sm text-gray-900">{{ $order->created_at->format('M d, Y \a\t g:i A') }}</dd>
                                    </div>
                                    @if($order->paid_at)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Paid Date</dt>
                                        <dd class="text-sm text-gray-900">{{ $order->paid_at->format('M d, Y \a\t g:i A') }}</dd>
                                    </div>
                                    @endif
                                </dl>
                            </div>
                        </div>

                        @if($order->payment_id)
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <dt class="text-sm font-medium text-gray-500">Payment ID</dt>
                            <dd class="text-sm text-gray-900 font-mono">{{ $order->payment_id }}</dd>
                        </div>
                        @endif
                    </div>

                    <!-- Customer Information -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
                        
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-lg font-medium text-gray-900">{{ $order->user->name }}</h4>
                                <p class="text-sm text-gray-600">{{ $order->user->email }}</p>
                                <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                                    <span>Member since {{ $order->user->created_at->format('M Y') }}</span>
                                    <span>•</span>
                                    <span>{{ $order->user->role }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Information -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Product Information</h3>
                        
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                @if($order->product->primaryImage)
                                    <img src="{{ Storage::url($order->product->primaryImage->image_path) }}" 
                                         alt="{{ $order->product->title }}" 
                                         class="w-20 h-20 object-cover rounded-lg">
                                @else
                                    <div class="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="flex-1">
                                <h4 class="text-lg font-medium text-gray-900">{{ $order->product->title }}</h4>
                                <p class="text-sm text-gray-600">{{ $order->product->category->name }}</p>
                                @if($order->product->short_description)
                                    <p class="text-sm text-gray-500 mt-2">{{ Str::limit($order->product->short_description, 150) }}</p>
                                @endif
                                <div class="mt-3 flex space-x-4">
                                    <a href="{{ route('admin.products.show', $order->product) }}" 
                                       class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                        View Product
                                    </a>
                                    <a href="{{ route('products.show', $order->product->slug) }}" 
                                       target="_blank"
                                       class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                        View on Site
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Details -->
                    @if($order->payment_details)
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Details</h3>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <pre class="text-sm text-gray-700 whitespace-pre-wrap">{{ json_encode($order->payment_details, JSON_PRETTY_PRINT) }}</pre>
                        </div>
                    </div>
                    @endif

                    <!-- Order Notes -->
                    @if($order->notes)
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Notes</h3>
                        <p class="text-gray-700">{{ $order->notes }}</p>
                    </div>
                    @endif
                </div>

                <!-- Sidebar Actions -->
                <div class="space-y-6">
                    <!-- Status Management -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Actions</h3>
                        
                        <form action="{{ route('admin.orders.update-status', $order) }}" method="POST" class="space-y-4">
                            @csrf
                            @method('PATCH')
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="pending" {{ $order->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="completed" {{ $order->status === 'completed' ? 'selected' : '' }}>Completed</option>
                                    <option value="failed" {{ $order->status === 'failed' ? 'selected' : '' }}>Failed</option>
                                    <option value="refunded" {{ $order->status === 'refunded' ? 'selected' : '' }}>Refunded</option>
                                    <option value="cancelled" {{ $order->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                                <textarea name="notes" rows="3" 
                                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                          placeholder="Add notes about this status change...">{{ $order->notes }}</textarea>
                            </div>
                            
                            <button type="submit" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Status
                            </button>
                        </form>

                        @if($order->payment_method === 'bank_transfer' && $order->status === 'pending')
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <form action="{{ route('admin.orders.confirm-bank-transfer', $order) }}" method="POST">
                                @csrf
                                <button type="submit" 
                                        class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                                        onclick="return confirm('Confirm this bank transfer payment?')">
                                    Confirm Bank Transfer
                                </button>
                            </form>
                        </div>
                        @endif
                    </div>

                    <!-- Quick Stats -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Stats</h3>
                        
                        @php
                            $customerStats = [
                                'total_orders' => $order->user->orders()->count(),
                                'total_spent' => $order->user->orders()->where('status', 'completed')->sum('amount'),
                                'first_order' => $order->user->orders()->oldest()->first(),
                            ];
                        @endphp
                        
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Total Orders</dt>
                                <dd class="text-sm text-gray-900">{{ $customerStats['total_orders'] }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Total Spent</dt>
                                <dd class="text-sm text-gray-900">${{ number_format($customerStats['total_spent'], 2) }}</dd>
                            </div>
                            @if($customerStats['first_order'])
                            <div>
                                <dt class="text-sm font-medium text-gray-500">First Order</dt>
                                <dd class="text-sm text-gray-900">{{ $customerStats['first_order']->created_at->format('M d, Y') }}</dd>
                            </div>
                            @endif
                        </dl>
                    </div>

                    <!-- Download Access -->
                    @if($order->status === 'completed')
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Download Access</h3>
                        
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Download Status</span>
                                <span class="text-sm font-medium text-green-600">Active</span>
                            </div>
                            
                            <a href="{{ route('products.download', $order->product->slug) }}" 
                               target="_blank"
                               class="w-full bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded text-center block">
                                Test Download
                            </a>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
