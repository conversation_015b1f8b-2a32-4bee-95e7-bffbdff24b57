<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Product: ') . $product->title }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('admin.products.show', $product) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    View Product
                </a>
                <a href="{{ route('admin.products.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Products
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <form action="{{ route('admin.products.update', $product) }}" method="POST" enctype="multipart/form-data" class="p-6">
            @csrf
            @method('PATCH')

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Basic Information</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Title *</label>
                                <input type="text" name="title" id="title" value="{{ old('title', $product->title) }}" required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
                                @error('title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="short_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Short Description</label>
                                <textarea name="short_description" id="short_description" rows="2"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">{{ old('short_description', $product->short_description) }}</textarea>
                                @error('short_description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description *</label>
                                <textarea name="description" id="description" rows="6" required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">{{ old('description', $product->description) }}</textarea>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Category *</label>
                                    <select name="category_id" id="category_id" required
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
                                        <option value="">Select Category</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ old('category_id', $product->category_id) == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Admin *</label>
                                    <select name="user_id" id="user_id" required
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
                                        <option value="">Select Admin</option>
                                        @foreach($admins as $admin)
                                            <option value="{{ $admin->id }}" {{ old('user_id', $product->user_id) == $admin->id ? 'selected' : '' }}>
                                                {{ $admin->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('user_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Pricing</h3>
                        
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <input type="checkbox" name="is_free" id="is_free" value="1" {{ old('is_free', $product->is_free) ? 'checked' : '' }}
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <label for="is_free" class="ml-2 text-sm text-gray-700 dark:text-gray-300">This is a free product</label>
                            </div>

                            <div id="price-field">
                                <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Price *</label>
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">$</span>
                                    </div>
                                    <input type="number" name="price" id="price" step="0.01" min="0" value="{{ old('price', $product->price) }}" required
                                        class="pl-7 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
                                </div>
                                @error('price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="demo_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Demo URL</label>
                                <input type="url" name="demo_url" id="demo_url" value="{{ old('demo_url', $product->demo_url) }}"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
                                @error('demo_url')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Current Files -->
                    @if($product->local_file_path || $product->file_urls)
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Current Files</h3>
                        
                        @if($product->local_file_path)
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Local File</label>
                            <div class="mt-1 flex items-center justify-between bg-white dark:bg-gray-600 p-2 rounded border">
                                <span class="text-sm">{{ basename($product->local_file_path) }}</span>
                                <a href="{{ Storage::url($product->local_file_path) }}" target="_blank" class="text-indigo-600 hover:text-indigo-800 text-sm">Download</a>
                            </div>
                        </div>
                        @endif

                        @if($product->file_urls)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">External URLs</label>
                            @foreach($product->file_urls as $url)
                            <div class="mt-1 flex items-center justify-between bg-white dark:bg-gray-600 p-2 rounded border">
                                <span class="text-sm truncate">{{ $url }}</span>
                                <a href="{{ $url }}" target="_blank" class="text-indigo-600 hover:text-indigo-800 text-sm">Open</a>
                            </div>
                            @endforeach
                        </div>
                        @endif
                    </div>
                    @endif

                    <!-- Update Files -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Update Product Files</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label for="local_file" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Replace Local File</label>
                                <input type="file" name="local_file" id="local_file" accept=".zip,.rar,.7z,.tar,.gz"
                                    class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                                <p class="mt-1 text-xs text-gray-500">Supported formats: ZIP, RAR, 7Z, TAR, GZ (Max: 100MB)</p>
                                @error('local_file')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Update External File URLs</label>
                                <div id="file-urls-container">
                                    @if(old('file_urls', $product->file_urls))
                                        @foreach(old('file_urls', $product->file_urls) as $index => $url)
                                        <div class="flex gap-2 {{ $index > 0 ? 'mt-2' : 'mt-1' }}">
                                            <input type="url" name="file_urls[]" value="{{ $url }}" placeholder="https://example.com/file.zip"
                                                class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
                                            @if($index > 0)
                                            <button type="button" onclick="removeFileUrl(this)" class="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">-</button>
                                            @else
                                            <button type="button" onclick="addFileUrl()" class="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">+</button>
                                            @endif
                                        </div>
                                        @endforeach
                                    @else
                                    <div class="flex gap-2 mt-1">
                                        <input type="url" name="file_urls[]" placeholder="https://example.com/file.zip"
                                            class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
                                        <button type="button" onclick="addFileUrl()" class="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">+</button>
                                    </div>
                                    @endif
                                </div>
                                @error('file_urls')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Status & Features -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Status & Features</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status *</label>
                                <select name="status" id="status" required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
                                    <option value="draft" {{ old('status', $product->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                                    <option value="published" {{ old('status', $product->status) == 'published' ? 'selected' : '' }}>Published</option>
                                    <option value="archived" {{ old('status', $product->status) == 'archived' ? 'selected' : '' }}>Archived</option>
                                </select>
                                @error('status')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="flex items-center space-x-6">
                                <div class="flex items-center">
                                    <input type="checkbox" name="is_featured" id="is_featured" value="1" {{ old('is_featured', $product->is_featured) ? 'checked' : '' }}
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <label for="is_featured" class="ml-2 text-sm text-gray-700 dark:text-gray-300">Featured Product</label>
                                </div>

                                <div class="flex items-center">
                                    <input type="checkbox" name="is_trending" id="is_trending" value="1" {{ old('is_trending', $product->is_trending) ? 'checked' : '' }}
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <label for="is_trending" class="ml-2 text-sm text-gray-700 dark:text-gray-300">Trending Product</label>
                                </div>
                            </div>

                            <div>
                                <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Tags</label>
                                <input type="text" name="tags" id="tags" value="{{ old('tags', is_array($product->tags) ? implode(', ', $product->tags) : $product->tags) }}" placeholder="tag1, tag2, tag3"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
                                <p class="mt-1 text-xs text-gray-500">Separate tags with commas</p>
                                @error('tags')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Images -->
            @if($product->images->count() > 0)
            <div class="mt-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Current Images</h3>

                <div id="current-images-grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    @foreach($product->images as $image)
                    <div class="relative group" data-image-id="{{ $image->id }}">
                        <img src="{{ Storage::url($image->image_path) }}" alt="{{ $image->alt_text }}" class="w-full h-32 object-cover rounded-lg">

                        <!-- Primary badge -->
                        @if($image->is_primary)
                        <div class="absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded">Primary</div>
                        @endif

                        <!-- Actions overlay -->
                        <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center space-x-2">
                            <button type="button" onclick="setPrimaryImage({{ $image->id }})" class="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs">
                                Set Primary
                            </button>
                            <button type="button" onclick="deleteImage({{ $image->id }})" class="bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded text-xs">
                                Delete
                            </button>
                        </div>

                        <!-- Alt text -->
                        <div class="mt-1">
                            <input type="text" value="{{ $image->alt_text }}" placeholder="Alt text"
                                class="w-full text-xs rounded border-gray-300 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                                onchange="updateImageAltText({{ $image->id }}, this.value)">
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Hidden inputs for image management -->
                <input type="hidden" name="primary_image_id" id="primary_image_id" value="{{ $product->images->where('is_primary', true)->first()?->id }}">
                <div id="remove-images-container"></div>
            </div>
            @endif

            <!-- Add New Images -->
            <div class="mt-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Add New Images</h3>

                <div class="space-y-4">
                    <div>
                        <label for="new_images" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Upload New Images</label>
                        <input type="file" name="new_images[]" id="new_images" multiple accept="image/*"
                            class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                        <p class="mt-1 text-xs text-gray-500">Supported formats: JPEG, PNG, JPG, GIF, WebP (Max: 5MB each, 10 images max)</p>
                        @error('new_images')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div id="new-image-preview-container" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">New Image Preview & Alt Text</label>
                        <div id="new-image-previews" class="grid grid-cols-2 gap-4 mt-2"></div>
                    </div>
                </div>
            </div>

            <!-- SEO Section -->
            <div class="mt-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">SEO Settings</h3>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div>
                        <label for="meta_title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Meta Title</label>
                        <input type="text" name="meta_title" id="meta_title" value="{{ old('meta_title', $product->meta_title) }}"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
                        @error('meta_title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="meta_keywords" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Meta Keywords</label>
                        <input type="text" name="meta_keywords" id="meta_keywords" value="{{ old('meta_keywords', is_array($product->meta_keywords) ? implode(', ', $product->meta_keywords) : $product->meta_keywords) }}" placeholder="keyword1, keyword2, keyword3"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
                        @error('meta_keywords')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="lg:col-span-2">
                        <label for="meta_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Meta Description</label>
                        <textarea name="meta_description" id="meta_description" rows="3"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">{{ old('meta_description', $product->meta_description) }}</textarea>
                        @error('meta_description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="mt-6 flex justify-end space-x-3">
                <a href="{{ route('admin.products.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                    Cancel
                </a>
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Update Product
                </button>
            </div>
        </form>
            </div>
        </div>
    </div>

<script>
// Handle free product checkbox
document.getElementById('is_free').addEventListener('change', function() {
    const priceField = document.getElementById('price-field');
    const priceInput = document.getElementById('price');

    if (this.checked) {
        priceField.style.display = 'none';
        priceInput.value = '0.00';
        priceInput.removeAttribute('required');
    } else {
        priceField.style.display = 'block';
        priceInput.setAttribute('required', 'required');
    }
});

// Add file URL input
function addFileUrl() {
    const container = document.getElementById('file-urls-container');
    const div = document.createElement('div');
    div.className = 'flex gap-2 mt-2';
    div.innerHTML = `
        <input type="url" name="file_urls[]" placeholder="https://example.com/file.zip"
            class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
        <button type="button" onclick="removeFileUrl(this)" class="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">-</button>
    `;
    container.appendChild(div);
}

function removeFileUrl(button) {
    button.parentElement.remove();
}

// Handle new image preview
document.getElementById('new_images').addEventListener('change', function(e) {
    const container = document.getElementById('new-image-preview-container');
    const previews = document.getElementById('new-image-previews');

    previews.innerHTML = '';

    if (e.target.files.length > 0) {
        container.classList.remove('hidden');

        Array.from(e.target.files).forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const div = document.createElement('div');
                div.className = 'space-y-2';
                div.innerHTML = `
                    <img src="${e.target.result}" class="w-full h-24 object-cover rounded">
                    <input type="text" name="new_image_alt_texts[]" placeholder="Alt text for new image ${index + 1}"
                        class="w-full text-xs rounded border-gray-300 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
                `;
                previews.appendChild(div);
            };
            reader.readAsDataURL(file);
        });
    } else {
        container.classList.add('hidden');
    }
});

// Image management functions
function setPrimaryImage(imageId) {
    // Remove primary badge from all images
    document.querySelectorAll('.absolute.top-2.left-2').forEach(badge => {
        if (badge.textContent === 'Primary') {
            badge.remove();
        }
    });

    // Add primary badge to selected image
    const imageContainer = document.querySelector(`[data-image-id="${imageId}"]`);
    const badge = document.createElement('div');
    badge.className = 'absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded';
    badge.textContent = 'Primary';
    imageContainer.appendChild(badge);

    // Update hidden input
    document.getElementById('primary_image_id').value = imageId;
}

function deleteImage(imageId) {
    if (confirm('Are you sure you want to delete this image?')) {
        // Hide the image container
        const imageContainer = document.querySelector(`[data-image-id="${imageId}"]`);
        imageContainer.style.display = 'none';

        // Add to remove list
        const removeContainer = document.getElementById('remove-images-container');
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'remove_images[]';
        input.value = imageId;
        removeContainer.appendChild(input);

        // If this was the primary image, clear the primary selection
        const primaryInput = document.getElementById('primary_image_id');
        if (primaryInput.value == imageId) {
            primaryInput.value = '';
        }
    }
}

function updateImageAltText(imageId, altText) {
    // This could be enhanced to save alt text changes via AJAX
    console.log(`Updated alt text for image ${imageId}: ${altText}`);
}

// Initialize free product state
document.addEventListener('DOMContentLoaded', function() {
    const isFreeCheckbox = document.getElementById('is_free');
    if (isFreeCheckbox.checked) {
        isFreeCheckbox.dispatchEvent(new Event('change'));
    }
});
</script>
</x-app-layout>
