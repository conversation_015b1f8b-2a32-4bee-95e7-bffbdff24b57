@extends('layouts.admin')

@section('title', 'Edit Product')

@section('breadcrumbs')
    <a href="{{ route('admin.dashboard') }}" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">Admin</a>
    <span class="text-gray-400 dark:text-gray-500 mx-2">/</span>
    <a href="{{ route('admin.products.index') }}" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">Products</a>
    <span class="text-gray-400 dark:text-gray-500 mx-2">/</span>
    <a href="{{ route('admin.products.show', $product) }}" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">{{ $product->title }}</a>
    <span class="text-gray-400 dark:text-gray-500 mx-2">/</span>
    <span class="text-gray-900 dark:text-white font-medium">Edit</span>
@endsection

@section('content')
    <div class="p-6">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Product</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">{{ $product->title }}</p>
                </div>
                <div class="flex space-x-3 mt-4 sm:mt-0">
                    <a href="{{ route('admin.products.show', $product) }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg shadow-sm transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        View Product
                    </a>
                    <a href="{{ route('admin.products.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg shadow-sm transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Products
                    </a>
                </div>
            </div>
        </div>

        <div class="max-w-7xl mx-auto">
            <!-- Display validation errors -->
            @if ($errors->any())
                <div class="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                There were {{ $errors->count() }} error(s) with your submission
                            </h3>
                            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                <ul class="list-disc pl-5 space-y-1">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <form action="{{ route('admin.products.update', $product) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PATCH')

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Left Column -->
                    <div class="space-y-6">
                        <!-- Basic Information Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="p-6">
                                <div class="flex items-center mb-6">
                                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Basic Information</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Product details and description</p>
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    <div>
                                        <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Title *</label>
                                        <input type="text" name="title" id="title" value="{{ old('title', $product->title) }}" required
                                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200">
                                        @error('title')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="short_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Short Description</label>
                                        <textarea name="short_description" id="short_description" rows="2"
                                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200">{{ old('short_description', $product->short_description) }}</textarea>
                                        @error('short_description')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description *</label>
                                        <textarea name="description" id="description" rows="6" required
                                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200">{{ old('description', $product->description) }}</textarea>
                                        @error('description')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category *</label>
                                            <select name="category_id" id="category_id" required
                                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200">
                                                <option value="">Select Category</option>
                                                @foreach($categories as $category)
                                                    <option value="{{ $category->id }}" {{ old('category_id', $product->category_id) == $category->id ? 'selected' : '' }}>
                                                        {{ $category->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('category_id')
                                                <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <div>
                                            <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Admin *</label>
                                            <select name="user_id" id="user_id" required
                                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200">
                                                <option value="">Select Admin</option>
                                                @foreach($admins as $admin)
                                                    <option value="{{ $admin->id }}" {{ old('user_id', $product->user_id) == $admin->id ? 'selected' : '' }}>
                                                        {{ $admin->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('user_id')
                                                <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="p-6">
                                <div class="flex items-center mb-6">
                                    <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Pricing</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Set product pricing</p>
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    <div class="flex items-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                        <input type="checkbox" name="is_free" id="is_free" value="1" {{ old('is_free', $product->is_free) ? 'checked' : '' }}
                                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="is_free" class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">This is a free product</label>
                                    </div>

                                    <div id="price-field">
                                        <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Price *</label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <span class="text-gray-500 dark:text-gray-400 text-sm">$</span>
                                            </div>
                                            <input type="number" name="price" id="price" step="0.01" min="0" value="{{ old('price', $product->price) }}" required
                                                class="w-full pl-8 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200">
                                        </div>
                                        @error('price')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="demo_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Demo URL</label>
                                        <input type="url" name="demo_url" id="demo_url" value="{{ old('demo_url', $product->demo_url) }}"
                                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                                            placeholder="https://example.com/demo">
                                        @error('demo_url')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="space-y-6">
                        <!-- Current Files Card -->
                        @if($product->local_file_path || $product->file_urls)
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="p-6">
                                <div class="flex items-center mb-6">
                                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Current Files</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Existing product files</p>
                                    </div>
                                </div>

                                @if($product->local_file_path)
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Local File</label>
                                    <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 text-gray-500 dark:text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ basename($product->local_file_path) }}</span>
                                        </div>
                                        <a href="{{ Storage::url($product->local_file_path) }}" target="_blank"
                                           class="inline-flex items-center px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            Download
                                        </a>
                                    </div>
                                </div>
                                @endif

                                @if($product->file_urls)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">External URLs</label>
                                    <div class="space-y-2">
                                        @foreach($product->file_urls as $url)
                                        <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                            <div class="flex items-center min-w-0 flex-1">
                                                <svg class="w-5 h-5 text-gray-500 dark:text-gray-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                                </svg>
                                                <span class="text-sm text-gray-900 dark:text-white truncate">{{ $url }}</span>
                                            </div>
                                            <a href="{{ $url }}" target="_blank"
                                               class="inline-flex items-center px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 ml-3">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                                </svg>
                                                Open
                                            </a>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                        @endif

                        <!-- Update Files Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="p-6">
                                <div class="flex items-center mb-6">
                                    <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Update Product Files</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Upload new files or update URLs</p>
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    <div>
                                        <label for="local_file" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Replace Local File</label>
                                        <div class="relative">
                                            <input type="file" name="local_file" id="local_file" accept=".zip,.rar,.7z,.tar,.gz"
                                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 dark:file:bg-blue-900/20 file:text-blue-700 dark:file:text-blue-300 hover:file:bg-blue-100 dark:hover:file:bg-blue-900/30">
                                        </div>
                                        <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">Supported formats: ZIP, RAR, 7Z, TAR, GZ (Max: 100MB)</p>
                                        @error('local_file')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Update External File URLs</label>
                                        <div id="file-urls-container">
                                            @if(old('file_urls', $product->file_urls))
                                                @foreach(old('file_urls', $product->file_urls) as $index => $url)
                                                <div class="flex gap-2 {{ $index > 0 ? 'mt-2' : '' }}">
                                                    <input type="url" name="file_urls[]" value="{{ $url }}" placeholder="https://example.com/file.zip"
                                                        class="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                                                        oninput="validateUrl(this)">
                                                    @if($index > 0)
                                                    <button type="button" onclick="removeFileUrl(this)"
                                                            class="px-4 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                                        </svg>
                                                    </button>
                                                    @else
                                                    <button type="button" onclick="addFileUrl()"
                                                            class="px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                        </svg>
                                                    </button>
                                                    @endif
                                                </div>
                                                @endforeach
                                            @else
                                            <div class="flex gap-2">
                                                <input type="url" name="file_urls[]" placeholder="https://example.com/file.zip"
                                                    class="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                                                    oninput="validateUrl(this)">
                                                <button type="button" onclick="addFileUrl()"
                                                        class="px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                            @endif
                                        </div>
                                        @error('file_urls')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Status & Features Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="p-6">
                                <div class="flex items-center mb-6">
                                    <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Status & Features</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Product status and visibility</p>
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    <div>
                                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status *</label>
                                        <select name="status" id="status" required
                                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200">
                                            <option value="draft" {{ old('status', $product->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                                            <option value="published" {{ old('status', $product->status) == 'published' ? 'selected' : '' }}>Published</option>
                                            <option value="archived" {{ old('status', $product->status) == 'archived' ? 'selected' : '' }}>Archived</option>
                                        </select>
                                        @error('status')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div class="flex items-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                            <input type="checkbox" name="is_featured" id="is_featured" value="1" {{ old('is_featured', $product->is_featured) ? 'checked' : '' }}
                                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                            <label for="is_featured" class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">Featured Product</label>
                                        </div>

                                        <div class="flex items-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                            <input type="checkbox" name="is_trending" id="is_trending" value="1" {{ old('is_trending', $product->is_trending) ? 'checked' : '' }}
                                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                            <label for="is_trending" class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">Trending Product</label>
                                        </div>
                                    </div>

                                    <div>
                                        <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</label>
                                        <input type="text" name="tags" id="tags" value="{{ old('tags', is_array($product->tags) ? implode(', ', $product->tags) : $product->tags) }}" placeholder="tag1, tag2, tag3"
                                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200">
                                        <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">Separate tags with commas</p>
                                        @error('tags')
                                            <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Images Card -->
                @if($product->images->count() > 0)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mt-6">
                    <div class="p-6">
                        <div class="flex items-center mb-6">
                            <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Current Images</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $product->images->count() }} images uploaded</p>
                            </div>
                        </div>

                        <div id="current-images-grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            @foreach($product->images as $image)
                            <div class="relative group" data-image-id="{{ $image->id }}">
                                <img src="{{ Storage::url($image->image_path) }}" alt="{{ $image->alt_text }}"
                                     class="w-full h-32 object-cover rounded-lg border border-gray-200 dark:border-gray-600">

                                <!-- Primary badge -->
                                @if($image->is_primary)
                                <div class="absolute top-2 left-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs px-2 py-1 rounded-full font-medium">Primary</div>
                                @endif

                                <!-- Actions overlay -->
                                <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center space-x-2">
                                    <button type="button" onclick="setPrimaryImage({{ $image->id }})"
                                            class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200">
                                        Set Primary
                                    </button>
                                    <button type="button" onclick="deleteImage({{ $image->id }})"
                                            class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200">
                                        Delete
                                    </button>
                                </div>

                                <!-- Alt text -->
                                <div class="mt-2">
                                    <input type="text" value="{{ $image->alt_text }}" placeholder="Alt text"
                                        class="w-full text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                                        onchange="updateImageAltText({{ $image->id }}, this.value)">
                                </div>
                            </div>
                            @endforeach
                        </div>

                        <!-- Hidden inputs for image management -->
                        <input type="hidden" name="primary_image_id" id="primary_image_id" value="{{ $product->images->where('is_primary', true)->first()?->id }}">
                        <div id="remove-images-container"></div>
                    </div>
                </div>
                @endif

                <!-- Add New Images Card -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mt-6">
                    <div class="p-6">
                        <div class="flex items-center mb-6">
                            <div class="w-10 h-10 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Add New Images</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Upload additional product images</p>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div>
                                <label for="new_images" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Upload New Images</label>
                                <input type="file" name="new_images[]" id="new_images" multiple accept="image/*"
                                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 dark:file:bg-blue-900/20 file:text-blue-700 dark:file:text-blue-300 hover:file:bg-blue-100 dark:hover:file:bg-blue-900/30">
                                <div class="mt-2 space-y-1">
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Supported formats: JPEG, PNG, JPG, GIF, WebP (Max: 5MB each, 10 images max)</p>
                                    <p class="text-xs text-blue-600 dark:text-blue-400">💡 Tip: Images will be uploaded when you click "Update Product". If there are validation errors, you'll need to re-select your images.</p>
                                </div>
                                @error('new_images')
                                    <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <div id="new-image-preview-container" class="hidden">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">New Image Preview & Alt Text</label>
                                <div id="new-image-previews" class="grid grid-cols-2 gap-4"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO Settings Card -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mt-6">
                    <div class="p-6">
                        <div class="flex items-center mb-6">
                            <div class="w-10 h-10 bg-gradient-to-r from-pink-500 to-rose-500 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">SEO Settings</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Search engine optimization</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <div>
                                <label for="meta_title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Meta Title</label>
                                <input type="text" name="meta_title" id="meta_title" value="{{ old('meta_title', $product->meta_title) }}"
                                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200">
                                @error('meta_title')
                                    <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="meta_keywords" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Meta Keywords</label>
                                <input type="text" name="meta_keywords" id="meta_keywords" value="{{ old('meta_keywords', is_array($product->meta_keywords) ? implode(', ', $product->meta_keywords) : $product->meta_keywords) }}" placeholder="keyword1, keyword2, keyword3"
                                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200">
                                @error('meta_keywords')
                                    <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="lg:col-span-2">
                                <label for="meta_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Meta Description</label>
                                <textarea name="meta_description" id="meta_description" rows="3"
                                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200">{{ old('meta_description', $product->meta_description) }}</textarea>
                                @error('meta_description')
                                    <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-end mt-8">
                    <a href="{{ route('admin.products.index') }}"
                       class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 font-medium rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Cancel
                    </a>
                    <button type="submit"
                            class="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Update Product
                    </button>
                </div>
        </form>
            </div>
        </div>
    </div>

<script>
// Handle free product checkbox
document.getElementById('is_free').addEventListener('change', function() {
    const priceField = document.getElementById('price-field');
    const priceInput = document.getElementById('price');

    if (this.checked) {
        priceField.style.display = 'none';
        priceInput.value = '0.00';
        priceInput.removeAttribute('required');
    } else {
        priceField.style.display = 'block';
        priceInput.setAttribute('required', 'required');
    }
});

// Add file URL input
function addFileUrl() {
    const container = document.getElementById('file-urls-container');
    const div = document.createElement('div');
    div.className = 'flex gap-2 mt-2';
    div.innerHTML = `
        <input type="url" name="file_urls[]" placeholder="https://example.com/file.zip"
            class="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
            oninput="validateUrl(this)">
        <button type="button" onclick="removeFileUrl(this)"
                class="px-4 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    `;
    container.appendChild(div);
}

function removeFileUrl(button) {
    button.parentElement.remove();
}

function validateUrl(input) {
    const value = input.value.trim();

    // If empty, reset styling
    if (!value) {
        input.classList.remove('border-red-500', 'border-green-500');
        input.classList.add('border-gray-300', 'dark:border-gray-600');
        return;
    }

    // Basic URL validation
    try {
        new URL(value);
        // Valid URL
        input.classList.remove('border-red-500', 'border-gray-300', 'dark:border-gray-600');
        input.classList.add('border-green-500');
    } catch (e) {
        // Invalid URL
        input.classList.remove('border-green-500', 'border-gray-300', 'dark:border-gray-600');
        input.classList.add('border-red-500');
    }
}

// Add validation to existing URL inputs
document.addEventListener('DOMContentLoaded', function() {
    const existingUrlInputs = document.querySelectorAll('input[name="file_urls[]"]');
    existingUrlInputs.forEach(input => {
        input.addEventListener('input', function() {
            validateUrl(this);
        });
        // Validate on page load
        validateUrl(input);
    });
});

// Handle new image preview
document.getElementById('new_images').addEventListener('change', function(e) {
    const container = document.getElementById('new-image-preview-container');
    const previews = document.getElementById('new-image-previews');

    previews.innerHTML = '';

    if (e.target.files.length > 0) {
        container.classList.remove('hidden');

        // Validate file count
        if (e.target.files.length > 10) {
            alert('You can only upload a maximum of 10 images at once.');
            e.target.value = '';
            container.classList.add('hidden');
            return;
        }

        Array.from(e.target.files).forEach((file, index) => {
            // Validate file size (5MB max)
            if (file.size > 5 * 1024 * 1024) {
                alert(`File "${file.name}" is too large. Maximum size is 5MB.`);
                e.target.value = '';
                container.classList.add('hidden');
                return;
            }

            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                alert(`File "${file.name}" is not a supported image format. Supported formats: JPEG, PNG, JPG, GIF, WebP.`);
                e.target.value = '';
                container.classList.add('hidden');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const div = document.createElement('div');
                div.className = 'space-y-2';
                div.innerHTML = `
                    <div class="relative">
                        <img src="${e.target.result}" class="w-full h-24 object-cover rounded border border-gray-300 dark:border-gray-600">
                        <div class="absolute top-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                            ${Math.round(file.size / 1024)}KB
                        </div>
                    </div>
                    <input type="text" name="new_image_alt_texts[]" placeholder="Alt text for ${file.name}"
                        class="w-full text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200">
                `;
                previews.appendChild(div);
            };
            reader.readAsDataURL(file);
        });
    } else {
        container.classList.add('hidden');
    }
});

// Add form submission handler to show loading state and clean up empty URLs
document.querySelector('form').addEventListener('submit', function(e) {
    // Remove empty URL fields before submission
    const urlInputs = document.querySelectorAll('input[name="file_urls[]"]');
    urlInputs.forEach(input => {
        if (!input.value.trim()) {
            input.remove();
        }
    });

    const submitButton = document.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;

    submitButton.disabled = true;
    submitButton.innerHTML = `
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Updating Product...
    `;

    // Re-enable button after 30 seconds as fallback
    setTimeout(() => {
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
    }, 30000);
});

// Image management functions
function setPrimaryImage(imageId) {
    // Remove primary badge from all images
    document.querySelectorAll('.absolute.top-2.left-2').forEach(badge => {
        if (badge.textContent === 'Primary') {
            badge.remove();
        }
    });

    // Add primary badge to selected image
    const imageContainer = document.querySelector(`[data-image-id="${imageId}"]`);
    const badge = document.createElement('div');
    badge.className = 'absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded';
    badge.textContent = 'Primary';
    imageContainer.appendChild(badge);

    // Update hidden input
    document.getElementById('primary_image_id').value = imageId;
}

function deleteImage(imageId) {
    if (confirm('Are you sure you want to delete this image?')) {
        // Hide the image container
        const imageContainer = document.querySelector(`[data-image-id="${imageId}"]`);
        imageContainer.style.display = 'none';

        // Add to remove list
        const removeContainer = document.getElementById('remove-images-container');
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'remove_images[]';
        input.value = imageId;
        removeContainer.appendChild(input);

        // If this was the primary image, clear the primary selection
        const primaryInput = document.getElementById('primary_image_id');
        if (primaryInput.value == imageId) {
            primaryInput.value = '';
        }
    }
}

function updateImageAltText(imageId, altText) {
    // This could be enhanced to save alt text changes via AJAX
    console.log(`Updated alt text for image ${imageId}: ${altText}`);
}

// Initialize free product state
document.addEventListener('DOMContentLoaded', function() {
    const isFreeCheckbox = document.getElementById('is_free');
    if (isFreeCheckbox.checked) {
        isFreeCheckbox.dispatchEvent(new Event('change'));
    }
});
</script>
@endsection
