<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Product Details') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('admin.products.edit', $product) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Edit Product
                </a>
                <a href="{{ route('admin.products.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Products
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Product Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Info -->
            <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">{{ $product->title }}</h2>
                
                @if($product->short_description)
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Short Description</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ $product->short_description }}</p>
                </div>
                @endif
                
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</h3>
                    <div class="text-gray-700 dark:text-gray-300 prose max-w-none">
                        {!! nl2br(e($product->description)) !!}
                    </div>
                </div>

                @if($product->tags)
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</h3>
                    <div class="flex flex-wrap gap-2">
                        @foreach(is_array($product->tags) ? $product->tags : explode(',', $product->tags) as $tag)
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">{{ trim($tag) }}</span>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>

            <!-- Product Images -->
            @if($product->images->count() > 0)
            <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Product Images</h3>
                
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                    @foreach($product->images->sortBy('sort_order') as $image)
                    <div class="relative">
                        <img src="{{ Storage::url($image->image_path) }}" alt="{{ $image->alt_text }}" 
                            class="w-full h-32 object-cover rounded-lg cursor-pointer hover:opacity-75 transition-opacity"
                            onclick="openImageModal('{{ Storage::url($image->image_path) }}', '{{ $image->alt_text }}')">
                        
                        @if($image->is_primary)
                        <div class="absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded">Primary</div>
                        @endif
                        
                        @if($image->alt_text)
                        <div class="mt-1 text-xs text-gray-600 dark:text-gray-400 truncate">{{ $image->alt_text }}</div>
                        @endif
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Product Files -->
            @if($product->local_file_path || $product->file_urls)
            <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Product Files</h3>
                
                @if($product->local_file_path)
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Local File</h4>
                    <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-3 rounded">
                        <span class="text-sm">{{ basename($product->local_file_path) }}</span>
                        <a href="{{ Storage::url($product->local_file_path) }}" target="_blank" 
                            class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">Download</a>
                    </div>
                </div>
                @endif

                @if($product->file_urls)
                <div>
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">External URLs</h4>
                    @foreach($product->file_urls as $url)
                    <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-3 rounded mb-2">
                        <span class="text-sm truncate">{{ $url }}</span>
                        <a href="{{ $url }}" target="_blank" 
                            class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">Open</a>
                    </div>
                    @endforeach
                </div>
                @endif
            </div>
            @endif

            <!-- SEO Information -->
            @if($product->meta_title || $product->meta_description || $product->meta_keywords)
            <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">SEO Information</h3>
                
                @if($product->meta_title)
                <div class="mb-3">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Meta Title:</span>
                    <p class="text-gray-900 dark:text-white">{{ $product->meta_title }}</p>
                </div>
                @endif

                @if($product->meta_description)
                <div class="mb-3">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Meta Description:</span>
                    <p class="text-gray-900 dark:text-white">{{ $product->meta_description }}</p>
                </div>
                @endif

                @if($product->meta_keywords)
                <div>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Meta Keywords:</span>
                    <div class="flex flex-wrap gap-2 mt-1">
                        @foreach(is_array($product->meta_keywords) ? $product->meta_keywords : explode(',', $product->meta_keywords) as $keyword)
                        <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">{{ trim($keyword) }}</span>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Product Details -->
            <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Product Details</h3>
                
                <div class="space-y-3">
                    <div>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Category:</span>
                        <p class="text-gray-900 dark:text-white">{{ $product->category->name }}</p>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Admin:</span>
                        <p class="text-gray-900 dark:text-white">{{ $product->user->name }}</p>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Price:</span>
                        <p class="text-gray-900 dark:text-white">
                            @if($product->is_free)
                                <span class="text-green-600 font-semibold">Free</span>
                            @else
                                ${{ number_format($product->price, 2) }}
                            @endif
                        </p>
                    </div>

                    @if($product->demo_url)
                    <div>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Demo URL:</span>
                        <p><a href="{{ $product->demo_url }}" target="_blank" class="text-indigo-600 hover:text-indigo-800">View Demo</a></p>
                    </div>
                    @endif
                    
                    <div>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Status:</span>
                        <span class="px-2 py-1 rounded text-sm
                            @if($product->status === 'published') bg-green-100 text-green-800
                            @elseif($product->status === 'draft') bg-yellow-100 text-yellow-800
                            @else bg-red-100 text-red-800
                            @endif">
                            {{ ucfirst($product->status) }}
                        </span>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Featured:</span>
                            <span class="text-gray-900 dark:text-white">{{ $product->is_featured ? 'Yes' : 'No' }}</span>
                        </div>
                        
                        <div>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Trending:</span>
                            <span class="text-gray-900 dark:text-white">{{ $product->is_trending ? 'Yes' : 'No' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Statistics</h3>
                
                <div class="space-y-3">
                    <div>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Views:</span>
                        <p class="text-gray-900 dark:text-white">{{ number_format($product->view_count) }}</p>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Downloads:</span>
                        <p class="text-gray-900 dark:text-white">{{ number_format($product->download_count) }}</p>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Created:</span>
                        <p class="text-gray-900 dark:text-white">{{ $product->created_at->format('M d, Y') }}</p>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Updated:</span>
                        <p class="text-gray-900 dark:text-white">{{ $product->updated_at->format('M d, Y') }}</p>
                    </div>

                    @if($product->published_at)
                    <div>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Published:</span>
                        <p class="text-gray-900 dark:text-white">{{ $product->published_at->format('M d, Y') }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Actions</h3>
                
                <div class="space-y-3">
                    @if($product->status === 'draft')
                        <form action="{{ route('admin.products.approve', $product) }}" method="POST" class="w-full">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                Approve Product
                            </button>
                        </form>
                    @endif
                    
                    @if($product->status === 'published')
                        <form action="{{ route('admin.products.reject', $product) }}" method="POST" class="w-full">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                Reject Product
                            </button>
                        </form>
                    @endif
                    
                    <form action="{{ route('admin.products.toggle-featured', $product) }}" method="POST" class="w-full">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="w-full bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                            {{ $product->is_featured ? 'Remove from Featured' : 'Make Featured' }}
                        </button>
                    </form>
                    
                    <form action="{{ route('admin.products.destroy', $product) }}" method="POST" class="w-full" onsubmit="return confirm('Are you sure you want to delete this product?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            Delete Product
                        </button>
                    </form>
                </div>
            </div>
        </div>
        </div>
    </div>

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center p-4">
    <div class="relative max-w-4xl max-h-full">
        <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white text-2xl font-bold z-10 bg-black bg-opacity-50 rounded-full w-10 h-10 flex items-center justify-center hover:bg-opacity-75">
            ×
        </button>
        <img id="modalImage" src="" alt="" class="max-w-full max-h-full object-contain">
    </div>
</div>

<script>
function openImageModal(src, alt) {
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    
    modalImage.src = src;
    modalImage.alt = alt;
    modal.classList.remove('hidden');
    
    // Close modal on click outside image
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeImageModal();
        }
    });
}

function closeImageModal() {
    const modal = document.getElementById('imageModal');
    modal.classList.add('hidden');
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImageModal();
    }
});
</script>
</x-app-layout>
