@extends('layouts.admin')

@section('title', 'Product Details')

@section('breadcrumbs')
    <a href="{{ route('admin.dashboard') }}" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">Admin</a>
    <span class="text-gray-400 dark:text-gray-500 mx-2">/</span>
    <a href="{{ route('admin.products.index') }}" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">Products</a>
    <span class="text-gray-400 dark:text-gray-500 mx-2">/</span>
    <span class="text-gray-900 dark:text-white font-medium">{{ $product->title }}</span>
@endsection

@section('content')
    <div class="p-6">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $product->title }}</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">Product details and management</p>
                </div>
                <div class="flex space-x-3 mt-4 sm:mt-0">
                    <a href="{{ route('admin.products.edit', $product) }}"
                       class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg shadow-sm transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Product
                    </a>
                    <a href="{{ route('admin.products.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg shadow-sm transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Products
                    </a>
                </div>
            </div>
        </div>

        <div class="max-w-7xl mx-auto">

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Product Information -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Basic Info Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">{{ $product->title }}</h2>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Product Information</p>
                                </div>
                            </div>

                            @if($product->short_description)
                            <div class="mb-6">
                                <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Short Description</h3>
                                <p class="text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">{{ $product->short_description }}</p>
                            </div>
                            @endif

                            <div class="mb-6">
                                <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</h3>
                                <div class="text-gray-700 dark:text-gray-300 prose max-w-none bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                                    {!! nl2br(e($product->description)) !!}
                                </div>
                            </div>

                            @if($product->tags)
                            <div>
                                <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Tags</h3>
                                <div class="flex flex-wrap gap-2">
                                    @foreach(is_array($product->tags) ? $product->tags : explode(',', $product->tags) as $tag)
                                    <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-sm rounded-full font-medium">{{ trim($tag) }}</span>
                                    @endforeach
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Product Images Card -->
                    @if($product->images->count() > 0)
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Product Images</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ $product->images->count() }} images</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                                @foreach($product->images->sortBy('sort_order') as $image)
                                <div class="relative group">
                                    <img src="{{ Storage::url($image->image_path) }}" alt="{{ $image->alt_text }}"
                                        class="w-full h-32 object-cover rounded-lg cursor-pointer hover:opacity-75 transition-all duration-200 group-hover:scale-105"
                                        onclick="openImageModal('{{ Storage::url($image->image_path) }}', '{{ $image->alt_text }}')">

                                    @if($image->is_primary)
                                    <div class="absolute top-2 left-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs px-2 py-1 rounded-full font-medium">Primary</div>
                                    @endif

                                    @if($image->alt_text)
                                    <div class="mt-2 text-xs text-gray-600 dark:text-gray-400 truncate">{{ $image->alt_text }}</div>
                                    @endif
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Product Files Card -->
                    @if($product->local_file_path || $product->file_urls)
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Product Files</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Downloadable content</p>
                                </div>
                            </div>

                            @if($product->local_file_path)
                            <div class="mb-6">
                                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Local File</h4>
                                <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-gray-500 dark:text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ basename($product->local_file_path) }}</span>
                                    </div>
                                    <a href="{{ Storage::url($product->local_file_path) }}" target="_blank"
                                        class="inline-flex items-center px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        Download
                                    </a>
                                </div>
                            </div>
                            @endif

                            @if($product->file_urls)
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">External URLs</h4>
                                <div class="space-y-2">
                                    @foreach($product->file_urls as $url)
                                    <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                        <div class="flex items-center min-w-0 flex-1">
                                            <svg class="w-5 h-5 text-gray-500 dark:text-gray-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                            </svg>
                                            <span class="text-sm text-gray-900 dark:text-white truncate">{{ $url }}</span>
                                        </div>
                                        <a href="{{ $url }}" target="_blank"
                                            class="inline-flex items-center px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 ml-3">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                            </svg>
                                            Open
                                        </a>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endif

                    <!-- SEO Information Card -->
                    @if($product->meta_title || $product->meta_description || $product->meta_keywords)
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">SEO Information</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Search engine optimization</p>
                                </div>
                            </div>

                            @if($product->meta_title)
                            <div class="mb-4">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Meta Title:</span>
                                <p class="text-gray-900 dark:text-white mt-1 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">{{ $product->meta_title }}</p>
                            </div>
                            @endif

                            @if($product->meta_description)
                            <div class="mb-4">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Meta Description:</span>
                                <p class="text-gray-900 dark:text-white mt-1 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">{{ $product->meta_description }}</p>
                            </div>
                            @endif

                            @if($product->meta_keywords)
                            <div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Meta Keywords:</span>
                                <div class="flex flex-wrap gap-2 mt-2">
                                    @foreach(is_array($product->meta_keywords) ? $product->meta_keywords : explode(',', $product->meta_keywords) as $keyword)
                                    <span class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-sm rounded-full">{{ trim($keyword) }}</span>
                                    @endforeach
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Product Details Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Product Details</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Basic information</p>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Category:</span>
                                    <p class="text-gray-900 dark:text-white font-medium">{{ $product->category->name }}</p>
                                </div>

                                <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Admin:</span>
                                    <p class="text-gray-900 dark:text-white font-medium">{{ $product->user->name }}</p>
                                </div>

                                <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Price:</span>
                                    <p class="text-gray-900 dark:text-white font-medium">
                                        @if($product->is_free)
                                            <span class="inline-flex items-center px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-sm font-medium rounded-full">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                </svg>
                                                Free
                                            </span>
                                        @else
                                            <span class="text-2xl font-bold text-blue-600 dark:text-blue-400">${{ number_format($product->price, 2) }}</span>
                                        @endif
                                    </p>
                                </div>

                                @if($product->demo_url)
                                <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Demo URL:</span>
                                    <p><a href="{{ $product->demo_url }}" target="_blank"
                                         class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                        </svg>
                                        View Demo
                                    </a></p>
                                </div>
                                @endif

                                <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Status:</span>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                        @if($product->status === 'published') bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300
                                        @elseif($product->status === 'draft') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300
                                        @else bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300
                                        @endif">
                                        {{ ucfirst($product->status) }}
                                    </span>
                                </div>

                                <div class="grid grid-cols-2 gap-3">
                                    <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Featured:</span>
                                        <p class="text-gray-900 dark:text-white font-medium">
                                            @if($product->is_featured)
                                                <span class="text-green-600 dark:text-green-400">✓ Yes</span>
                                            @else
                                                <span class="text-gray-500">✗ No</span>
                                            @endif
                                        </p>
                                    </div>

                                    <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Trending:</span>
                                        <p class="text-gray-900 dark:text-white font-medium">
                                            @if($product->is_trending)
                                                <span class="text-green-600 dark:text-green-400">✓ Yes</span>
                                            @else
                                                <span class="text-gray-500">✗ No</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Statistics</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Performance metrics</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($product->view_count) }}</div>
                                    <div class="text-sm text-blue-600 dark:text-blue-400 font-medium">Views</div>
                                </div>

                                <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ number_format($product->download_count) }}</div>
                                    <div class="text-sm text-green-600 dark:text-green-400 font-medium">Downloads</div>
                                </div>
                            </div>

                            <div class="mt-4 space-y-3">
                                <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Created:</span>
                                    <p class="text-gray-900 dark:text-white font-medium">{{ $product->created_at->format('M d, Y') }}</p>
                                </div>

                                <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Updated:</span>
                                    <p class="text-gray-900 dark:text-white font-medium">{{ $product->updated_at->format('M d, Y') }}</p>
                                </div>

                                @if($product->published_at)
                                <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Published:</span>
                                    <p class="text-gray-900 dark:text-white font-medium">{{ $product->published_at->format('M d, Y') }}</p>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Actions Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Actions</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Product management</p>
                                </div>
                            </div>

                            <div class="space-y-3">
                                @if($product->status === 'draft')
                                    <form action="{{ route('admin.products.approve', $product) }}" method="POST" class="w-full">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium rounded-lg transition-all duration-200">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Approve Product
                                        </button>
                                    </form>
                                @endif

                                @if($product->status === 'published')
                                    <form action="{{ route('admin.products.reject', $product) }}" method="POST" class="w-full">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white font-medium rounded-lg transition-all duration-200">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                            Reject Product
                                        </button>
                                    </form>
                                @endif

                                <form action="{{ route('admin.products.toggle-featured', $product) }}" method="POST" class="w-full">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-medium rounded-lg transition-all duration-200">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                        </svg>
                                        {{ $product->is_featured ? 'Remove from Featured' : 'Make Featured' }}
                                    </button>
                                </form>

                                <form action="{{ route('admin.products.destroy', $product) }}" method="POST" class="w-full" onsubmit="return confirm('Are you sure you want to delete this product?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-medium rounded-lg transition-all duration-200">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        Delete Product
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center p-4">
        <div class="relative max-w-4xl max-h-full">
            <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white text-2xl font-bold z-10 bg-black bg-opacity-50 rounded-full w-10 h-10 flex items-center justify-center hover:bg-opacity-75 transition-all duration-200">
                ×
            </button>
            <img id="modalImage" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg">
        </div>
    </div>

    <script>
    function openImageModal(src, alt) {
        const modal = document.getElementById('imageModal');
        const modalImage = document.getElementById('modalImage');

        modalImage.src = src;
        modalImage.alt = alt;
        modal.classList.remove('hidden');

        // Close modal on click outside image
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeImageModal();
            }
        });
    }

    function closeImageModal() {
        const modal = document.getElementById('imageModal');
        modal.classList.add('hidden');
    }

    // Close modal on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeImageModal();
        }
    });
    </script>
@endsection
