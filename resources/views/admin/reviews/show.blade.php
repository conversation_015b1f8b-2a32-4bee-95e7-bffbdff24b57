@extends('layouts.admin')

@section('title', 'Review Details')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Review Details</h1>
            <nav class="text-sm text-gray-600 dark:text-gray-400">
                <a href="{{ route('admin.reviews.index') }}" class="hover:text-gray-900 dark:hover:text-white">Reviews</a>
                <span class="mx-2">/</span>
                <span>{{ $review->title }}</span>
            </nav>
        </div>
        
        <div class="flex items-center space-x-3">
            @if($review->status === 'pending')
            <form method="POST" action="{{ route('admin.reviews.approve', $review) }}" class="inline">
                @csrf
                @method('PATCH')
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-check mr-2"></i>Approve
                </button>
            </form>
            
            <form method="POST" action="{{ route('admin.reviews.reject', $review) }}" class="inline">
                @csrf
                @method('PATCH')
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-times mr-2"></i>Reject
                </button>
            </form>
            @endif
            
            <form method="POST" action="{{ route('admin.reviews.destroy', $review) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this review?')">
                @csrf
                @method('DELETE')
                <button type="submit" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-trash mr-2"></i>Delete
                </button>
            </form>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Review Content -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <!-- Review Header -->
                <div class="flex items-start justify-between mb-6">
                    <div>
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ $review->title }}</h2>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center">
                                {!! $review->stars_html !!}
                                <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">({{ $review->rating }}/5)</span>
                            </div>
                            <span class="px-3 py-1 text-xs font-semibold rounded-full
                                @if($review->status === 'approved') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                @elseif($review->status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                @else bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                @endif">
                                {{ ucfirst($review->status) }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Review Content -->
                <div class="prose dark:prose-invert max-w-none">
                    <p class="text-gray-700 dark:text-gray-300 leading-relaxed">{{ $review->content }}</p>
                </div>

                <!-- Review Meta -->
                <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
                        <div>
                            <span class="font-medium">Submitted:</span>
                            {{ $review->created_at->format('M j, Y \a\t g:i A') }}
                        </div>
                        <div>
                            <span class="font-medium">IP Address:</span>
                            {{ $review->ip_address ?? 'N/A' }}
                        </div>
                        @if($review->updated_at != $review->created_at)
                        <div>
                            <span class="font-medium">Last Updated:</span>
                            {{ $review->updated_at->format('M j, Y \a\t g:i A') }}
                        </div>
                        @endif
                        <div>
                            <span class="font-medium">User Agent:</span>
                            <span class="truncate block">{{ Str::limit($review->user_agent ?? 'N/A', 50) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Customer Info -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Customer Information</h3>
                
                <div class="space-y-3">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="font-medium text-gray-900 dark:text-white">{{ $review->user->name }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $review->user->email }}</p>
                        </div>
                    </div>
                    
                    <div class="pt-3 border-t border-gray-200 dark:border-gray-700">
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            <p><span class="font-medium">Member since:</span> {{ $review->user->created_at->format('M Y') }}</p>
                            <p><span class="font-medium">Role:</span> {{ ucfirst($review->user->role) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Info -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Product Information</h3>
                
                <div class="space-y-3">
                    @if($review->commentable->primaryImage)
                    <div class="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                        <img src="{{ $review->commentable->primaryImage->image_url }}"
                             alt="{{ $review->commentable->title }}"
                             class="w-full h-full object-cover">
                    </div>
                    @endif
                    
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">{{ $review->commentable->title }}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $review->commentable->category->name }}</p>
                    </div>
                    
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">Price:</span>
                        <span class="font-medium text-gray-900 dark:text-white">{{ $review->commentable->formatted_price }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">Downloads:</span>
                        <span class="font-medium text-gray-900 dark:text-white">{{ number_format($review->commentable->download_count) }}</span>
                    </div>
                    
                    <div class="pt-3 border-t border-gray-200 dark:border-gray-700">
                        <a href="{{ route('admin.products.show', $review->commentable) }}" 
                           class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-center block">
                            View Product
                        </a>
                    </div>
                </div>
            </div>

            <!-- Review Stats -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Product Reviews</h3>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">Average Rating:</span>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-900 dark:text-white mr-2">{{ number_format($review->commentable->average_rating, 1) }}</span>
                            <div class="flex">
                                {!! $review->commentable->rating_stars !!}
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">Total Reviews:</span>
                        <span class="font-medium text-gray-900 dark:text-white">{{ $review->commentable->review_count }}</span>
                    </div>
                    
                    @php
                        $ratingBreakdown = $review->commentable->reviews()
                            ->where('status', 'approved')
                            ->selectRaw('rating, COUNT(*) as count')
                            ->groupBy('rating')
                            ->orderBy('rating', 'desc')
                            ->pluck('count', 'rating');
                    @endphp
                    
                    <div class="pt-3 border-t border-gray-200 dark:border-gray-700">
                        <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rating Breakdown:</p>
                        @for($i = 5; $i >= 1; $i--)
                        <div class="flex items-center text-xs mb-1">
                            <span class="w-8">{{ $i }}★</span>
                            <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mx-2">
                                @php
                                    $count = $ratingBreakdown[$i] ?? 0;
                                    $percentage = $review->commentable->review_count > 0 ? ($count / $review->commentable->review_count) * 100 : 0;
                                @endphp
                                <div class="bg-yellow-400 h-2 rounded-full" style="width: {{ $percentage }}%"></div>
                            </div>
                            <span class="w-8 text-right">{{ $count }}</span>
                        </div>
                        @endfor
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
