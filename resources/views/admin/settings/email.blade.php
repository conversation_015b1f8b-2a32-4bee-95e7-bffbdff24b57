@extends('layouts.admin')

@section('title', '<PERSON><PERSON>')

@section('breadcrumbs')
    <a href="{{ route('admin.dashboard') }}" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">Admin</a>
    <span class="text-gray-400 dark:text-gray-500 mx-2">/</span>
    <span class="text-gray-900 dark:text-white font-medium">Email Settings</span>
@endsection

@section('content')
    <div class="p-6">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Email Settings</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">Configure email delivery and notification settings</p>
                </div>
            </div>
        </div>

        <!-- Settings Navigation Tabs -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="flex space-x-8 px-6">
                    <a href="{{ route('admin.settings.general') }}"
                       class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:border-gray-300 dark:hover:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            General
                        </div>
                    </a>
                    <a href="{{ route('admin.settings.email') }}"
                       class="border-blue-500 text-blue-600 dark:text-blue-400 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Email
                        </div>
                    </a>
                    <a href="{{ route('admin.settings.payment') }}"
                       class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:border-gray-300 dark:hover:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            Payment
                        </div>
                    </a>
                    <a href="{{ route('admin.settings.seo') }}"
                       class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:border-gray-300 dark:hover:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            SEO
                        </div>
                    </a>
                </nav>
            </div>
        </div>

        <!-- Settings Form Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="p-6">
                @if (session('success'))
                    <div class="mb-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {{ session('success') }}
                        </div>
                    </div>
                @endif

                @if ($errors->any())
                    <div class="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="font-medium mb-1">Please fix the following errors:</p>
                                <ul class="list-disc list-inside space-y-1">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('admin.settings.email.update') }}" class="space-y-8">
                        @csrf

                        <!-- SMTP Configuration Section -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">SMTP Configuration</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Configure your email delivery settings</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="mail_driver" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Mail Driver <span class="text-red-500">*</span>
                                    </label>
                                    <select name="mail_driver" id="mail_driver"
                                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            required>
                                        @php
                                            $currentDriver = old('mail_driver', $settings['mail_driver'] ?? config('mail.default'));
                                            $drivers = [
                                                'smtp' => 'SMTP',
                                                'sendmail' => 'Sendmail',
                                                'mailgun' => 'Mailgun',
                                                'ses' => 'Amazon SES',
                                                'postmark' => 'Postmark'
                                            ];
                                        @endphp
                                        @foreach($drivers as $value => $label)
                                            <option value="{{ $value }}" {{ $value === $currentDriver ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="smtp-fields">
                                    <label for="mail_host" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Host</label>
                                    <input type="text" name="mail_host" id="mail_host"
                                           value="{{ old('mail_host', $settings['mail_host'] ?? config('mail.mailers.smtp.host')) }}"
                                           placeholder="smtp.gmail.com"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="smtp-fields">
                                    <label for="mail_port" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Port</label>
                                    <input type="number" name="mail_port" id="mail_port"
                                           value="{{ old('mail_port', $settings['mail_port'] ?? config('mail.mailers.smtp.port')) }}"
                                           placeholder="587"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="smtp-fields">
                                    <label for="mail_username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Username</label>
                                    <input type="text" name="mail_username" id="mail_username"
                                           value="{{ old('mail_username', $settings['mail_username'] ?? config('mail.mailers.smtp.username')) }}"
                                           placeholder="<EMAIL>"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="smtp-fields">
                                    <label for="mail_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Password</label>
                                    <input type="password" name="mail_password" id="mail_password"
                                           placeholder="Leave blank to keep current password"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="smtp-fields">
                                    <label for="mail_encryption" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Encryption</label>
                                    <select name="mail_encryption" id="mail_encryption"
                                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        @php
                                            $currentEncryption = old('mail_encryption', $settings['mail_encryption'] ?? config('mail.mailers.smtp.encryption'));
                                        @endphp
                                        <option value="">None</option>
                                        <option value="tls" {{ $currentEncryption === 'tls' ? 'selected' : '' }}>TLS</option>
                                        <option value="ssl" {{ $currentEncryption === 'ssl' ? 'selected' : '' }}>SSL</option>
                                    </select>
                                </div>
                            </div>

                            <!-- SMTP Connection Test -->
                            <div class="mt-6 bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Test SMTP Connection</h4>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">Verify your SMTP settings are working correctly</p>
                                    </div>
                                    <button type="button" id="test-smtp-connection"
                                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                        <i class="fas fa-plug mr-2"></i>
                                        Test SMTP
                                    </button>
                                </div>
                                <div id="smtp-test-result" class="mt-3 hidden"></div>
                            </div>
                        </div>

                        <!-- Email From Settings Section -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Email From Settings</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Configure sender information for outgoing emails</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="mail_from_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        From Email Address <span class="text-red-500">*</span>
                                    </label>
                                    <input type="email" name="mail_from_address" id="mail_from_address"
                                           value="{{ old('mail_from_address', $settings['mail_from_address'] ?? config('mail.from.address')) }}"
                                           placeholder="<EMAIL>"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           required>
                                </div>

                                <div>
                                    <label for="mail_from_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        From Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" name="mail_from_name" id="mail_from_name"
                                           value="{{ old('mail_from_name', $settings['mail_from_name'] ?? config('mail.from.name')) }}"
                                           placeholder="Templates Cave"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           required>
                                </div>
                            </div>
                        </div>

                        <!-- IMAP Configuration Section -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">IMAP Configuration</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Configure IMAP settings for reading incoming emails</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="imap_enabled" class="flex items-center">
                                        <input type="checkbox" name="imap_enabled" id="imap_enabled" value="1"
                                               {{ old('imap_enabled', $settings['imap_enabled'] ?? false) ? 'checked' : '' }}
                                               class="rounded border-gray-300 dark:border-gray-600 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700">
                                        <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Enable IMAP</span>
                                    </label>
                                </div>

                                <div></div>

                                <div class="imap-fields">
                                    <label for="imap_host" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">IMAP Host</label>
                                    <input type="text" name="imap_host" id="imap_host"
                                           value="{{ old('imap_host', $settings['imap_host'] ?? '') }}"
                                           placeholder="imap.gmail.com"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="imap-fields">
                                    <label for="imap_port" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">IMAP Port</label>
                                    <input type="number" name="imap_port" id="imap_port"
                                           value="{{ old('imap_port', $settings['imap_port'] ?? '993') }}"
                                           placeholder="993"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="imap-fields">
                                    <label for="imap_username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">IMAP Username</label>
                                    <input type="text" name="imap_username" id="imap_username"
                                           value="{{ old('imap_username', $settings['imap_username'] ?? '') }}"
                                           placeholder="<EMAIL>"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="imap-fields">
                                    <label for="imap_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">IMAP Password</label>
                                    <input type="password" name="imap_password" id="imap_password"
                                           placeholder="Leave blank to keep current password"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="imap-fields">
                                    <label for="imap_encryption" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">IMAP Encryption</label>
                                    <select name="imap_encryption" id="imap_encryption"
                                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        @php
                                            $currentImapEncryption = old('imap_encryption', $settings['imap_encryption'] ?? 'ssl');
                                        @endphp
                                        <option value="">None</option>
                                        <option value="tls" {{ $currentImapEncryption === 'tls' ? 'selected' : '' }}>TLS</option>
                                        <option value="ssl" {{ $currentImapEncryption === 'ssl' ? 'selected' : '' }}>SSL</option>
                                    </select>
                                </div>

                                <div class="imap-fields">
                                    <label for="imap_validate_cert" class="flex items-center">
                                        <input type="checkbox" name="imap_validate_cert" id="imap_validate_cert" value="1"
                                               {{ old('imap_validate_cert', $settings['imap_validate_cert'] ?? true) ? 'checked' : '' }}
                                               class="rounded border-gray-300 dark:border-gray-600 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700">
                                        <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Validate SSL Certificate</span>
                                    </label>
                                </div>
                            </div>

                            <!-- IMAP Test Section -->
                            <div class="mt-6 bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Test IMAP Connection</h4>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">Verify your IMAP settings are working correctly</p>
                                    </div>
                                    <button type="button" id="test-imap-connection"
                                            class="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                        <i class="fas fa-plug mr-2"></i>
                                        Test IMAP
                                    </button>
                                </div>
                                <div id="imap-test-result" class="mt-3 hidden"></div>
                            </div>
                        </div>

                        <!-- Test Email Section -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Test Email Configuration</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Send a test email to verify your settings</p>
                                </div>
                            </div>

                            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                <div class="flex flex-col sm:flex-row sm:items-end space-y-4 sm:space-y-0 sm:space-x-4">
                                    <div class="flex-1">
                                        <label for="test_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Test Email Address</label>
                                        <input type="email" id="test_email"
                                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                               placeholder="Enter email address to test">
                                    </div>
                                    <div class="flex-shrink-0">
                                        <button type="button" id="send-test-email"
                                               class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium rounded-lg shadow-sm transition-all duration-200">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                            </svg>
                                            Send Test Email
                                        </button>
                                    </div>
                                </div>
                                <div id="test-email-result" class="mt-4 hidden"></div>
                            </div>
                        </div>

                        <!-- Save Button -->
                        <div class="flex justify-end pt-6">
                            <button type="submit"
                                    class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg shadow-sm transition-all duration-200">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Save Email Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mailDriverSelect = document.getElementById('mail_driver');
            const smtpFields = document.querySelectorAll('.smtp-fields');

            function toggleSmtpFields() {
                const isSmtp = mailDriverSelect.value === 'smtp';
                smtpFields.forEach(field => {
                    field.style.display = isSmtp ? 'block' : 'none';
                    const input = field.querySelector('input, select');
                    if (input) {
                        input.required = isSmtp;
                    }
                });
            }

            mailDriverSelect.addEventListener('change', toggleSmtpFields);
            toggleSmtpFields(); // Initial call

            // Test email functionality
            document.getElementById('send-test-email').addEventListener('click', function() {
                const testEmail = document.getElementById('test_email').value;
                const resultDiv = document.getElementById('test-email-result');

                if (!testEmail) {
                    alert('Please enter a test email address');
                    return;
                }

                this.disabled = true;
                this.textContent = 'Sending...';

                fetch('{{ route("admin.settings.email.test") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ test_email: testEmail })
                })
                .then(response => response.json())
                .then(data => {
                    resultDiv.className = data.success
                        ? 'mt-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded-lg'
                        : 'mt-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg';
                    resultDiv.textContent = data.message;
                    resultDiv.classList.remove('hidden');
                })
                .catch(error => {
                    resultDiv.className = 'mt-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg';
                    resultDiv.textContent = 'Error sending test email: ' + error.message;
                    resultDiv.classList.remove('hidden');
                })
                .finally(() => {
                    this.disabled = false;
                    this.textContent = 'Send Test Email';
                });
            });

            // SMTP Test functionality
            const testSmtpButton = document.getElementById('test-smtp-connection');
            const smtpResultDiv = document.getElementById('smtp-test-result');

            if (testSmtpButton) {
                testSmtpButton.addEventListener('click', function() {
                    this.disabled = true;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';

                    fetch('{{ route("admin.settings.email.test-smtp") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        smtpResultDiv.className = data.success
                            ? 'mt-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded-lg'
                            : 'mt-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg';
                        smtpResultDiv.innerHTML = '<div class="flex items-center"><i class="fas fa-' + (data.success ? 'check-circle' : 'exclamation-circle') + ' mr-2"></i>' + data.message + '</div>';
                        smtpResultDiv.classList.remove('hidden');
                    })
                    .catch(error => {
                        smtpResultDiv.className = 'mt-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg';
                        smtpResultDiv.innerHTML = '<div class="flex items-center"><i class="fas fa-exclamation-circle mr-2"></i>Error testing SMTP connection: ' + error.message + '</div>';
                        smtpResultDiv.classList.remove('hidden');
                    })
                    .finally(() => {
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-plug mr-2"></i>Test SMTP';
                    });
                });
            }

            // IMAP Test functionality
            const testImapButton = document.getElementById('test-imap-connection');
            const imapResultDiv = document.getElementById('imap-test-result');

            testImapButton.addEventListener('click', function() {
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';

                fetch('{{ route("admin.settings.email.test-imap") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    imapResultDiv.className = data.success
                        ? 'mt-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded-lg'
                        : 'mt-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg';
                    imapResultDiv.innerHTML = '<div class="flex items-center"><i class="fas fa-' + (data.success ? 'check-circle' : 'exclamation-circle') + ' mr-2"></i>' + data.message + '</div>';
                    imapResultDiv.classList.remove('hidden');
                })
                .catch(error => {
                    imapResultDiv.className = 'mt-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg';
                    imapResultDiv.innerHTML = '<div class="flex items-center"><i class="fas fa-exclamation-circle mr-2"></i>Error testing IMAP connection: ' + error.message + '</div>';
                    imapResultDiv.classList.remove('hidden');
                })
                .finally(() => {
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-plug mr-2"></i>Test IMAP';
                });
            });
        });
    </script>
@endsection
