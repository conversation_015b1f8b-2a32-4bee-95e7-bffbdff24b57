@extends('layouts.admin')

@section('title', 'General Settings')

@section('breadcrumbs')
    <a href="{{ route('admin.dashboard') }}" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">Admin</a>
    <span class="text-gray-400 dark:text-gray-500 mx-2">/</span>
    <span class="text-gray-900 dark:text-white font-medium">General Settings</span>
@endsection

@section('content')
    <div class="p-6">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">General Settings</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">Configure your marketplace's basic settings and preferences</p>
                </div>
            </div>
        </div>

        <!-- Settings Navigation Tabs -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="flex space-x-8 px-6">
                    <a href="{{ route('admin.settings.general') }}"
                       class="border-blue-500 text-blue-600 dark:text-blue-400 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            General
                        </div>
                    </a>
                    <a href="{{ route('admin.settings.email') }}"
                       class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:border-gray-300 dark:hover:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Email
                        </div>
                    </a>
                    <a href="{{ route('admin.settings.payment') }}"
                       class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:border-gray-300 dark:hover:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            Payment
                        </div>
                    </a>
                    <a href="{{ route('admin.settings.seo') }}"
                       class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:border-gray-300 dark:hover:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            SEO
                        </div>
                    </a>
                </nav>
            </div>
        </div>

        <!-- Settings Form Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="p-6">
                @if (session('success'))
                    <div class="mb-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {{ session('success') }}
                        </div>
                    </div>
                @endif

                @if ($errors->any())
                    <div class="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="font-medium mb-1">Please fix the following errors:</p>
                                <ul class="list-disc list-inside space-y-1">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('admin.settings.general.update') }}" class="space-y-8">
                        @csrf

                        <!-- Site Information Section -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Site Information</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Basic information about your marketplace</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="site_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Site Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" name="site_name" id="site_name"
                                           value="{{ old('site_name', $settings['site_name'] ?? config('templatescave.site.name')) }}"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           required>
                                </div>

                                <div>
                                    <label for="site_tagline" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Site Tagline</label>
                                    <input type="text" name="site_tagline" id="site_tagline"
                                           value="{{ old('site_tagline', $settings['site_tagline'] ?? config('templatescave.site.tagline')) }}"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="md:col-span-2">
                                    <label for="site_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Site Description</label>
                                    <textarea name="site_description" id="site_description" rows="3"
                                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">{{ old('site_description', $settings['site_description'] ?? config('templatescave.site.description')) }}</textarea>
                                </div>

                                <div class="md:col-span-2">
                                    <label for="site_keywords" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Site Keywords</label>
                                    <input type="text" name="site_keywords" id="site_keywords"
                                           value="{{ old('site_keywords', $settings['site_keywords'] ?? config('templatescave.site.keywords')) }}"
                                           placeholder="template, design, marketplace, digital products"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Separate keywords with commas</p>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information Section -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Contact Information</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">How users can reach you</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="contact_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Contact Email <span class="text-red-500">*</span>
                                    </label>
                                    <input type="email" name="contact_email" id="contact_email"
                                           value="{{ old('contact_email', $settings['contact_email'] ?? config('templatescave.email.admin_email')) }}"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           required>
                                </div>

                                <div>
                                    <label for="support_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Support Email <span class="text-red-500">*</span>
                                    </label>
                                    <input type="email" name="support_email" id="support_email"
                                           value="{{ old('support_email', $settings['support_email'] ?? config('templatescave.email.support_email')) }}"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           required>
                                </div>

                                <div>
                                    <label for="admin_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Admin Email <span class="text-red-500">*</span>
                                    </label>
                                    <input type="email" name="admin_email" id="admin_email"
                                           value="{{ old('admin_email', $settings['admin_email'] ?? config('templatescave.email.admin_email')) }}"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           required>
                                </div>

                                <div>
                                    <label for="contact_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Contact Phone</label>
                                    <input type="text" name="contact_phone" id="contact_phone"
                                           value="{{ old('contact_phone', $settings['contact_phone'] ?? '') }}"
                                           placeholder="+****************"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="md:col-span-2">
                                    <label for="contact_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Contact Address</label>
                                    <textarea name="contact_address" id="contact_address" rows="3"
                                              placeholder="123 Main Street, City, State, Country"
                                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">{{ old('contact_address', $settings['contact_address'] ?? '') }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Marketplace Settings Section -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Marketplace Settings</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Configure marketplace behavior and defaults</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <div>
                                    <label for="timezone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Timezone <span class="text-red-500">*</span>
                                    </label>
                                    <select name="timezone" id="timezone"
                                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            required>
                                        @php
                                            $currentTimezone = old('timezone', $settings['timezone'] ?? config('app.timezone'));
                                            $timezones = timezone_identifiers_list();
                                        @endphp
                                        @foreach($timezones as $timezone)
                                            <option value="{{ $timezone }}" {{ $timezone === $currentTimezone ? 'selected' : '' }}>
                                                {{ $timezone }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div>
                                    <label for="default_currency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Default Currency <span class="text-red-500">*</span>
                                    </label>
                                    <select name="default_currency" id="default_currency"
                                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            required>
                                        @php
                                            $currentCurrency = old('default_currency', $settings['default_currency'] ?? config('templatescave.payments.currency'));
                                            $currencies = ['usd' => 'USD', 'eur' => 'EUR', 'gbp' => 'GBP', 'cad' => 'CAD', 'aud' => 'AUD'];
                                        @endphp
                                        @foreach($currencies as $code => $name)
                                            <option value="{{ $code }}" {{ $code === $currentCurrency ? 'selected' : '' }}>
                                                {{ $name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div>
                                    <label for="currency_symbol" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Currency Symbol <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" name="currency_symbol" id="currency_symbol"
                                           value="{{ old('currency_symbol', $settings['currency_symbol'] ?? config('templatescave.payments.currency_symbol')) }}"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           required>
                                </div>

                                <div>
                                    <label for="commission_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Commission Rate (%) <span class="text-red-500">*</span>
                                    </label>
                                    <input type="number" name="commission_rate" id="commission_rate" step="0.01" min="0" max="100"
                                           value="{{ old('commission_rate', $settings['commission_rate'] ?? '0') }}"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           required>
                                </div>
                            </div>
                        </div>

                        <!-- Social Media Links Section -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-pink-500 to-rose-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v3M7 4H5a1 1 0 00-1 1v16a1 1 0 001 1h14a1 1 0 001-1V5a1 1 0 00-1-1h-2M7 4h10M9 9h6m-6 4h6m-6 4h6"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Social Media Links</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Connect your social media profiles</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="facebook_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Facebook URL</label>
                                    <input type="url" name="facebook_url" id="facebook_url"
                                           value="{{ old('facebook_url', $settings['facebook_url'] ?? config('templatescave.social.facebook')) }}"
                                           placeholder="https://facebook.com/yourpage"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div>
                                    <label for="twitter_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Twitter URL</label>
                                    <input type="url" name="twitter_url" id="twitter_url"
                                           value="{{ old('twitter_url', $settings['twitter_url'] ?? config('templatescave.social.twitter')) }}"
                                           placeholder="https://twitter.com/youraccount"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div>
                                    <label for="linkedin_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">LinkedIn URL</label>
                                    <input type="url" name="linkedin_url" id="linkedin_url"
                                           value="{{ old('linkedin_url', $settings['linkedin_url'] ?? config('templatescave.social.linkedin')) }}"
                                           placeholder="https://linkedin.com/company/yourcompany"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div>
                                    <label for="youtube_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">YouTube URL</label>
                                    <input type="url" name="youtube_url" id="youtube_url"
                                           value="{{ old('youtube_url', $settings['youtube_url'] ?? config('templatescave.social.youtube')) }}"
                                           placeholder="https://youtube.com/c/yourchannel"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div>
                                    <label for="instagram_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Instagram URL</label>
                                    <input type="url" name="instagram_url" id="instagram_url"
                                           value="{{ old('instagram_url', $settings['instagram_url'] ?? '') }}"
                                           placeholder="https://instagram.com/youraccount"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                            </div>
                        </div>

                        <!-- Save Button -->
                        <div class="flex justify-end pt-6">
                            <button type="submit"
                                    class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg shadow-sm transition-all duration-200">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Save General Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
