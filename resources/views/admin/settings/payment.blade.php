@extends('layouts.admin')

@section('title', 'Payment Settings')

@section('breadcrumbs')
    <a href="{{ route('admin.dashboard') }}" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">Admin</a>
    <span class="text-gray-400 dark:text-gray-500 mx-2">/</span>
    <span class="text-gray-900 dark:text-white font-medium">Payment Settings</span>
@endsection

@section('content')
    <div class="p-6">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Payment Settings</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">Configure payment gateways and transaction settings</p>
                </div>
            </div>
        </div>

        <!-- Settings Navigation Tabs -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="flex space-x-8 px-6">
                    <a href="{{ route('admin.settings.general') }}"
                       class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:border-gray-300 dark:hover:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            General
                        </div>
                    </a>
                    <a href="{{ route('admin.settings.email') }}"
                       class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:border-gray-300 dark:hover:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Email
                        </div>
                    </a>
                    <a href="{{ route('admin.settings.payment') }}"
                       class="border-blue-500 text-blue-600 dark:text-blue-400 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            Payment
                        </div>
                    </a>
                    <a href="{{ route('admin.settings.seo') }}"
                       class="border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:border-gray-300 dark:hover:border-gray-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            SEO
                        </div>
                    </a>
                </nav>
            </div>
        </div>

        <!-- Settings Form Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="p-6">
                @if (session('success'))
                    <div class="mb-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {{ session('success') }}
                        </div>
                    </div>
                @endif

                @if ($errors->any())
                    <div class="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="font-medium mb-1">Please fix the following errors:</p>
                                <ul class="list-disc list-inside space-y-1">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('admin.settings.payment.update') }}" class="space-y-8">
                        @csrf

                        <!-- Stripe Configuration Section -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Stripe Configuration</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Configure Stripe payment gateway</p>
                                    </div>
                                </div>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" name="stripe_enabled" value="1"
                                           {{ old('stripe_enabled', $settings['stripe_enabled'] ?? false) ? 'checked' : '' }}
                                           class="rounded border-gray-300 dark:border-gray-600 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-white dark:bg-gray-700">
                                    <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">Enable Stripe</span>
                                </label>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="stripe_key" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Stripe Publishable Key</label>
                                    <input type="text" name="stripe_key" id="stripe_key"
                                           value="{{ old('stripe_key', $settings['stripe_key'] ?? config('templatescave.payments.stripe.key')) }}"
                                           placeholder="pk_test_..."
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div>
                                    <label for="stripe_secret" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Stripe Secret Key</label>
                                    <input type="password" name="stripe_secret" id="stripe_secret"
                                           placeholder="Leave blank to keep current secret"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="md:col-span-2">
                                    <label for="stripe_webhook_secret" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Stripe Webhook Secret</label>
                                    <input type="password" name="stripe_webhook_secret" id="stripe_webhook_secret"
                                           placeholder="Leave blank to keep current webhook secret"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                            </div>
                        </div>

                        <!-- PayPal Configuration Section -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">PayPal Configuration</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Configure PayPal payment gateway</p>
                                    </div>
                                </div>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" name="paypal_enabled" value="1"
                                           {{ old('paypal_enabled', $settings['paypal_enabled'] ?? false) ? 'checked' : '' }}
                                           class="rounded border-gray-300 dark:border-gray-600 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-white dark:bg-gray-700">
                                    <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">Enable PayPal</span>
                                </label>
                            </div>

                            <!-- Connection Status -->
                            @php
                                $isConnected = $settings['paypal_connected'] ?? false;
                                $connectedAt = $settings['paypal_connected_at'] ?? null;
                                $userEmail = $settings['paypal_user_email'] ?? null;
                                $userName = $settings['paypal_user_name'] ?? null;
                                $lastTested = $settings['paypal_last_tested'] ?? null;
                                $lastError = $settings['paypal_last_error'] ?? null;
                                $paypalMode = $settings['paypal_mode'] ?? 'sandbox';
                            @endphp

                            @if($isConnected)
                                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <h3 class="text-sm font-medium text-green-800 dark:text-green-200">PayPal Connected</h3>
                                            <div class="mt-1 text-sm text-green-700 dark:text-green-300">
                                                @if($userName && $userEmail)
                                                    Connected as <strong>{{ $userName }}</strong> ({{ $userEmail }})
                                                    <br>
                                                @endif
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100 mr-2">
                                                    {{ ucfirst($paypalMode) }} Mode
                                                </span>
                                                @if($connectedAt)
                                                    Connected on {{ \Carbon\Carbon::parse($connectedAt)->format('M j, Y \a\t g:i A') }}
                                                @endif
                                                @if($lastTested)
                                                    <br>Last tested: {{ \Carbon\Carbon::parse($lastTested)->format('M j, Y \a\t g:i A') }}
                                                @endif
                                            </div>
                                        </div>
                                        <div class="ml-auto">
                                            <form action="{{ route('admin.settings.paypal.disconnect') }}" method="POST" class="inline">
                                                @csrf
                                                <button type="submit" class="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                                                        onclick="return confirm('Are you sure you want to disconnect PayPal?')">
                                                    Disconnect
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            @else
                                @if($lastError)
                                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                                        <div class="flex items-start">
                                            <div class="flex-shrink-0">
                                                <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                            <div class="ml-3 flex-1">
                                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">PayPal Connection Error</h3>
                                                <div class="mt-1 text-sm text-red-700 dark:text-red-300">
                                                    {{ $lastError }}
                                                    <br>
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 mr-2">
                                                        {{ ucfirst($paypalMode) }} Mode
                                                    </span>
                                                    Please check your credentials and try again.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
                                        <div class="flex items-start">
                                            <div class="flex-shrink-0">
                                                <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                            <div class="ml-3 flex-1">
                                                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">PayPal Not Connected</h3>
                                                <div class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100 mr-2">
                                                        {{ ucfirst($paypalMode) }} Mode
                                                    </span>
                                                    Use Auto-Connect to automatically configure your PayPal credentials, or enter them manually below.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @endif

                            <!-- Auto-Connect Button -->
                            <div class="mb-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Quick Setup</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Connect your PayPal account automatically</p>
                                    </div>
                                    <div class="flex space-x-3">
                                        @if($isConnected)
                                            <button type="button" id="test-paypal-connection"
                                                    class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                Test Connection
                                            </button>
                                        @endif
                                        <a href="{{ route('admin.settings.paypal.connect') }}"
                                           class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg hover:from-yellow-600 hover:to-orange-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 text-sm font-medium">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                            </svg>
                                            {{ $isConnected ? 'Reconnect' : 'Auto-Connect' }} PayPal
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Manual Configuration -->
                            <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Manual Configuration</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="paypal_client_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">PayPal Client ID</label>
                                        <input type="text" name="paypal_client_id" id="paypal_client_id"
                                               value="{{ old('paypal_client_id', $settings['paypal_client_id'] ?? config('templatescave.payments.paypal.client_id')) }}"
                                               placeholder="AXxxx..."
                                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>

                                    <div>
                                        <label for="paypal_client_secret" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">PayPal Client Secret</label>
                                        <input type="password" name="paypal_client_secret" id="paypal_client_secret"
                                               placeholder="Leave blank to keep current secret"
                                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>

                                    <div>
                                        <label for="paypal_mode" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">PayPal Mode</label>
                                        <select name="paypal_mode" id="paypal_mode"
                                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                required>
                                            @php
                                                $currentMode = old('paypal_mode', $settings['paypal_mode'] ?? config('templatescave.payments.paypal.mode'));
                                            @endphp
                                            <option value="sandbox" {{ $currentMode === 'sandbox' ? 'selected' : '' }}>Sandbox</option>
                                            <option value="live" {{ $currentMode === 'live' ? 'selected' : '' }}>Live</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bank Transfer Configuration Section -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Bank Transfer Configuration</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Configure bank transfer payment method</p>
                                    </div>
                                </div>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" name="bank_transfer_enabled" value="1"
                                           {{ old('bank_transfer_enabled', $settings['bank_transfer_enabled'] ?? false) ? 'checked' : '' }}
                                           class="rounded border-gray-300 dark:border-gray-600 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-white dark:bg-gray-700">
                                    <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">Enable Bank Transfer</span>
                                </label>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="bank_account_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Account Name</label>
                                    <input type="text" name="bank_account_name" id="bank_account_name"
                                           value="{{ old('bank_account_name', $settings['bank_account_name'] ?? '') }}"
                                           placeholder="Templates Cave Ltd"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div>
                                    <label for="bank_account_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Account Number</label>
                                    <input type="text" name="bank_account_number" id="bank_account_number"
                                           value="{{ old('bank_account_number', $settings['bank_account_number'] ?? '') }}"
                                           placeholder="**********"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div>
                                    <label for="bank_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Bank Name</label>
                                    <input type="text" name="bank_name" id="bank_name"
                                           value="{{ old('bank_name', $settings['bank_name'] ?? '') }}"
                                           placeholder="Bank of America"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div>
                                    <label for="bank_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Bank Code</label>
                                    <input type="text" name="bank_code" id="bank_code"
                                           value="{{ old('bank_code', $settings['bank_code'] ?? '') }}"
                                           placeholder="*********"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div>
                                    <label for="bank_swift_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SWIFT Code</label>
                                    <input type="text" name="bank_swift_code" id="bank_swift_code"
                                           value="{{ old('bank_swift_code', $settings['bank_swift_code'] ?? '') }}"
                                           placeholder="BOFAUS3N"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div>
                                    <label for="bank_iban" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">IBAN</label>
                                    <input type="text" name="bank_iban" id="bank_iban"
                                           value="{{ old('bank_iban', $settings['bank_iban'] ?? '') }}"
                                           placeholder="US12 3456 7890 1234 5678 90"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="md:col-span-2">
                                    <label for="bank_transfer_instructions" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Transfer Instructions</label>
                                    <textarea name="bank_transfer_instructions" id="bank_transfer_instructions" rows="4"
                                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                              placeholder="Instructions for customers on how to make bank transfers">{{ old('bank_transfer_instructions', $settings['bank_transfer_instructions'] ?? '') }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- NOWPayments Configuration Section -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">NOWPayments Configuration</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Configure cryptocurrency payment gateway</p>
                                    </div>
                                </div>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" name="nowpayments_enabled" value="1"
                                           {{ old('nowpayments_enabled', $settings['nowpayments_enabled'] ?? false) ? 'checked' : '' }}
                                           class="rounded border-gray-300 dark:border-gray-600 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-white dark:bg-gray-700">
                                    <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">Enable NOWPayments</span>
                                </label>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="nowpayments_api_key" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">API Key</label>
                                    <input type="password" name="nowpayments_api_key" id="nowpayments_api_key"
                                           value="{{ old('nowpayments_api_key', $settings['nowpayments_api_key'] ?? '') }}"
                                           placeholder="Enter your NOWPayments API key"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div>
                                    <label for="nowpayments_sandbox" class="flex items-center">
                                        <input type="checkbox" name="nowpayments_sandbox" id="nowpayments_sandbox" value="1"
                                               {{ old('nowpayments_sandbox', $settings['nowpayments_sandbox'] ?? false) ? 'checked' : '' }}
                                               class="rounded border-gray-300 dark:border-gray-600 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700">
                                        <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Sandbox Mode</span>
                                    </label>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Enable for testing purposes</p>
                                </div>

                                <div>
                                    <label for="nowpayments_ipn_secret" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">IPN Secret Key</label>
                                    <input type="password" name="nowpayments_ipn_secret" id="nowpayments_ipn_secret"
                                           value="{{ old('nowpayments_ipn_secret', $settings['nowpayments_ipn_secret'] ?? '') }}"
                                           placeholder="Enter your IPN secret key"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div>
                                    <label for="nowpayments_callback_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Callback URL</label>
                                    <input type="url" name="nowpayments_callback_url" id="nowpayments_callback_url"
                                           value="{{ old('nowpayments_callback_url', $settings['nowpayments_callback_url'] ?? url('/payment/nowpayments/callback')) }}"
                                           placeholder="https://yoursite.com/payment/nowpayments/callback"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="md:col-span-2">
                                    <label for="nowpayments_accepted_currencies" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Accepted Currencies</label>
                                    <input type="text" name="nowpayments_accepted_currencies" id="nowpayments_accepted_currencies"
                                           value="{{ old('nowpayments_accepted_currencies', $settings['nowpayments_accepted_currencies'] ?? 'btc,eth,ltc,bch,xrp,ada,dot,bnb') }}"
                                           placeholder="btc,eth,ltc,bch,xrp,ada,dot,bnb"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Comma-separated list of cryptocurrency symbols</p>
                                </div>
                            </div>

                            <!-- NOWPayments Connection Test -->
                            <div class="mt-6 bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Test NOWPayments Connection</h4>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">Verify your API credentials and connection</p>
                                    </div>
                                    <button type="button" id="test-nowpayments-connection"
                                            class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                        <i class="fas fa-plug mr-2"></i>
                                        Test Connection
                                    </button>
                                </div>
                                <div id="nowpayments-test-result" class="mt-3 hidden"></div>
                            </div>
                        </div>

                        <!-- Save Button -->
                        <div class="flex justify-end pt-6">
                            <button type="submit"
                                    class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg shadow-sm transition-all duration-200">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Save Payment Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Test PayPal connection
    const testButton = document.getElementById('test-paypal-connection');
    if (testButton) {
        testButton.addEventListener('click', function() {
            const button = this;
            const originalText = button.innerHTML;

            // Show loading state
            button.disabled = true;
            button.innerHTML = `
                <svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Testing...
            `;

            // Make AJAX request
            fetch('{{ route("admin.settings.paypal.test") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                // Show result
                if (data.success) {
                    showNotification('success', data.message);
                } else {
                    showNotification('error', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('error', 'Connection test failed. Please try again.');
            })
            .finally(() => {
                // Restore button
                button.disabled = false;
                button.innerHTML = originalText;
            });
        });
    }

    // Notification function
    function showNotification(type, message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
            type === 'success'
                ? 'bg-green-500 text-white'
                : 'bg-red-500 text-white'
        }`;
        notification.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    ${type === 'success'
                        ? '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>'
                        : '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>'
                    }
                </svg>
                <span>${message}</span>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    // NOWPayments Test functionality
    const testNowPaymentsButton = document.getElementById('test-nowpayments-connection');
    const nowPaymentsResultDiv = document.getElementById('nowpayments-test-result');

    if (testNowPaymentsButton) {
        testNowPaymentsButton.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';

            fetch('{{ route("admin.settings.payment.test-nowpayments") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                nowPaymentsResultDiv.className = data.success
                    ? 'mt-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded-lg'
                    : 'mt-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg';
                nowPaymentsResultDiv.innerHTML = '<div class="flex items-center"><i class="fas fa-' + (data.success ? 'check-circle' : 'exclamation-circle') + ' mr-2"></i>' + data.message + '</div>';
                nowPaymentsResultDiv.classList.remove('hidden');
            })
            .catch(error => {
                nowPaymentsResultDiv.className = 'mt-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg';
                nowPaymentsResultDiv.innerHTML = '<div class="flex items-center"><i class="fas fa-exclamation-circle mr-2"></i>Error testing NOWPayments connection: ' + error.message + '</div>';
                nowPaymentsResultDiv.classList.remove('hidden');
            })
            .finally(() => {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-plug mr-2"></i>Test Connection';
            });
        });
    }
});
</script>
@endpush
