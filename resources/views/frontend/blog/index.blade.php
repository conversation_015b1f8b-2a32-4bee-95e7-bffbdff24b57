@extends('layouts.frontend')

@section('title', 'Blog - ' . $settings['site_name'])
@section('description', 'Stay updated with the latest news, tutorials, and insights from Templates Cave.')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 overflow-hidden">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="text-center">
                <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
                    Our Blog
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto mb-8">
                    Discover the latest trends, tutorials, and insights in web design, development, and digital creativity.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg px-6 py-3 text-white">
                        <span class="text-2xl font-bold">{{ $posts->total() }}</span>
                        <span class="text-blue-100 ml-2">Articles</span>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg px-6 py-3 text-white">
                        <span class="text-2xl font-bold">{{ $categories->count() }}</span>
                        <span class="text-blue-100 ml-2">Categories</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- Decorative elements -->
        <div class="absolute top-0 left-0 w-full h-full">
            <div class="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full"></div>
            <div class="absolute top-32 right-20 w-16 h-16 bg-white/10 rounded-full"></div>
            <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-white/10 rounded-full"></div>
            <div class="absolute bottom-32 right-1/3 w-8 h-8 bg-white/10 rounded-full"></div>
        </div>
    </section>

    <!-- Search and Filters Section -->
    <section class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="max-w-4xl mx-auto">

                <form method="GET" class="flex flex-col md:flex-row gap-4">
                    <!-- Search -->
                    <div class="flex-1">
                        <input type="text" name="search" value="{{ request('search') }}" 
                               placeholder="Search blog posts..." 
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <!-- Category Filter -->
                    <div>
                        <select name="category" class="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="">All Categories</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->slug }}" {{ request('category') == $category->slug ? 'selected' : '' }}>
                                    {{ $category->name }} ({{ $category->published_posts_count }})
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <!-- Sort -->
                    <div>
                        <select name="sort" class="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="latest" {{ request('sort') == 'latest' ? 'selected' : '' }}>Latest</option>
                            <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>Most Popular</option>
                            <option value="comments" {{ request('sort') == 'comments' ? 'selected' : '' }}>Most Commented</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg transition-all duration-200 transform hover:scale-105">
                        <i class="fas fa-search mr-2"></i>
                        Search
                    </button>
                </form>
            </div>
        </div>
    </section>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-3">
                @if($posts->count() > 0)
                    <!-- Featured Posts -->
                    @if($featuredPosts->count() > 0 && !request()->filled('search'))
                        <div class="mb-12">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Featured Posts</h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                @foreach($featuredPosts as $post)
                                    <article class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-shadow duration-200">
                                        @if($post->featured_image)
                                            <img src="{{ $post->featured_image_url }}" alt="{{ $post->title }}" class="w-full h-48 object-cover">
                                        @endif
                                        <div class="p-6">
                                            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3">
                                                <span>{{ $post->published_at->format('M j, Y') }}</span>
                                                <span class="mx-2">•</span>
                                                <span>{{ $post->reading_time }}</span>
                                                @if($post->category)
                                                    <span class="mx-2">•</span>
                                                    <span class="text-blue-600 dark:text-blue-400">{{ $post->category->name }}</span>
                                                @endif
                                            </div>
                                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                                <a href="{{ route('blog.show', $post->slug) }}" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                                    {{ $post->title }}
                                                </a>
                                            </h3>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                    {{ $post->view_count }}
                                                    <svg class="w-4 h-4 ml-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                                    </svg>
                                                    {{ $post->comment_count }}
                                                </div>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                                    Featured
                                                </span>
                                            </div>
                                        </div>
                                    </article>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- All Posts -->
                    <div class="mb-8">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                                @if(request()->filled('search'))
                                    Search Results for "{{ request('search') }}"
                                @elseif(request()->filled('category'))
                                    {{ $categories->where('slug', request('category'))->first()->name ?? 'Category' }} Posts
                                @else
                                    Latest Posts
                                @endif
                            </h2>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $posts->total() }} {{ Str::plural('post', $posts->total()) }}
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            @foreach($posts as $post)
                                <article class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-shadow duration-200">
                                    @if($post->featured_image)
                                        <img src="{{ $post->featured_image_url }}" alt="{{ $post->title }}" class="w-full h-48 object-cover">
                                    @endif
                                    <div class="p-6">
                                        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3">
                                            <span>{{ $post->published_at->format('M j, Y') }}</span>
                                            <span class="mx-2">•</span>
                                            <span>{{ $post->reading_time }}</span>
                                            @if($post->category)
                                                <span class="mx-2">•</span>
                                                <a href="{{ route('blog.category', $post->category->slug) }}" class="text-blue-600 dark:text-blue-400 hover:underline">
                                                    {{ $post->category->name }}
                                                </a>
                                            @endif
                                        </div>
                                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                            <a href="{{ route('blog.show', $post->slug) }}" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                                {{ $post->title }}
                                            </a>
                                        </h3>
                                        
                                        @if($post->tags && is_array($post->tags))
                                            <div class="flex flex-wrap gap-2 mb-4">
                                                @foreach(array_slice($post->tags, 0, 3) as $tag)
                                                    <a href="{{ route('blog.tag', $tag) }}" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                                                        {{ $tag }}
                                                    </a>
                                                @endforeach
                                            </div>
                                        @endif
                                        
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                                {{ $post->view_count }}
                                                <svg class="w-4 h-4 ml-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                                </svg>
                                                {{ $post->comment_count }}
                                            </div>
                                            @if($post->is_featured)
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                                    Featured
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </article>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        @if($posts->hasPages())
                            <div class="mt-8">
                                {{ $posts->links() }}
                            </div>
                        @endif
                    </div>
                @else
                    <!-- No Posts Found -->
                    <div class="text-center py-12">
                        <svg class="w-24 h-24 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                        </svg>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No posts found</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-6">
                            @if(request()->filled('search'))
                                No posts match your search criteria. Try different keywords.
                            @else
                                No blog posts have been published yet. Check back soon!
                            @endif
                        </p>
                        @if(request()->filled('search') || request()->filled('category'))
                            <a href="{{ route('blog.index') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                                View All Posts
                            </a>
                        @endif
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Categories -->
                @if($categories->count() > 0)
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Categories</h3>
                        <div class="space-y-2">
                            @foreach($categories as $category)
                                <a href="{{ route('blog.category', $category->slug) }}" class="flex items-center justify-between py-2 px-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                    <span class="text-gray-700 dark:text-gray-300">{{ $category->name }}</span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-600 px-2 py-1 rounded-full">
                                        {{ $category->published_posts_count ?? 0 }}
                                    </span>
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Popular Tags -->
                @if(count($popularTags) > 0)
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Popular Tags</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($popularTags as $tag)
                                <a href="{{ route('blog.tag', $tag) }}" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                                    {{ $tag }}
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Recent Posts -->
                @if($recentPosts->count() > 0)
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Posts</h3>
                        <div class="space-y-4">
                            @foreach($recentPosts as $post)
                                <article class="flex space-x-3">
                                    @if($post->featured_image)
                                        <img src="{{ $post->featured_image_url }}" alt="{{ $post->title }}" class="w-16 h-16 object-cover rounded-lg flex-shrink-0">
                                    @else
                                        <div class="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex-shrink-0 flex items-center justify-center">
                                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                            </svg>
                                        </div>
                                    @endif
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1">
                                            <a href="{{ route('blog.show', $post->slug) }}" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                                {{ Str::limit($post->title, 50) }}
                                            </a>
                                        </h4>
                                        <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                            <span>{{ $post->published_at->format('M j, Y') }}</span>
                                            <span class="mx-1">•</span>
                                            <span>{{ $post->view_count }} views</span>
                                        </div>
                                    </div>
                                </article>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
