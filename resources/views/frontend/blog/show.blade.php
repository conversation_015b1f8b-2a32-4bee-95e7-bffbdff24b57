@extends('layouts.frontend')

@section('title', $post->meta_title ?: $post->title)
@section('description', $post->meta_description ?: $post->excerpt)

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Breadcrumb -->
    <nav class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <a href="{{ route('home') }}" class="hover:text-gray-700 dark:hover:text-gray-200">Home</a>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <a href="{{ route('blog.index') }}" class="hover:text-gray-700 dark:hover:text-gray-200">Blog</a>
                @if($post->category)
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <a href="{{ route('blog.category', $post->category->slug) }}" class="hover:text-gray-700 dark:hover:text-gray-200">{{ $post->category->name }}</a>
                @endif
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="text-gray-900 dark:text-white">{{ Str::limit($post->title, 50) }}</span>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-1 gap-8">
            <!-- Main Content -->
            <article class="w-full">
                <!-- Post Header -->
                <header class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8 mb-8">
                    @if($post->is_featured)
                        <div class="mb-6">
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                </svg>
                                Featured Post
                            </span>
                        </div>
                    @endif

                    <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">{{ $post->title }}</h1>

                    @if($post->excerpt)
                        <p class="text-xl text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">{{ $post->excerpt }}</p>
                    @endif

                    <div class="flex flex-wrap items-center gap-6 text-sm text-gray-500 dark:text-gray-400 mb-6">
                        <div class="flex items-center bg-gray-50 dark:bg-gray-700 rounded-lg px-3 py-2">
                            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white font-semibold text-xs">{{ substr($post->author->name, 0, 1) }}</span>
                            </div>
                            <span class="font-medium">{{ $post->author->name }}</span>
                        </div>
                        <div class="flex items-center bg-gray-50 dark:bg-gray-700 rounded-lg px-3 py-2">
                            <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span>{{ $post->published_at->format('F j, Y') }}</span>
                        </div>
                        <div class="flex items-center bg-gray-50 dark:bg-gray-700 rounded-lg px-3 py-2">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>{{ $post->reading_time }}</span>
                        </div>
                        <div class="flex items-center bg-gray-50 dark:bg-gray-700 rounded-lg px-3 py-2">
                            <svg class="w-4 h-4 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            <span>{{ $post->view_count }} views</span>
                        </div>
                        @if($post->category)
                            <div class="flex items-center">
                                <a href="{{ route('blog.category', $post->category->slug) }}" class="inline-flex items-center bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-lg px-3 py-2 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                    </svg>
                                    {{ $post->category->name }}
                                </a>
                            </div>
                        @endif
                    </div>

                    @if($post->tags && is_array($post->tags))
                        <div class="flex flex-wrap gap-2">
                            @foreach($post->tags as $tag)
                                <a href="{{ route('blog.tag', $tag) }}" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-800 dark:text-gray-200 hover:from-gray-200 hover:to-gray-300 dark:hover:from-gray-600 dark:hover:to-gray-500 transition-all duration-200 transform hover:scale-105">
                                    <i class="fas fa-tag mr-1 text-xs"></i>
                                    {{ $tag }}
                                </a>
                            @endforeach
                        </div>
                    @endif
                </header>

                <!-- Featured Image -->
                @if($post->featured_image)
                    <div class="mb-8">
                        <img src="{{ $post->featured_image_url }}" alt="{{ $post->title }}" class="w-full h-96 object-cover rounded-xl">
                    </div>
                @endif

                <!-- Post Content -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8 mb-8">
                    <div class="prose prose-lg max-w-none dark:prose-invert prose-headings:text-gray-900 dark:prose-headings:text-white prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-a:text-blue-600 dark:prose-a:text-blue-400 prose-strong:text-gray-900 dark:prose-strong:text-white">
                        {!! $post->content !!}
                    </div>
                </div>

                <!-- Gallery Images -->
                @if($post->gallery_images && is_array($post->gallery_images))
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8 mb-8">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Gallery</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($post->gallery_images as $image)
                                <img src="{{ asset('uploads/blog/gallery/' . $image) }}" alt="Gallery image" class="w-full h-48 object-cover rounded-lg">
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Social Share -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Share this post</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-3">
                        <!-- X (Twitter) -->
                        <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->url()) }}&text={{ urlencode($post->title) }}" target="_blank" class="flex items-center justify-center p-3 bg-black hover:bg-gray-800 text-white rounded-lg transition-colors duration-200" title="Share on X">
                            <i class="fab fa-x-twitter text-lg"></i>
                        </a>

                        <!-- Facebook -->
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" target="_blank" class="flex items-center justify-center p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200" title="Share on Facebook">
                            <i class="fab fa-facebook-f text-lg"></i>
                        </a>

                        <!-- LinkedIn -->
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->url()) }}" target="_blank" class="flex items-center justify-center p-3 bg-blue-700 hover:bg-blue-800 text-white rounded-lg transition-colors duration-200" title="Share on LinkedIn">
                            <i class="fab fa-linkedin-in text-lg"></i>
                        </a>

                        <!-- WhatsApp -->
                        <a href="https://wa.me/?text={{ urlencode($post->title . ' - ' . request()->url()) }}" target="_blank" class="flex items-center justify-center p-3 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors duration-200" title="Share on WhatsApp">
                            <i class="fab fa-whatsapp text-lg"></i>
                        </a>

                        <!-- Email -->
                        <a href="mailto:?subject={{ urlencode($post->title) }}&body={{ urlencode('Check out this article: ' . request()->url()) }}" class="flex items-center justify-center p-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200" title="Share via Email">
                            <i class="fas fa-envelope text-lg"></i>
                        </a>

                        <!-- Reddit -->
                        <a href="https://reddit.com/submit?url={{ urlencode(request()->url()) }}&title={{ urlencode($post->title) }}" target="_blank" class="flex items-center justify-center p-3 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors duration-200" title="Share on Reddit">
                            <i class="fab fa-reddit-alien text-lg"></i>
                        </a>

                        <!-- Telegram -->
                        <a href="https://t.me/share/url?url={{ urlencode(request()->url()) }}&text={{ urlencode($post->title) }}" target="_blank" class="flex items-center justify-center p-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200" title="Share on Telegram">
                            <i class="fab fa-telegram-plane text-lg"></i>
                        </a>

                        <!-- Copy Link -->
                        <button onclick="copyToClipboard('{{ request()->url() }}')" class="flex items-center justify-center p-3 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200" title="Copy Link">
                            <i class="fas fa-link text-lg"></i>
                        </button>
                    </div>
                </div>

                <script>
                function copyToClipboard(text) {
                    navigator.clipboard.writeText(text).then(function() {
                        // Show success message
                        const button = event.target.closest('button');
                        const originalIcon = button.innerHTML;
                        button.innerHTML = '<i class="fas fa-check text-lg"></i>';
                        button.classList.remove('bg-gray-500', 'hover:bg-gray-600');
                        button.classList.add('bg-green-500', 'hover:bg-green-600');

                        setTimeout(() => {
                            button.innerHTML = originalIcon;
                            button.classList.remove('bg-green-500', 'hover:bg-green-600');
                            button.classList.add('bg-gray-500', 'hover:bg-gray-600');
                        }, 2000);
                    }).catch(function(err) {
                        console.error('Could not copy text: ', err);
                        alert('Link copied to clipboard!');
                    });
                }
                </script>
            </article>
        </div>

        <!-- Related Posts -->
        @if($relatedPosts->count() > 0)
            <div class="mt-16">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-8">Related Posts</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach($relatedPosts as $relatedPost)
                        <article class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-shadow duration-200">
                            @if($relatedPost->featured_image)
                                <img src="{{ $relatedPost->featured_image_url }}" alt="{{ $relatedPost->title }}" class="w-full h-48 object-cover">
                            @endif
                            <div class="p-6">
                                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-2">
                                    <span>{{ $relatedPost->published_at->format('M j, Y') }}</span>
                                    <span class="mx-2">•</span>
                                    <span>{{ $relatedPost->reading_time }}</span>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                    <a href="{{ route('blog.show', $relatedPost->slug) }}" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                        {{ Str::limit($relatedPost->title, 60) }}
                                    </a>
                                </h3>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">{{ Str::limit($relatedPost->excerpt, 100) }}</p>
                            </div>
                        </article>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Comments Section -->
        @if($post->allow_comments)
            <div id="comments" class="mt-16">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-8">
                    Comments ({{ $comments->total() }})
                </h2>

                <!-- Session Messages -->
                @if(session('success'))
                    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <p class="text-green-800 dark:text-green-200">{{ session('success') }}</p>
                        </div>
                    </div>
                @endif

                @if(session('error'))
                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-red-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                            <p class="text-red-800 dark:text-red-200">{{ session('error') }}</p>
                        </div>
                    </div>
                @endif

                <!-- Validation Errors -->
                @if($errors->any())
                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-red-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <h4 class="text-red-800 dark:text-red-200 font-medium mb-2">Please fix the following errors:</h4>
                                <ul class="text-red-700 dark:text-red-300 text-sm space-y-1">
                                    @foreach($errors->all() as $error)
                                        <li>• {{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Comment Form -->
                @auth
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Leave a Comment</h3>
                        <form action="{{ route('blog.comments.store', $post->slug) }}" method="POST">
                            @csrf
                            <div class="mb-4">
                                <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Your Comment</label>
                                <textarea id="content" name="content" rows="4" required class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white" placeholder="Share your thoughts...">{{ old('content') }}</textarea>
                                @error('content')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            <button type="submit" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                                Post Comment
                            </button>
                        </form>
                    </div>
                @else
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8 text-center">
                        <p class="text-gray-600 dark:text-gray-400 mb-4">Please log in to leave a comment.</p>
                        <a href="{{ route('login') }}" class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                            Log In to Comment
                        </a>
                    </div>
                @endauth

                <!-- Comments List -->
                @if($comments->count() > 0)
                    <div class="space-y-6">
                        @foreach($comments as $comment)
                            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-start space-x-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold flex-shrink-0">
                                        {{ substr($comment->user ? $comment->user->name : $comment->author_name, 0, 1) }}
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <h4 class="font-medium text-gray-900 dark:text-white">
                                                {{ $comment->user ? $comment->user->name : $comment->author_name }}
                                            </h4>
                                            <span class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ $comment->created_at->format('M j, Y \a\t g:i A') }}
                                            </span>
                                            @if($comment->is_pinned)
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                                    Pinned
                                                </span>
                                            @endif
                                        </div>
                                        <div class="prose prose-sm max-w-none dark:prose-invert text-gray-700 dark:text-gray-300">
                                            {!! nl2br(e($comment->content)) !!}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Comments Pagination -->
                    @if($comments->hasPages())
                        <div class="mt-8">
                            {{ $comments->links() }}
                        </div>
                    @endif
                @else
                    <div class="text-center py-8">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No comments yet</h3>
                        <p class="text-gray-600 dark:text-gray-400">Be the first to share your thoughts!</p>
                    </div>
                @endif
            </div>
        @endif
    </div>
</div>
@endsection
