@extends('layouts.frontend')

@section('title', 'Categories - ' . config('templatescave.site.name'))
@section('description', 'Browse our product categories to find exactly what you need for your projects.')

@section('content')
<div x-data="app">
    <!-- Page Header -->
    <section class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    Browse Categories
                </h1>
                <p class="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                    Explore our organized collection of digital products. Find exactly what you need for your next project.
                </p>
            </div>
        </div>
    </section>

    <!-- Categories Grid -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($categories->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                    @foreach($categories as $category)
                    <a href="{{ route('categories.show', $category->slug) }}" class="group">
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all group-hover:scale-105 border border-gray-200 dark:border-gray-700">
                            <!-- Category Header -->
                            <div class="p-6 text-center" style="background: linear-gradient(135deg, {{ $category->color ?? '#3B82F6' }}20, {{ $category->color ?? '#3B82F6' }}10);">
                                <!-- Category Icon -->
                                <div class="w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center" style="background-color: {{ $category->color ?? '#3B82F6' }}20;">
                                    @if($category->icon)
                                        <i class="{{ $category->icon }} text-3xl" style="color: {{ $category->color ?? '#3B82F6' }};"></i>
                                    @else
                                        <svg class="w-10 h-10" style="color: {{ $category->color ?? '#3B82F6' }};" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                        </svg>
                                    @endif
                                </div>
                                
                                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
                                    {{ $category->name }}
                                </h3>
                            </div>

                            <!-- Category Content -->
                            <div class="p-6 pt-0">
                                <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                                    {{ $category->description }}
                                </p>
                                
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                        </svg>
                                        {{ $category->products_count }} {{ Str::plural('product', $category->products_count) }}
                                    </div>
                                    
                                    <div class="flex items-center text-sm font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors" style="color: {{ $category->color ?? '#3B82F6' }};">
                                        Explore
                                        <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
                    @endforeach
                </div>
            @else
                <!-- No Categories -->
                <div class="text-center py-16">
                    <svg class="w-24 h-24 text-gray-400 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                        No categories available
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        Categories will appear here once they are added to the marketplace.
                    </p>
                    <a href="{{ route('home') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        Back to Home
                    </a>
                </div>
            @endif
        </div>
    </section>

    <!-- Popular Categories Section -->
    @if($categories->count() > 0)
    <section class="py-12 bg-gray-50 dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    Most Popular Categories
                </h2>
                <p class="text-gray-600 dark:text-gray-400">
                    Categories with the most products and downloads
                </p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                @foreach($categories->sortByDesc('products_count')->take(4) as $category)
                <a href="{{ route('categories.show', $category->slug) }}" class="group text-center">
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 hover:shadow-lg transition-all group-hover:scale-105">
                        <div class="w-12 h-12 mx-auto mb-3 rounded-full flex items-center justify-center" style="background-color: {{ $category->color ?? '#3B82F6' }}20;">
                            @if($category->icon)
                                <i class="{{ $category->icon }} text-lg" style="color: {{ $category->color ?? '#3B82F6' }};"></i>
                            @else
                                <svg class="w-6 h-6" style="color: {{ $category->color ?? '#3B82F6' }};" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                            @endif
                        </div>
                        <h3 class="font-semibold text-gray-900 dark:text-white text-sm mb-1">
                            {{ $category->name }}
                        </h3>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            {{ $category->products_count }} products
                        </p>
                    </div>
                </a>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Call to Action -->
    <section class="py-16 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">
                Can't Find What You're Looking For?
            </h2>
            <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                Browse all our products or use our search feature to find exactly what you need.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('products.index') }}" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    Browse All Products
                </a>
                <a href="{{ route('products.search') }}" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                    Search Products
                </a>
            </div>
        </div>
    </section>
</div>
@endsection
