@extends('layouts.frontend')

@section('title', $category->name . ' - Categories - ' . config('templatescave.site.name'))
@section('description', $category->description)

@section('content')
<div x-data="app">
    <!-- Breadcrumb -->
    <nav class="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <ol class="flex items-center space-x-2 text-sm">
                <li><a href="{{ route('home') }}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">Home</a></li>
                <li><span class="text-gray-400">/</span></li>
                <li><a href="{{ route('categories.index') }}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">Categories</a></li>
                <li><span class="text-gray-400">/</span></li>
                <li><span class="text-gray-900 dark:text-white">{{ $category->name }}</span></li>
            </ol>
        </div>
    </nav>

    <!-- Category Header -->
    <section class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="flex items-center">
                    <!-- Category Icon -->
                    <div class="w-16 h-16 mr-6 rounded-full flex items-center justify-center" style="background-color: {{ $category->color ?? '#3B82F6' }}20;">
                        @if($category->icon)
                            <i class="{{ $category->icon }} text-2xl" style="color: {{ $category->color ?? '#3B82F6' }};"></i>
                        @else
                            <svg class="w-8 h-8" style="color: {{ $category->color ?? '#3B82F6' }};" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        @endif
                    </div>
                    
                    <div>
                        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2">
                            {{ $category->name }}
                        </h1>
                        <p class="text-lg text-gray-600 dark:text-gray-400">
                            {{ $category->description }}
                        </p>
                    </div>
                </div>
                
                <div class="mt-6 md:mt-0 text-center md:text-right">
                    <div class="text-3xl font-bold text-gray-900 dark:text-white">
                        {{ $products->total() }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ Str::plural('Product', $products->total()) }}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filters and Search -->
    <section class="bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <form method="GET" class="flex flex-col lg:flex-row gap-4">
                <!-- Search Input -->
                <div class="flex-1">
                    <input 
                        type="text" 
                        name="q" 
                        value="{{ request('q') }}"
                        placeholder="Search in {{ $category->name }}..." 
                        class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                </div>

                <!-- Price Filter -->
                <div class="lg:w-32">
                    <select name="price" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">Any Price</option>
                        <option value="free" {{ request('price') == 'free' ? 'selected' : '' }}>Free</option>
                        <option value="paid" {{ request('price') == 'paid' ? 'selected' : '' }}>Paid</option>
                    </select>
                </div>

                <!-- Sort Filter -->
                <div class="lg:w-40">
                    <select name="sort" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="latest" {{ request('sort') == 'latest' ? 'selected' : '' }}>Latest</option>
                        <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>Most Popular</option>
                        <option value="downloads" {{ request('sort') == 'downloads' ? 'selected' : '' }}>Most Downloaded</option>
                        <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                        <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                    </select>
                </div>

                <!-- Filter Button -->
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    Filter
                </button>

                <!-- Clear Filters -->
                @if(request()->hasAny(['q', 'price', 'sort']))
                    <a href="{{ route('categories.show', $category->slug) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors text-center">
                        Clear
                    </a>
                @endif
            </form>
        </div>
    </section>

    <!-- Products Grid -->
    <section class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($products->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    @foreach($products as $product)
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <!-- Product Image -->
                        <div class="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 relative overflow-hidden">
                            @if($product->primaryImage)
                                <img src="{{ $product->primaryImage->image_path }}" alt="{{ $product->title }}" class="w-full h-full object-cover">
                            @else
                                <div class="w-full h-full flex items-center justify-center">
                                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            <!-- Badges -->
                            <div class="absolute top-3 left-3 flex flex-col gap-2">
                                @if($product->is_featured)
                                    <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                        Featured
                                    </span>
                                @endif
                                @if($product->created_at->isToday())
                                    <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                        New
                                    </span>
                                @endif
                            </div>

                            <!-- Price Badge -->
                            <div class="absolute top-3 right-3">
                                @if($product->is_free)
                                    <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                        Free
                                    </span>
                                @else
                                    <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                        ${{ number_format($product->price, 2) }}
                                    </span>
                                @endif
                            </div>
                        </div>

                        <!-- Product Info -->
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    {{ number_format($product->view_count) }}
                                </div>
                            </div>
                            
                            <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                                {{ $product->title }}
                            </h3>
                            
                            <p class="text-gray-600 dark:text-gray-400 text-xs mb-3 line-clamp-2">
                                {{ $product->short_description }}
                            </p>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                    {{ number_format($product->download_count) }}
                                </div>
                                
                                <a href="{{ route('products.show', $product->slug) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors">
                                    View
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12">
                    {{ $products->appends(request()->query())->links() }}
                </div>
            @else
                <!-- No Products Found -->
                <div class="text-center py-16">
                    <svg class="w-24 h-24 text-gray-400 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0m6 0V5a2 2 0 00-2-2H9a2 2 0 00-2 2v1.306m8 0V7a2 2 0 012 2v6a2 2 0 01-2 2H7a2 2 0 01-2-2V9a2 2 0 012-2h8a2 2 0 012 2z"></path>
                    </svg>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                        No products found in {{ $category->name }}
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        @if(request()->hasAny(['q', 'price']))
                            Try adjusting your search criteria or browse other categories.
                        @else
                            Products will appear here once they are added to this category.
                        @endif
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        @if(request()->hasAny(['q', 'price']))
                            <a href="{{ route('categories.show', $category->slug) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                View All {{ $category->name }} Products
                            </a>
                        @endif
                        <a href="{{ route('categories.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            Browse Other Categories
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </section>
</div>
@endsection
