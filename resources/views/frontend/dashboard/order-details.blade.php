@extends('layouts.frontend')

@section('title', 'Order #' . $order->order_number . ' - ' . config('templatescave.site.name'))
@section('description', 'Order details and download history')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Order #{{ $order->order_number }}</h1>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">Order placed on {{ $order->created_at->format('F j, Y \a\t g:i A') }}</p>
                </div>
                <a href="{{ route('dashboard.orders') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Orders
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Order Details -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Order Status -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Order Status</h2>
                    <div class="flex items-center justify-between">
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                            @if($order->status === 'completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                            @elseif($order->status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                            @elseif($order->status === 'failed') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                            @elseif($order->status === 'refunded') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                            @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                            {{ ucfirst($order->status) }}
                        </span>
                        @if($order->status === 'completed')
                            <a href="{{ route('products.download', $order->product->slug) }}" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Download Product
                            </a>
                        @endif
                    </div>
                </div>

                <!-- Product Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Product Details</h2>
                    <div class="flex items-start space-x-4">
                        @if($order->product->primaryImage)
                            <img class="h-20 w-20 rounded-lg object-cover" src="{{ Storage::url($order->product->primaryImage->image_path) }}" alt="{{ $order->product->title }}">
                        @else
                            <div class="h-20 w-20 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        @endif
                        <div class="flex-1">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $order->product->title }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">{{ $order->product->category->name }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-300">{{ Str::limit($order->product->short_description, 150) }}</p>
                            <div class="mt-3">
                                <a href="{{ route('products.show', $order->product->slug) }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                                    View Product Page →
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Download History -->
                @if($downloads->count() > 0)
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Download History</h2>
                        <div class="space-y-3">
                            @foreach($downloads as $download)
                                <div class="flex items-center justify-between py-3 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">Downloaded</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">{{ $download->downloaded_at->format('M j, Y \a\t g:i A') }}</p>
                                        </div>
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        IP: {{ $download->ip_address }}
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <!-- Order Summary -->
            <div class="space-y-6">
                <!-- Payment Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Payment Information</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Payment Method</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                                @if($order->payment_method === 'stripe')
                                    Credit Card
                                @elseif($order->payment_method === 'bank_transfer')
                                    Bank Transfer
                                @elseif($order->payment_method === 'free')
                                    Free Download
                                @else
                                    {{ ucfirst($order->payment_method) }}
                                @endif
                            </span>
                        </div>
                        @if($order->payment_intent_id)
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Payment ID</span>
                                <span class="text-sm font-mono text-gray-900 dark:text-white">{{ $order->payment_intent_id }}</span>
                            </div>
                        @endif
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Subtotal</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                                @if($order->amount > 0)
                                    ${{ number_format($order->amount, 2) }}
                                @else
                                    Free
                                @endif
                            </span>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                            <div class="flex justify-between">
                                <span class="text-base font-medium text-gray-900 dark:text-white">Total</span>
                                <span class="text-base font-bold text-gray-900 dark:text-white">
                                    @if($order->amount > 0)
                                        ${{ number_format($order->amount, 2) }}
                                    @else
                                        <span class="text-green-600 dark:text-green-400">Free</span>
                                    @endif
                                </span>
                            </div>
                        </div>
                        @if($order->paid_at)
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Paid At</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $order->paid_at->format('M j, Y \a\t g:i A') }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Order Timeline -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Order Timeline</h2>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-white">Order Created</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ $order->created_at->format('M j, Y \a\t g:i A') }}</p>
                            </div>
                        </div>
                        @if($order->paid_at)
                            <div class="flex items-start">
                                <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                    <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">Payment Completed</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $order->paid_at->format('M j, Y \a\t g:i A') }}</p>
                                </div>
                            </div>
                        @endif
                        @if($downloads->count() > 0)
                            <div class="flex items-start">
                                <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                    <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">First Download</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $downloads->first()->downloaded_at->format('M j, Y \a\t g:i A') }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Support -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Need Help?</h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        If you have any questions about this order or need assistance with your download, please contact our support team.
                    </p>
                    <a href="mailto:<EMAIL>" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors text-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
