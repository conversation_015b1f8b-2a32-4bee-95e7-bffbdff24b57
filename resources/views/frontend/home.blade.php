@extends('layouts.frontend')

@section('title', config('templatescave.site.name') . ' - ' . config('templatescave.site.tagline'))
@section('description', config('templatescave.site.description'))

@section('content')
<div x-data="app">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
            <div class="text-center">
                <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 leading-tight">
                    Discover Premium
                    <span class="block sm:inline text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">
                        Digital Assets
                    </span>
                </h1>
                <p class="text-lg sm:text-xl md:text-2xl text-blue-100 mb-6 sm:mb-8 max-w-3xl mx-auto leading-relaxed px-4 sm:px-0">
                    {{ config('templatescave.site.description') }}
                </p>
                <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center max-w-md sm:max-w-none mx-auto">
                    <a href="{{ route('products.index') }}"
                       class="bg-white text-blue-600 px-6 sm:px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors text-center">
                        Browse Products
                    </a>
                    <a href="{{ route('categories.index') }}"
                       class="border-2 border-white text-white px-6 sm:px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors text-center">
                        Explore Categories
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    @if($featuredProducts->count() > 0)
    <section class="py-12 sm:py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8 sm:mb-12">
                <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-3 sm:mb-4">
                    Featured Products
                </h2>
                <p class="text-base sm:text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                    Hand-picked premium digital assets for your projects
                </p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
                @foreach($featuredProducts as $product)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    <!-- Product Image -->
                    <div class="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 relative overflow-hidden">
                        @if($product->primaryImage)
                            <img src="{{ $product->primaryImage->image_path }}" alt="{{ $product->title }}" class="w-full h-full object-cover">
                        @else
                            <div class="w-full h-full flex items-center justify-center">
                                <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        @endif
                        
                        <!-- Featured Badge -->
                        <div class="absolute top-3 left-3">
                            <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                Featured
                            </span>
                        </div>

                        <!-- Price Badge -->
                        <div class="absolute top-3 right-3">
                            @if($product->is_free)
                                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                    Free
                                </span>
                            @else
                                <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                    ${{ number_format($product->price, 2) }}
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- Product Info -->
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm text-blue-600 dark:text-blue-400 font-medium">
                                {{ $product->category->name }}
                            </span>
                            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                {{ number_format($product->view_count) }}
                            </div>
                        </div>
                        
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                            {{ $product->title }}
                        </h3>
                        
                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                            {{ $product->short_description }}
                        </p>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                                {{ number_format($product->download_count) }} downloads
                            </div>
                            
                            <a href="{{ route('products.show', $product->slug) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <div class="text-center mt-12">
                <a href="{{ route('products.index') }}" class="bg-gray-900 dark:bg-white text-white dark:text-gray-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-800 dark:hover:bg-gray-100 transition-colors">
                    View All Products
                </a>
            </div>
        </div>
    </section>
    @endif

    <!-- Categories Section -->
    @if($categories->count() > 0)
    <section class="py-16 bg-gray-50 dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    Browse by Category
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-400">
                    Find exactly what you're looking for
                </p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                @foreach($categories as $category)
                <a href="{{ route('categories.show', $category->slug) }}" class="group">
                    <div class="bg-white dark:bg-gray-700 rounded-xl p-6 text-center hover:shadow-lg transition-all group-hover:scale-105">
                        <!-- Category Icon -->
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style="background-color: {{ $category->color ?? '#3B82F6' }}20;">
                            @if($category->icon)
                                <i class="{{ $category->icon }} text-2xl" style="color: {{ $category->color ?? '#3B82F6' }};"></i>
                            @else
                                <svg class="w-8 h-8" style="color: {{ $category->color ?? '#3B82F6' }};" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                            @endif
                        </div>
                        
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            {{ $category->name }}
                        </h3>
                        
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                            {{ $category->description }}
                        </p>
                        
                        <span class="text-xs text-gray-500 dark:text-gray-400">
                            {{ $category->products_count }} {{ Str::plural('product', $category->products_count) }}
                        </span>
                    </div>
                </a>
                @endforeach
            </div>

            <div class="text-center mt-12">
                <a href="{{ route('categories.index') }}" class="text-blue-600 dark:text-blue-400 font-semibold hover:text-blue-700 dark:hover:text-blue-300">
                    View All Categories →
                </a>
            </div>
        </div>
    </section>
    @endif

    <!-- Stats Section -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div>
                    <div class="text-3xl md:text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                        {{ number_format($stats['total_products']) }}+
                    </div>
                    <div class="text-gray-600 dark:text-gray-400">
                        Digital Products
                    </div>
                </div>
                
                <div>
                    <div class="text-3xl md:text-4xl font-bold text-green-600 dark:text-green-400 mb-2">
                        {{ number_format($stats['total_downloads']) }}+
                    </div>
                    <div class="text-gray-600 dark:text-gray-400">
                        Downloads
                    </div>
                </div>
                
                <div>
                    <div class="text-3xl md:text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                        {{ number_format($stats['total_users']) }}+
                    </div>
                    <div class="text-gray-600 dark:text-gray-400">
                        Happy Users
                    </div>
                </div>
                
                <div>
                    <div class="text-3xl md:text-4xl font-bold text-orange-600 dark:text-orange-400 mb-2">
                        {{ number_format($stats['total_categories']) }}+
                    </div>
                    <div class="text-gray-600 dark:text-gray-400">
                        Categories
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16 bg-gray-900 dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
                Stay Updated
            </h2>
            <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Get notified about new products, updates, and exclusive offers
            </p>
            
            <form class="max-w-md mx-auto flex gap-4">
                <input 
                    type="email" 
                    placeholder="Enter your email" 
                    class="flex-1 px-4 py-3 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                >
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                    Subscribe
                </button>
            </form>
        </div>
    </section>
</div>
@endsection
