@extends('layouts.frontend')

@section('title', $page->meta_title ?: $page->title)
@section('description', $page->meta_description ?: Str::limit(strip_tags($page->content), 160))

@if($page->meta_keywords)
@section('keywords', is_array($page->meta_keywords) ? implode(', ', $page->meta_keywords) : $page->meta_keywords)
@endif

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Page Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {{ $page->title }}
            </h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto rounded-full"></div>
        </div>

        <!-- Page Content -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="p-8 lg:p-12">
                <div class="prose prose-lg max-w-none dark:prose-invert prose-headings:text-gray-900 dark:prose-headings:text-white prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-a:text-blue-600 dark:prose-a:text-blue-400 prose-strong:text-gray-900 dark:prose-strong:text-white">
                    {!! $page->content !!}
                </div>
            </div>
        </div>

        <!-- Back to Home -->
        <div class="text-center mt-12">
            <a href="{{ route('home') }}" 
               class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Home
            </a>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Custom prose styles for better dark mode support */
    .prose.dark\:prose-invert h1,
    .prose.dark\:prose-invert h2,
    .prose.dark\:prose-invert h3,
    .prose.dark\:prose-invert h4,
    .prose.dark\:prose-invert h5,
    .prose.dark\:prose-invert h6 {
        color: rgb(255 255 255);
    }
    
    .prose.dark\:prose-invert p,
    .prose.dark\:prose-invert li {
        color: rgb(209 213 219);
    }
    
    .prose.dark\:prose-invert a {
        color: rgb(96 165 250);
    }
    
    .prose.dark\:prose-invert a:hover {
        color: rgb(147 197 253);
    }
    
    .prose.dark\:prose-invert strong {
        color: rgb(255 255 255);
    }
    
    .prose.dark\:prose-invert blockquote {
        color: rgb(156 163 175);
        border-left-color: rgb(75 85 99);
    }
    
    .prose.dark\:prose-invert code {
        color: rgb(249 250 251);
        background-color: rgb(55 65 81);
    }
    
    .prose.dark\:prose-invert pre {
        background-color: rgb(31 41 55);
        color: rgb(249 250 251);
    }
    
    .prose.dark\:prose-invert hr {
        border-color: rgb(75 85 99);
    }
    
    .prose.dark\:prose-invert table {
        color: rgb(209 213 219);
    }
    
    .prose.dark\:prose-invert thead {
        color: rgb(255 255 255);
        border-bottom-color: rgb(75 85 99);
    }
    
    .prose.dark\:prose-invert tbody tr {
        border-bottom-color: rgb(55 65 81);
    }
</style>
@endpush
