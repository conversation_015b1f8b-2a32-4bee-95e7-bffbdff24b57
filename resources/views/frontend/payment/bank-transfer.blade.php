@extends('layouts.frontend')

@section('title', 'Bank Transfer Instructions - ' . config('templatescave.site.name'))
@section('description', 'Complete your payment via bank transfer')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 dark:bg-blue-900 mb-4">
                <svg class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Bank Transfer Payment</h1>
            <p class="mt-2 text-lg text-gray-600 dark:text-gray-400">Complete your payment using the details below</p>
        </div>

        <!-- Order Summary -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden mb-8">
            <div class="px-6 py-4 bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800">
                <h2 class="text-lg font-semibold text-blue-800 dark:text-blue-200">Order Summary</h2>
                <p class="text-sm text-blue-600 dark:text-blue-400">Order #{{ $order->order_number }}</p>
            </div>
            
            <div class="p-6">
                <div class="flex items-start space-x-4 mb-4">
                    <div class="flex-shrink-0">
                        @if($order->product->primaryImage)
                            <img src="{{ Storage::url($order->product->primaryImage->image_path) }}" 
                                 alt="{{ $order->product->title }}" 
                                 class="w-16 h-16 object-cover rounded-lg">
                        @else
                            <div class="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        @endif
                    </div>
                    
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $order->product->title }}</h3>
                        <p class="text-gray-600 dark:text-gray-400">{{ $order->product->category->name }}</p>
                        <div class="mt-2">
                            <span class="text-xl font-bold text-gray-900 dark:text-white">
                                ${{ number_format($order->amount, 2) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bank Transfer Details -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Bank Transfer Details</h2>
            
            @if($bankDetails && $bankDetails['enabled'])
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="space-y-4">
                    @if($bankDetails['account_name'])
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Account Name</label>
                        <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                            <span class="text-gray-900 dark:text-white font-medium">{{ $bankDetails['account_name'] }}</span>
                            <button onclick="copyToClipboard('{{ $bankDetails['account_name'] }}')" 
                                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    @endif

                    @if($bankDetails['account_number'])
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Account Number</label>
                        <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                            <span class="text-gray-900 dark:text-white font-medium">{{ $bankDetails['account_number'] }}</span>
                            <button onclick="copyToClipboard('{{ $bankDetails['account_number'] }}')" 
                                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    @endif

                    @if($bankDetails['bank_name'])
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Bank Name</label>
                        <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                            <span class="text-gray-900 dark:text-white font-medium">{{ $bankDetails['bank_name'] }}</span>
                        </div>
                    </div>
                    @endif
                </div>

                <div class="space-y-4">
                    @if($bankDetails['bank_code'])
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Bank Code</label>
                        <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                            <span class="text-gray-900 dark:text-white font-medium">{{ $bankDetails['bank_code'] }}</span>
                            <button onclick="copyToClipboard('{{ $bankDetails['bank_code'] }}')" 
                                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    @endif

                    @if($bankDetails['swift_code'])
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">SWIFT Code</label>
                        <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                            <span class="text-gray-900 dark:text-white font-medium">{{ $bankDetails['swift_code'] }}</span>
                            <button onclick="copyToClipboard('{{ $bankDetails['swift_code'] }}')" 
                                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    @endif

                    @if($bankDetails['iban'])
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">IBAN</label>
                        <div class="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                            <span class="text-gray-900 dark:text-white font-medium">{{ $bankDetails['iban'] }}</span>
                            <button onclick="copyToClipboard('{{ $bankDetails['iban'] }}')" 
                                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Transfer Amount -->
            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <div>
                        <p class="font-medium text-yellow-800 dark:text-yellow-200">Transfer Amount: ${{ number_format($order->amount, 2) }}</p>
                        <p class="text-sm text-yellow-700 dark:text-yellow-300">Please transfer the exact amount to avoid delays</p>
                    </div>
                </div>
            </div>

            <!-- Reference -->
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <p class="font-medium text-blue-800 dark:text-blue-200">Reference: {{ $order->order_number }}</p>
                        <p class="text-sm text-blue-700 dark:text-blue-300">Include this reference in your transfer</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Payment Instructions</h3>
            <ol class="list-decimal list-inside space-y-3 text-gray-700 dark:text-gray-300">
                <li>Transfer the exact amount of <strong>${{ number_format($order->amount, 2) }}</strong> to the bank account above</li>
                <li>Include the reference number <strong>{{ $order->order_number }}</strong> in your transfer description</li>
                <li>Email us a copy of your transfer receipt to <a href="mailto:{{ config('templatescave.email.support_email') }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">{{ config('templatescave.email.support_email') }}</a></li>
                <li>We will verify your payment and activate your download within 1-2 business days</li>
                <li>You will receive an email confirmation once your payment is verified</li>
            </ol>

            @if($bankDetails && $bankDetails['instructions'])
            <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    <strong>Additional Instructions:</strong> {{ $bankDetails['instructions'] }}
                </p>
            </div>
            @endif
        </div>

        <!-- Contact Support -->
        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-6 text-center">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Need Help?</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                If you have any questions about the payment process, please don't hesitate to contact us.
            </p>
            <a href="mailto:{{ config('templatescave.email.support_email') }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                Contact Support
            </a>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show a temporary success message
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.innerHTML = '<svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
        
        setTimeout(() => {
            button.innerHTML = originalContent;
        }, 2000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
    });
}
</script>
@endsection
