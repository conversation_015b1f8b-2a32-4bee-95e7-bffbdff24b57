@extends('layouts.frontend')

@section('title', 'Cryptocurrency Payment - ' . config('templatescave.site.name'))
@section('description', 'Complete your cryptocurrency payment')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8" x-data="cryptoPayment()">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Cryptocurrency Payment</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">Complete your payment with cryptocurrency</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Order Summary -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Order Summary</h2>
                
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        @if($order->product->primaryImage)
                            <img src="{{ Storage::url($order->product->primaryImage->image_path) }}" 
                                 alt="{{ $order->product->title }}" 
                                 class="w-20 h-20 object-cover rounded-lg">
                        @else
                            <div class="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        @endif
                    </div>
                    
                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $order->product->title }}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $order->product->category->name }}</p>
                        <p class="text-sm text-gray-500 dark:text-gray-500 mt-2">Order #{{ $order->order_number }}</p>
                    </div>
                </div>

                <div class="border-t border-gray-200 dark:border-gray-700 mt-6 pt-6">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-medium text-gray-900 dark:text-white">Total</span>
                        <span class="text-2xl font-bold text-gray-900 dark:text-white">
                            ${{ number_format($order->amount, 2) }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Payment Details -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Payment Details</h2>

                @if($order->payment_details && isset($order->payment_details['pay_address']))
                    <!-- Payment Information -->
                    <div class="space-y-6">
                        <!-- Payment Status -->
                        <div class="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-blue-500 rounded-full mr-3 animate-pulse"></div>
                                <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
                                    Status: <span x-text="paymentStatus" class="capitalize">{{ $order->payment_details['payment_status'] ?? 'waiting' }}</span>
                                </span>
                            </div>
                            <button @click="checkPaymentStatus()" 
                                    class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm font-medium">
                                Refresh
                            </button>
                        </div>

                        <!-- Payment Address -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Send {{ strtoupper($order->payment_details['pay_currency']) }} to this address:
                            </label>
                            <div class="flex items-center space-x-2">
                                <input type="text" 
                                       value="{{ $order->payment_details['pay_address'] }}" 
                                       readonly
                                       class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-mono">
                                <button @click="copyToClipboard('{{ $order->payment_details['pay_address'] }}')"
                                        class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors">
                                    Copy
                                </button>
                            </div>
                        </div>

                        <!-- Payment Amount -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Amount to send:
                            </label>
                            <div class="flex items-center space-x-2">
                                <input type="text" 
                                       value="{{ $order->payment_details['pay_amount'] }} {{ strtoupper($order->payment_details['pay_currency']) }}" 
                                       readonly
                                       class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-mono">
                                <button @click="copyToClipboard('{{ $order->payment_details['pay_amount'] }}')"
                                        class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors">
                                    Copy
                                </button>
                            </div>
                        </div>

                        <!-- QR Code (if available) -->
                        @if(isset($order->payment_details['pay_address']))
                        <div class="text-center">
                            <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Or scan QR code:</p>
                            <div class="inline-block p-4 bg-white rounded-lg">
                                <div id="qr-code" class="w-48 h-48 mx-auto"></div>
                            </div>
                        </div>
                        @endif

                        <!-- Instructions -->
                        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                            <div class="flex">
                                <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div class="text-sm text-yellow-800 dark:text-yellow-200">
                                    <p class="font-medium mb-2">Important Instructions:</p>
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>Send exactly the amount shown above</li>
                                        <li>Use the correct cryptocurrency network</li>
                                        <li>Payment will be confirmed automatically</li>
                                        <li>Do not close this page until payment is confirmed</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Auto-refresh notice -->
                        <div class="text-center">
                            <p class="text-xs text-gray-500 dark:text-gray-500">
                                This page will automatically check for payment confirmation every 30 seconds
                            </p>
                        </div>
                    </div>
                @else
                    <!-- Loading or Error State -->
                    <div class="text-center py-8">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p class="text-gray-600 dark:text-gray-400">Loading payment details...</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- QR Code Library -->
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

<script>
function cryptoPayment() {
    return {
        paymentStatus: '{{ $order->payment_details['payment_status'] ?? 'waiting' }}',
        checkInterval: null,
        
        init() {
            // Generate QR code if payment address is available
            @if($order->payment_details && isset($order->payment_details['pay_address']))
            this.generateQRCode('{{ $order->payment_details['pay_address'] }}');
            @endif
            
            // Start auto-checking payment status
            this.startStatusChecking();
        },
        
        generateQRCode(address) {
            const qrElement = document.getElementById('qr-code');
            if (qrElement && typeof QRCode !== 'undefined') {
                QRCode.toCanvas(qrElement, address, {
                    width: 192,
                    height: 192,
                    margin: 2,
                }, function (error) {
                    if (error) console.error('QR Code generation failed:', error);
                });
            }
        },
        
        async copyToClipboard(text) {
            try {
                await navigator.clipboard.writeText(text);
                // Show a temporary success message
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.classList.add('bg-green-600');
                button.classList.remove('bg-blue-600');
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('bg-green-600');
                    button.classList.add('bg-blue-600');
                }, 2000);
            } catch (err) {
                console.error('Failed to copy text: ', err);
                alert('Failed to copy to clipboard');
            }
        },
        
        async checkPaymentStatus() {
            try {
                const response = await fetch('{{ route('payment.crypto.status', $order->id) }}');
                const result = await response.json();
                
                if (result.success) {
                    this.paymentStatus = result.status;
                    
                    // Redirect on successful payment
                    if (result.order_status === 'completed') {
                        window.location.href = '{{ route('payment.crypto.success', $order->id) }}';
                    } else if (result.order_status === 'failed') {
                        window.location.href = '{{ route('payment.crypto.cancel', $order->id) }}';
                    }
                }
            } catch (error) {
                console.error('Failed to check payment status:', error);
            }
        },
        
        startStatusChecking() {
            // Check status every 30 seconds
            this.checkInterval = setInterval(() => {
                this.checkPaymentStatus();
            }, 30000);
        },
        
        destroy() {
            if (this.checkInterval) {
                clearInterval(this.checkInterval);
            }
        }
    }
}
</script>
@endsection
