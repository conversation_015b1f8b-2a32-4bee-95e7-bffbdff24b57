@extends('layouts.frontend')

@section('title', 'Purchase ' . $product->title . ' - ' . $settings['site_name'])
@section('description', 'Complete your purchase of ' . $product->title)

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8" x-data="paymentForm()">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Complete Your Purchase</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">Secure payment powered by Stripe</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Product Summary -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Order Summary</h2>
                
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        @if($product->primaryImage)
                            <img src="{{ Storage::url($product->primaryImage->image_path) }}" 
                                 alt="{{ $product->title }}" 
                                 class="w-20 h-20 object-cover rounded-lg">
                        @else
                            <div class="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        @endif
                    </div>
                    
                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $product->title }}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $product->category->name }}</p>
                        @if($product->short_description)
                            <p class="text-sm text-gray-500 dark:text-gray-500 mt-2">{{ Str::limit($product->short_description, 100) }}</p>
                        @endif
                    </div>
                </div>

                <div class="border-t border-gray-200 dark:border-gray-700 mt-6 pt-6">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-medium text-gray-900 dark:text-white">Total</span>
                        <span class="text-2xl font-bold text-gray-900 dark:text-white">
                            {{ $settings['currency_symbol'] }}{{ number_format($product->price, 2) }}
                        </span>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-500 mt-1">One-time payment • Instant download</p>
                </div>

                <!-- Product Features -->
                <div class="mt-6">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">What you'll get:</h4>
                    <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Instant download access
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Lifetime access to downloads
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Commercial license included
                        </li>
                        @if($product->demo_url)
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <a href="{{ $product->demo_url }}" target="_blank" class="text-blue-600 hover:text-blue-800">Live preview available</a>
                        </li>
                        @endif
                    </ul>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Payment Method</h2>

                <!-- Payment Method Selection -->
                <div class="space-y-4 mb-6">
                    @foreach($enabledMethods as $method => $config)
                    <label class="relative flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                           :class="{ 'border-blue-500 bg-blue-50 dark:bg-blue-900/20': selectedMethod === '{{ $method }}' }">
                        <input type="radio" 
                               name="payment_method" 
                               value="{{ $method }}" 
                               x-model="selectedMethod"
                               class="sr-only">
                        <div class="flex items-center space-x-3 flex-1">
                            <i class="{{ $config['icon'] }} text-xl text-gray-600 dark:text-gray-400"></i>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">{{ $config['name'] }}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-500">{{ $config['description'] }}</div>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="w-4 h-4 border-2 border-gray-300 rounded-full flex items-center justify-center"
                                 :class="{ 'border-blue-500': selectedMethod === '{{ $method }}' }">
                                <div class="w-2 h-2 bg-blue-500 rounded-full" 
                                     x-show="selectedMethod === '{{ $method }}'"></div>
                            </div>
                        </div>
                    </label>
                    @endforeach
                </div>

                <!-- Stripe Payment Form -->
                <div x-show="selectedMethod === 'stripe'" x-transition>
                    <div id="card-element" class="p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 mb-4">
                        <!-- Stripe Elements will create form elements here -->
                    </div>
                    <div id="card-errors" class="text-red-600 text-sm mb-4" role="alert"></div>
                </div>

                <!-- Bank Transfer Info -->
                <div x-show="selectedMethod === 'bank_transfer'" x-transition>
                    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="text-sm text-blue-800 dark:text-blue-200">
                                <p class="font-medium mb-1">Bank Transfer Instructions</p>
                                <p>After clicking "Complete Order", you'll receive bank details and payment instructions via email.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Crypto Payment Form -->
                <div x-show="selectedMethod === 'crypto'" x-transition x-data="{ selectedCrypto: 'btc', availableCryptos: {} }" x-init="loadCryptoCurrencies()">
                    <div class="space-y-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Select Cryptocurrency
                            </label>
                            <select x-model="selectedCrypto"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <template x-for="(name, code) in availableCryptos" :key="code">
                                    <option :value="code" x-text="name + ' (' + code.toUpperCase() + ')'"></option>
                                </template>
                            </select>
                        </div>

                        <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                            <div class="flex">
                                <svg class="w-5 h-5 text-orange-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div class="text-sm text-orange-800 dark:text-orange-200">
                                    <p class="font-medium mb-1">Cryptocurrency Payment</p>
                                    <p>You'll be redirected to complete your payment with the selected cryptocurrency. The payment will be processed securely through our crypto payment gateway.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <button type="button" 
                        @click="processPayment()"
                        :disabled="processing || !selectedMethod"
                        :class="{ 'opacity-50 cursor-not-allowed': processing || !selectedMethod }"
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                    <span x-show="!processing">
                        <span x-text="selectedMethod === 'bank_transfer' ? 'Complete Order' : selectedMethod === 'paypal' ? 'Pay with PayPal' : selectedMethod === 'crypto' ? 'Pay with Crypto' : 'Pay {{ $settings['currency_symbol'] }}{{ number_format($product->price, 2) }}'"></span>
                    </span>
                    <span x-show="processing" class="flex items-center justify-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                    </span>
                </button>

                <!-- Security Notice -->
                <div class="mt-4 text-center">
                    <p class="text-xs text-gray-500 dark:text-gray-500">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                        Your payment information is secure and encrypted
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@if(isset($enabledMethods['stripe']) && $enabledMethods['stripe']['enabled'])
<script src="https://js.stripe.com/v3/"></script>
@endif

<script>
function paymentForm() {
    return {
        selectedMethod: '{{ array_key_first($enabledMethods) }}',
        processing: false,
        stripe: null,
        cardElement: null,
        
        init() {
            @if(isset($enabledMethods['stripe']) && $enabledMethods['stripe']['enabled'])
            // Initialize Stripe
            this.stripe = Stripe('{{ config('services.stripe.key') }}');
            const elements = this.stripe.elements();
            
            this.cardElement = elements.create('card', {
                style: {
                    base: {
                        fontSize: '16px',
                        color: '#424770',
                        '::placeholder': {
                            color: '#aab7c4',
                        },
                    },
                },
            });
            
            this.cardElement.mount('#card-element');
            
            this.cardElement.on('change', ({error}) => {
                const displayError = document.getElementById('card-errors');
                if (error) {
                    displayError.textContent = error.message;
                } else {
                    displayError.textContent = '';
                }
            });
            @endif
        },
        
        async processPayment() {
            if (this.processing) return;

            this.processing = true;

            try {
                if (this.selectedMethod === 'stripe') {
                    await this.processStripePayment();
                } else if (this.selectedMethod === 'paypal') {
                    await this.processPayPalPayment();
                } else if (this.selectedMethod === 'bank_transfer') {
                    await this.processBankTransfer();
                } else if (this.selectedMethod === 'crypto') {
                    await this.processCryptoPayment();
                }
            } catch (error) {
                console.error('Payment processing error:', error);
                alert('Payment processing failed. Please try again.');
            } finally {
                this.processing = false;
            }
        },
        
        async processStripePayment() {
            // Create payment intent
            const response = await fetch('{{ route('payment.create-intent', $product->slug) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                },
                body: JSON.stringify({
                    payment_method: 'stripe'
                })
            });
            
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.error);
            }
            
            // Confirm payment with Stripe
            const {error} = await this.stripe.confirmCardPayment(result.client_secret, {
                payment_method: {
                    card: this.cardElement,
                }
            });
            
            if (error) {
                throw new Error(error.message);
            } else {
                // Payment succeeded, redirect to success page
                window.location.href = '{{ route('payment.success', ':order_id') }}'.replace(':order_id', result.order.id);
            }
        },

        async processPayPalPayment() {
            const response = await fetch('{{ route('payment.create-intent', $product->slug) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                },
                body: JSON.stringify({
                    payment_method: 'paypal'
                })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error);
            }

            // Redirect to PayPal for payment approval
            window.location.href = result.approval_url;
        },

        async processBankTransfer() {
            const response = await fetch('{{ route('payment.create-intent', $product->slug) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                },
                body: JSON.stringify({
                    payment_method: 'bank_transfer'
                })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error);
            }

            // Redirect to bank transfer instructions
            window.location.href = '{{ route('payment.bank-transfer', ':order_id') }}'.replace(':order_id', result.order.id);
        },

        async processCryptoPayment() {
            // Get the selected cryptocurrency from the crypto form
            const cryptoForm = document.querySelector('[x-show="selectedMethod === \'crypto\'"]');
            const selectedCrypto = cryptoForm ? cryptoForm.__x.$data.selectedCrypto : 'btc';

            const response = await fetch('{{ route('payment.crypto.create', $product->slug) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                },
                body: JSON.stringify({
                    pay_currency: selectedCrypto
                })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error);
            }

            // Redirect to crypto payment page
            window.location.href = result.redirect_url;
        },

        async loadCryptoCurrencies() {
            try {
                const response = await fetch('{{ route('payment.crypto.currencies') }}');
                const result = await response.json();

                if (result.success) {
                    // Update the crypto form data
                    const cryptoForm = document.querySelector('[x-show="selectedMethod === \'crypto\'"]');
                    if (cryptoForm && cryptoForm.__x) {
                        cryptoForm.__x.$data.availableCryptos = result.currencies;
                    }
                }
            } catch (error) {
                console.error('Failed to load crypto currencies:', error);
                // Set default currencies as fallback
                const cryptoForm = document.querySelector('[x-show="selectedMethod === \'crypto\'"]');
                if (cryptoForm && cryptoForm.__x) {
                    cryptoForm.__x.$data.availableCryptos = {
                        'btc': 'Bitcoin',
                        'eth': 'Ethereum',
                        'ltc': 'Litecoin',
                        'usdt': 'Tether'
                    };
                }
            }
        }
    }
}
</script>
@endsection
