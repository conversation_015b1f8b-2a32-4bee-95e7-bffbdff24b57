@extends('layouts.frontend')

@section('title', 'Products - ' . $settings['site_name'])
@section('description', 'Browse our collection of premium digital products including themes, templates, plugins, and more.')

@section('content')
<div x-data="productSearch()" x-init="initFilters()">
    <!-- <PERSON> Header -->
    <section class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                        @if(request('category'))
                            {{ $category->name }} Products
                        @elseif(request('q'))
                            Search Results for "{{ request('q') }}"
                        @else
                            All Products
                        @endif
                    </h1>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">
                        @if(request('category'))
                            {{ $category->description }}
                        @elseif(request('q'))
                            Found {{ $products->total() }} {{ Str::plural('result', $products->total()) }}
                        @else
                            Discover premium digital assets for your projects
                        @endif
                    </p>
                </div>
                
                <div class="mt-4 md:mt-0 flex items-center space-x-4">
                    <span class="text-sm text-gray-500 dark:text-gray-400">
                        {{ $products->total() }} {{ Str::plural('product', $products->total()) }}
                    </span>
                    <!-- Results per page -->
                    <select name="per_page" onchange="updatePerPage(this.value)" class="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                        <option value="12" {{ request('per_page', 12) == 12 ? 'selected' : '' }}>12 per page</option>
                        <option value="24" {{ request('per_page') == 24 ? 'selected' : '' }}>24 per page</option>
                        <option value="48" {{ request('per_page') == 48 ? 'selected' : '' }}>48 per page</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Advanced Search and Filters -->
    <section class="bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <form method="GET" class="space-y-4">
                <!-- Main Search Bar -->
                <div class="flex flex-col lg:flex-row gap-4">
                    <!-- Search Input with Autocomplete -->
                    <div class="flex-1 relative">
                        <input
                            type="text"
                            name="q"
                            value="{{ request('q') }}"
                            placeholder="Search products, categories, or tags..."
                            class="w-full px-4 py-3 pl-10 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            x-model="searchQuery"
                            @input="getSuggestions()"
                            @focus="showSuggestions = suggestions.length > 0"
                            @blur="hideSuggestions()"
                            @keydown="handleKeydown($event)"
                            autocomplete="off"
                        >
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>

                        <!-- Clear Search Button -->
                        <div x-show="searchQuery.length > 0" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button type="button" @click="clearSearch()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>

                        <!-- Enhanced Search Suggestions -->
                        <div x-show="showSuggestions && suggestions.length > 0"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform scale-100"
                             x-transition:leave-end="opacity-0 transform scale-95"
                             class="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-96 overflow-y-auto">
                            <template x-for="(suggestion, index) in suggestions" :key="index">
                                <div class="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0 transition-colors"
                                     :class="{ 'bg-blue-50 dark:bg-blue-900': selectedSuggestion === index }"
                                     @click="selectSuggestion(suggestion)"
                                     @mouseenter="selectedSuggestion = index">
                                    <div class="flex items-start space-x-3">
                                        <div class="flex-shrink-0 mt-1">
                                            <i :class="suggestion.icon" class="text-gray-400 w-4 h-4"></i>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white truncate" x-text="suggestion.title"></div>
                                            <div x-show="suggestion.subtitle" class="text-xs text-blue-600 dark:text-blue-400 mt-1" x-text="suggestion.subtitle"></div>
                                            <div x-show="suggestion.description" class="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-2" x-text="suggestion.description"></div>
                                        </div>
                                        <div x-show="suggestion.price" class="flex-shrink-0 text-sm font-medium text-gray-900 dark:text-white" x-text="suggestion.price"></div>
                                    </div>
                                </div>
                            </template>

                            <!-- Search Tips -->
                            <div class="px-4 py-2 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    <span class="font-medium">Tips:</span> Use ↑↓ to navigate, Enter to select, Esc to close
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Filters -->
                    <div class="flex gap-2">
                        <button type="button" 
                                @click="toggleQuickFilter('featured')"
                                :class="{ 'bg-blue-600 text-white': quickFilters.featured, 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300': !quickFilters.featured }"
                                class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg transition-colors">
                            Featured
                        </button>
                        <button type="button" 
                                @click="toggleQuickFilter('trending')"
                                :class="{ 'bg-blue-600 text-white': quickFilters.trending, 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300': !quickFilters.trending }"
                                class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg transition-colors">
                            Trending
                        </button>
                        <button type="button" 
                                @click="showAdvanced = !showAdvanced"
                                class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                            </svg>
                            Filters
                        </button>
                    </div>
                </div>

                <!-- Advanced Filters Panel -->
                <div x-show="showAdvanced" x-transition class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        <!-- Category Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category</label>
                            <select name="category" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <option value="">All Categories</option>
                                @foreach($categories as $cat)
                                    <option value="{{ $cat->slug }}" {{ request('category') == $cat->slug ? 'selected' : '' }}>
                                        {{ $cat->name }}{{ isset($cat->products_count) ? ' (' . $cat->products_count . ')' : '' }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Price Range Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Price Range</label>
                            <select name="price" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <option value="">All Prices</option>
                                <option value="free" {{ request('price') == 'free' ? 'selected' : '' }}>
                                    Free{{ isset($filterCounts['free']) ? ' (' . $filterCounts['free'] . ')' : '' }}
                                </option>
                                <option value="paid" {{ request('price') == 'paid' ? 'selected' : '' }}>
                                    Paid{{ isset($filterCounts['paid']) ? ' (' . $filterCounts['paid'] . ')' : '' }}
                                </option>
                                @if(isset($priceRange) && $priceRange && $priceRange->min_price && $priceRange->max_price)
                                    <option value="0-10" {{ request('price') == '0-10' ? 'selected' : '' }}>$0 - $10</option>
                                    <option value="10-25" {{ request('price') == '10-25' ? 'selected' : '' }}>$10 - $25</option>
                                    <option value="25-50" {{ request('price') == '25-50' ? 'selected' : '' }}>$25 - $50</option>
                                    <option value="50-100" {{ request('price') == '50-100' ? 'selected' : '' }}>$50 - $100</option>
                                    <option value="100-999999" {{ request('price') == '100-999999' ? 'selected' : '' }}>$100+</option>
                                @endif
                            </select>
                        </div>

                        <!-- Custom Price Range -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Custom Price Range</label>
                            <div class="flex gap-2">
                                <input type="number" name="min_price" value="{{ request('min_price') }}" placeholder="Min" 
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <input type="number" name="max_price" value="{{ request('max_price') }}" placeholder="Max" 
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                            </div>
                        </div>

                        <!-- Sort Options -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sort By</label>
                            <select name="sort" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <option value="latest" {{ request('sort') == 'latest' ? 'selected' : '' }}>Latest</option>
                                <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>Oldest</option>
                                <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>Most Popular</option>
                                <option value="downloads" {{ request('sort') == 'downloads' ? 'selected' : '' }}>Most Downloaded</option>
                                <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                                <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                                <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Name: A to Z</option>
                            </select>
                        </div>

                        <!-- Date Range Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date Range</label>
                            <select name="date_range" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <option value="">All Time</option>
                                <option value="today" {{ request('date_range') == 'today' ? 'selected' : '' }}>Today</option>
                                <option value="week" {{ request('date_range') == 'week' ? 'selected' : '' }}>This Week</option>
                                <option value="month" {{ request('date_range') == 'month' ? 'selected' : '' }}>This Month</option>
                                <option value="year" {{ request('date_range') == 'year' ? 'selected' : '' }}>This Year</option>
                            </select>
                        </div>

                        <!-- Custom Date Range -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Custom Date Range</label>
                            <div class="flex gap-2">
                                <input type="date" name="date_from" value="{{ request('date_from') }}"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <input type="date" name="date_to" value="{{ request('date_to') }}"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                            </div>
                        </div>

                        <!-- Results Per Page -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Results Per Page</label>
                            <select name="per_page" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <option value="12" {{ request('per_page', 12) == 12 ? 'selected' : '' }}>12 per page</option>
                                <option value="24" {{ request('per_page') == 24 ? 'selected' : '' }}>24 per page</option>
                                <option value="48" {{ request('per_page') == 48 ? 'selected' : '' }}>48 per page</option>
                                <option value="96" {{ request('per_page') == 96 ? 'selected' : '' }}>96 per page</option>
                            </select>
                        </div>
                    </div>

                    <!-- Filter Actions -->
                    <div class="mt-6 flex justify-between">
                        <a href="{{ route('products.index') }}" class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">
                            Clear All Filters
                        </a>
                        <div class="flex gap-2">
                            <button type="button" @click="showAdvanced = false" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                                Cancel
                            </button>
                            <button type="submit" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Hidden inputs for quick filters -->
                <input type="hidden" name="featured" x-model="quickFilters.featured ? '1' : ''">
                <input type="hidden" name="trending" x-model="quickFilters.trending ? '1' : ''">
                <input type="hidden" name="per_page" value="{{ request('per_page', 12) }}">
            </form>
        </div>
    </section>

    <!-- Active Filters Display -->
    @if(request()->hasAny(['q', 'category', 'price', 'sort', 'featured', 'trending', 'min_price', 'max_price']))
    <section class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex flex-wrap items-center gap-2">
                <span class="text-sm text-gray-600 dark:text-gray-400">Active filters:</span>
                
                @if(request('q'))
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        Search: "{{ request('q') }}"
                        <a href="{{ request()->fullUrlWithQuery(['q' => null]) }}" class="ml-2 text-blue-600 hover:text-blue-800">×</a>
                    </span>
                @endif
                
                @if(request('category'))
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Category: {{ $category->name ?? request('category') }}
                        <a href="{{ request()->fullUrlWithQuery(['category' => null]) }}" class="ml-2 text-green-600 hover:text-green-800">×</a>
                    </span>
                @endif
                
                @if(request('price'))
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                        Price: {{ ucfirst(request('price')) }}
                        <a href="{{ request()->fullUrlWithQuery(['price' => null]) }}" class="ml-2 text-purple-600 hover:text-purple-800">×</a>
                    </span>
                @endif
                
                @if(request('featured'))
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                        Featured
                        <a href="{{ request()->fullUrlWithQuery(['featured' => null]) }}" class="ml-2 text-yellow-600 hover:text-yellow-800">×</a>
                    </span>
                @endif
                
                @if(request('trending'))
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                        Trending
                        <a href="{{ request()->fullUrlWithQuery(['trending' => null]) }}" class="ml-2 text-red-600 hover:text-red-800">×</a>
                    </span>
                @endif
                
                <a href="{{ route('products.index') }}" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    Clear all
                </a>
            </div>
        </div>
    </section>
    @endif

    <!-- Products Grid -->
    <section class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($products->count() > 0)
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
                    @foreach($products as $product)
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                        <!-- Product Image -->
                        <div class="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 relative overflow-hidden">
                            @if($product->primaryImage)
                                <img src="{{ Storage::url($product->primaryImage->image_path) }}" alt="{{ $product->title }}" class="w-full h-full object-cover">
                            @else
                                <div class="w-full h-full flex items-center justify-center">
                                    <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            @endif

                            <!-- Product Badges -->
                            <div class="absolute top-3 left-3 flex flex-col gap-1">
                                @if($product->is_featured)
                                    <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">Featured</span>
                                @endif
                                @if($product->is_trending)
                                    <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">Trending</span>
                                @endif
                                @if($product->is_free)
                                    <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">Free</span>
                                @endif
                            </div>
                        </div>

                        <!-- Product Info -->
                        <div class="p-4 sm:p-6">
                            <div class="mb-3">
                                <h3 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white line-clamp-2 leading-tight">
                                    <a href="{{ route('products.show', $product->slug) }}" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                        {{ $product->title }}
                                    </a>
                                </h3>
                            </div>



                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-2 sm:space-y-0">
                                <span class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 font-medium">
                                    {{ $product->category->name }}
                                </span>
                                <div class="flex items-center text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                                    <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    {{ number_format($product->view_count) }}
                                </div>
                            </div>

                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                                <div class="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">
                                    @if($product->is_free)
                                        <span class="text-green-600">Free</span>
                                    @else
                                        {{ $settings['currency_symbol'] }}{{ number_format($product->price, 2) }}
                                    @endif
                                </div>

                                <a href="{{ route('products.show', $product->slug) }}"
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 sm:px-4 sm:py-2 rounded-lg text-sm font-medium transition-colors text-center">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12">
                    {{ $products->appends(request()->query())->links() }}
                </div>
            @else
                <!-- No Products Found -->
                <div class="text-center py-16">
                    <svg class="w-24 h-24 text-gray-400 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0m6 0V5a2 2 0 00-2-2H9a2 2 0 00-2 2v1.306m8 0V7a2 2 0 012 2v6a2 2 0 01-2 2H7a2 2 0 01-2-2V9a2 2 0 012-2h8a2 2 0 012 2z"></path>
                    </svg>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                        No products found
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        @if(request()->hasAny(['q', 'category', 'price', 'sort', 'featured', 'trending']))
                            Try adjusting your search criteria or filters.
                        @else
                            No products are available at the moment.
                        @endif
                    </p>
                    @if(request()->hasAny(['q', 'category', 'price', 'sort', 'featured', 'trending']))
                        <a href="{{ route('products.index') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            Clear Filters
                        </a>
                    @endif
                </div>
            @endif
        </div>
    </section>
</div>

<script>
function productSearch() {
    return {
        searchQuery: '{{ request('q') }}',
        showSuggestions: false,
        suggestions: [],
        showAdvanced: false,
        selectedSuggestion: -1,
        searchTimeout: null,
        quickFilters: {
            featured: {{ request('featured') ? 'true' : 'false' }},
            trending: {{ request('trending') ? 'true' : 'false' }}
        },

        initFilters() {
            // Initialize filters from URL parameters
            this.showAdvanced = {{ request()->hasAny(['min_price', 'max_price', 'date_from', 'date_to']) ? 'true' : 'false' }};
        },

        async getSuggestions() {
            if (this.searchQuery.length < 2) {
                this.suggestions = [];
                this.showSuggestions = false;
                return;
            }

            // Debounce search requests
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(async () => {
                try {
                    const response = await fetch(`{{ route('api.products.suggestions') }}?q=${encodeURIComponent(this.searchQuery)}`);
                    this.suggestions = await response.json();
                    this.showSuggestions = this.suggestions.length > 0;
                    this.selectedSuggestion = -1;
                } catch (error) {
                    console.error('Error fetching suggestions:', error);
                    this.suggestions = [];
                    this.showSuggestions = false;
                }
            }, 300);
        },

        selectSuggestion(suggestion) {
            if (suggestion.type === 'product' || suggestion.type === 'category') {
                window.location.href = suggestion.url;
            } else {
                this.searchQuery = suggestion.title;
                this.showSuggestions = false;
                this.submitSearch();
            }
        },

        handleKeydown(event) {
            if (!this.showSuggestions || this.suggestions.length === 0) return;

            switch (event.key) {
                case 'ArrowDown':
                    event.preventDefault();
                    this.selectedSuggestion = Math.min(this.selectedSuggestion + 1, this.suggestions.length - 1);
                    break;
                case 'ArrowUp':
                    event.preventDefault();
                    this.selectedSuggestion = Math.max(this.selectedSuggestion - 1, -1);
                    break;
                case 'Enter':
                    event.preventDefault();
                    if (this.selectedSuggestion >= 0) {
                        this.selectSuggestion(this.suggestions[this.selectedSuggestion]);
                    } else {
                        this.submitSearch();
                    }
                    break;
                case 'Escape':
                    this.showSuggestions = false;
                    this.selectedSuggestion = -1;
                    break;
            }
        },

        submitSearch() {
            if (this.searchQuery.trim()) {
                const form = document.querySelector('form[method="GET"]');
                if (form) {
                    form.submit();
                }
            }
        },

        hideSuggestions() {
            // Delay hiding to allow click events on suggestions
            setTimeout(() => {
                this.showSuggestions = false;
                this.selectedSuggestion = -1;
            }, 200);
        },

        toggleQuickFilter(filter) {
            this.quickFilters[filter] = !this.quickFilters[filter];
            // Auto-submit form when quick filter is toggled
            setTimeout(() => {
                const form = document.querySelector('form[method="GET"]');
                if (form) {
                    // Update hidden inputs for quick filters
                    const featuredInput = form.querySelector('input[name="featured"]');
                    const trendingInput = form.querySelector('input[name="trending"]');

                    if (featuredInput) featuredInput.value = this.quickFilters.featured ? '1' : '';
                    if (trendingInput) trendingInput.value = this.quickFilters.trending ? '1' : '';

                    form.submit();
                }
            }, 100);
        },

        clearSearch() {
            this.searchQuery = '';
            this.suggestions = [];
            this.showSuggestions = false;
            // Redirect to products page without search
            window.location.href = '{{ route('products.index') }}';
        }
    }
}

function updatePerPage(value) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', value);
    window.location.href = url.toString();
}
</script>
@endsection
