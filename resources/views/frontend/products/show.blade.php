@extends('layouts.frontend')

@section('title', $product->title . ' - ' . config('templatescave.site.name'))
@section('description', $product->short_description)

@section('og_title', $product->title)
@section('og_description', $product->short_description)
@section('og_type', 'product')
@section('og_image', $product->primaryImage ? $product->primaryImage->image_path : asset('images/og-default.jpg'))

@section('content')
<div x-data="productDetail">
    <!-- Breadcrumb -->
    <nav class="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <ol class="flex items-center space-x-2 text-sm">
                <li><a href="{{ route('home') }}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">Home</a></li>
                <li><span class="text-gray-400">/</span></li>
                <li><a href="{{ route('products.index') }}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">Products</a></li>
                <li><span class="text-gray-400">/</span></li>
                <li><a href="{{ route('categories.show', $product->category->slug) }}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">{{ $product->category->name }}</a></li>
                <li><span class="text-gray-400">/</span></li>
                <li><span class="text-gray-900 dark:text-white">{{ $product->title }}</span></li>
            </ol>
        </div>
    </nav>

    <!-- Product Details -->
    <section class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Product Images -->
                <div>
                    <!-- Main Image -->
                    <div class="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-xl overflow-hidden mb-4">
                        @if($product->primaryImage)
                            <img 
                                x-ref="mainImage"
                                src="{{ $product->primaryImage->image_path }}" 
                                alt="{{ $product->title }}" 
                                class="w-full h-full object-cover"
                            >
                        @else
                            <div class="w-full h-full flex items-center justify-center">
                                <svg class="w-24 h-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        @endif
                    </div>

                    <!-- Thumbnail Images -->
                    @if($product->images->count() > 1)
                    <div class="grid grid-cols-4 gap-2">
                        @foreach($product->images as $image)
                        <button 
                            @click="changeMainImage('{{ $image->image_path }}')"
                            class="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden hover:ring-2 hover:ring-blue-500 transition-all"
                        >
                            <img src="{{ $image->image_path }}" alt="{{ $product->title }}" class="w-full h-full object-cover">
                        </button>
                        @endforeach
                    </div>
                    @endif
                </div>

                <!-- Product Info -->
                <div>
                    <!-- Category and Status -->
                    <div class="flex items-center gap-3 mb-4">
                        <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">
                            {{ $product->category->name }}
                        </span>
                        @if($product->is_featured)
                            <span class="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm font-medium">
                                Featured
                            </span>
                        @endif
                        @if($product->created_at->isToday())
                            <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">
                                New
                            </span>
                        @endif
                    </div>

                    <!-- Title -->
                    <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        {{ $product->title }}
                    </h1>

                    <!-- Short Description -->
                    <p class="text-lg text-gray-600 dark:text-gray-400 mb-6">
                        {{ $product->short_description }}
                    </p>

                    <!-- Stats -->
                    <div class="flex items-center gap-6 mb-6 text-sm text-gray-500 dark:text-gray-400">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            {{ number_format($product->view_count) }} views
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            {{ number_format($product->download_count) }} downloads
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {{ $product->created_at->format('M j, Y') }}
                        </div>
                    </div>

                    <!-- Price and Download -->
                    <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-6 mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                @if($product->is_free)
                                    <span class="text-3xl font-bold text-green-600 dark:text-green-400">Free</span>
                                @else
                                    <span class="text-3xl font-bold text-gray-900 dark:text-white">${{ number_format($product->price, 2) }}</span>
                                @endif
                            </div>
                        </div>

                        @auth
                            @if($product->is_free)
                                <button 
                                    @click="downloadProduct"
                                    class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors"
                                >
                                    Download Free
                                </button>
                            @else
                                <button 
                                    @click="purchaseProduct"
                                    class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors"
                                >
                                    Purchase & Download
                                </button>
                            @endif
                        @else
                            <div class="text-center">
                                <p class="text-gray-600 dark:text-gray-400 mb-4">
                                    Please login to download this product
                                </p>
                                <div class="flex gap-3">
                                    <a href="{{ route('login') }}" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold text-center transition-colors">
                                        Login
                                    </a>
                                    <a href="{{ route('register') }}" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-3 px-6 rounded-lg font-semibold text-center transition-colors">
                                        Register
                                    </a>
                                </div>
                            </div>
                        @endauth
                    </div>

                    <!-- Product Details -->
                    <div class="space-y-4">
                        @if($product->demo_url)
                        <div>
                            <a href="{{ $product->demo_url }}" target="_blank" class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                View Live Demo
                            </a>
                        </div>
                        @endif

                        @if($product->tags)
                        <div>
                            <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">Tags</h3>
                            <div class="flex flex-wrap gap-2">
                                @foreach(explode(',', $product->tags) as $tag)
                                <span class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm">
                                    {{ trim($tag) }}
                                </span>
                                @endforeach
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Product Description -->
            @if($product->description)
            <div class="mt-16">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Description</h2>
                <div class="prose prose-lg dark:prose-invert max-w-none">
                    {!! nl2br(e($product->description)) !!}
                </div>
            </div>
            @endif

            <!-- Related Products -->
            @if($relatedProducts->count() > 0)
            <div class="mt-16">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Related Products</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach($relatedProducts as $relatedProduct)
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 relative overflow-hidden">
                            @if($relatedProduct->primaryImage)
                                <img src="{{ $relatedProduct->primaryImage->image_path }}" alt="{{ $relatedProduct->title }}" class="w-full h-full object-cover">
                            @else
                                <div class="w-full h-full flex items-center justify-center">
                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            <div class="absolute top-3 right-3">
                                @if($relatedProduct->is_free)
                                    <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">Free</span>
                                @else
                                    <span class="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-semibold">${{ number_format($relatedProduct->price, 2) }}</span>
                                @endif
                            </div>
                        </div>

                        <div class="p-4">
                            <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                                {{ $relatedProduct->title }}
                            </h3>
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-blue-600 dark:text-blue-400">{{ $relatedProduct->category->name }}</span>
                                <a href="{{ route('products.show', $relatedProduct->slug) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors">
                                    View
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </section>
</div>

@push('scripts')
<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('productDetail', () => ({
        changeMainImage(imagePath) {
            this.$refs.mainImage.src = imagePath;
        },
        
        downloadProduct() {
            // Handle free download
            window.location.href = '{{ route("products.download", $product->slug) }}';
        },
        
        purchaseProduct() {
            // Handle purchase flow
            window.location.href = '{{ route("products.purchase", $product->slug) }}';
        }
    }))
})
</script>
@endpush
@endsection
