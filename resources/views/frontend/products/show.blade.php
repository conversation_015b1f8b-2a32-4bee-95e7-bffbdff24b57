@extends('layouts.frontend')

@section('title', $product->title . ' - ' . $settings['site_name'])
@section('description', $product->short_description)

@section('og_title', $product->title)
@section('og_description', $product->short_description)
@section('og_type', 'product')
@section('og_image', $product->primaryImage ? $product->primaryImage->image_path : asset('images/og-default.jpg'))

@section('content')
<div x-data="productDetail">
    <!-- Breadcrumb -->
    <nav class="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <ol class="flex items-center space-x-2 text-sm">
                <li><a href="{{ route('home') }}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">Home</a></li>
                <li><span class="text-gray-400">/</span></li>
                <li><a href="{{ route('products.index') }}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">Products</a></li>
                <li><span class="text-gray-400">/</span></li>
                <li><a href="{{ route('categories.show', $product->category->slug) }}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">{{ $product->category->name }}</a></li>
                <li><span class="text-gray-400">/</span></li>
                <li><span class="text-gray-900 dark:text-white">{{ $product->title }}</span></li>
            </ol>
        </div>
    </nav>

    <!-- Product Details -->
    <section class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Product Images -->
                <div>
                    <!-- Main Image -->
                    <div class="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-xl overflow-hidden mb-4">
                        @if($product->primaryImage)
                            <img 
                                x-ref="mainImage"
                                src="{{ $product->primaryImage->image_url }}"
                                alt="{{ $product->title }}" 
                                class="w-full h-full object-cover"
                            >
                        @else
                            <div class="w-full h-full flex items-center justify-center">
                                <svg class="w-24 h-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        @endif
                    </div>

                    <!-- Thumbnail Images -->
                    @if($product->images->count() > 1)
                    <div class="grid grid-cols-4 gap-2">
                        @foreach($product->images as $image)
                        <button
                            @click="changeMainImage('{{ $image->image_url }}')"
                            class="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden hover:ring-2 hover:ring-blue-500 transition-all"
                        >
                            <img src="{{ $image->image_url }}" alt="{{ $product->title }}" class="w-full h-full object-cover">
                        </button>
                        @endforeach
                    </div>
                    @endif
                </div>

                <!-- External URLs Section -->
                @if($product->file_urls && count($product->file_urls) > 0)
                <div class="mt-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">External URLs</h3>
                    <div class="flex flex-wrap gap-3">
                        @foreach($product->file_urls as $index => $fileUrl)
                        @if(!empty(trim($fileUrl)))
                        <a href="{{ $fileUrl }}" target="_blank"
                           class="group flex items-center gap-2 px-4 py-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 hover:shadow-md transition-all duration-200"
                           title="External Link {{ $index + 1 }}">
                            <div class="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors duration-200">
                                <i class="fas fa-external-link-alt text-sm"></i>
                            </div>
                            <div class="flex flex-col">
                                <span class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
                                    Link {{ $index + 1 }}
                                </span>
                                <span class="text-xs text-gray-500 dark:text-gray-400 truncate max-w-[200px]">
                                    {{ parse_url($fileUrl, PHP_URL_HOST) ?: 'External Resource' }}
                                </span>
                            </div>
                            <i class="fas fa-arrow-right text-xs text-gray-400 group-hover:text-blue-500 group-hover:translate-x-1 transition-all duration-200"></i>
                        </a>
                        @endif
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Product Info -->
                <div>
                    <!-- Category and Status -->
                    <div class="flex items-center gap-3 mb-4">
                        <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">
                            {{ $product->category->name }}
                        </span>
                        @if($product->is_featured)
                            <span class="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm font-medium">
                                Featured
                            </span>
                        @endif
                        @if($product->created_at->isToday())
                            <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">
                                New
                            </span>
                        @endif
                    </div>

                    <!-- Title -->
                    <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        {{ $product->title }}
                    </h1>

                    <!-- Short Description -->
                    <p class="text-lg text-gray-600 dark:text-gray-400 mb-6">
                        {{ $product->short_description }}
                    </p>

                    <!-- Stats -->
                    <div class="flex items-center gap-6 mb-6 text-sm text-gray-500 dark:text-gray-400">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            {{ number_format($product->view_count) }} views
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            {{ number_format($product->download_count) }} downloads
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {{ $product->created_at->format('M j, Y') }}
                        </div>
                    </div>

                    <!-- Price and Download -->
                    <div class="bg-gray-50 dark:bg-gray-800 rounded-xl mb-6">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                            <div>
                                @if($product->is_free)
                                    <span class="text-3xl font-bold text-green-600 dark:text-green-400">Free</span>
                                @else
                                    <span class="text-3xl font-bold text-gray-900 dark:text-white">{{ $settings['currency_symbol'] }}{{ number_format($product->price, 2) }}</span>
                                @endif
                            </div>
                        </div>

                        @auth
                            @if($product->is_free)
                                <button 
                                    @click="downloadProduct"
                                    class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors"
                                >
                                    Download Free
                                </button>
                            @else
                                <button 
                                    @click="purchaseProduct"
                                    class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors"
                                >
                                    Purchase & Download
                                </button>
                            @endif
                        @else
                            <div class="text-center">
                                <p class="text-gray-600 dark:text-gray-400 mb-4">
                                    Please login to download this product
                                </p>
                                <div class="flex gap-3">
                                    <a href="{{ route('login') }}" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold text-center transition-colors">
                                        Login
                                    </a>
                                    <a href="{{ route('register') }}" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-3 px-6 rounded-lg font-semibold text-center transition-colors">
                                        Register
                                    </a>
                                </div>
                            </div>
                        @endauth
                        </div>
                    </div>

                    <!-- Product Details -->
                    <div class="space-y-4">
                        @if($product->demo_url)
                        <div class="mb-6">
                            <a href="{{ $product->demo_url }}" target="_blank"
                               class="w-full flex items-center justify-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group">
                                <svg class="w-5 h-5 mr-2 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <span class="mr-2">View Live Demo</span>
                                <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                            </a>
                        </div>
                        @endif

                        @if($product->tags && count($product->tags) > 0)
                        <div>
                            <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">Tags</h3>
                            <div class="flex flex-wrap gap-2">
                                @foreach($product->tags as $tag)
                                <span class="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm">
                                    {{ trim($tag) }}
                                </span>
                                @endforeach
                            </div>
                        </div>
                        @endif


                    </div>
                </div>
            </div>

            <!-- Product Description -->
            @if($product->description)
            <div class="mt-16">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Description</h2>
                <div class="prose prose-lg dark:prose-invert max-w-none">
                    {!! nl2br(e($product->description)) !!}
                </div>
            </div>
            @endif

            <!-- Reviews Section -->
            <div class="mt-16">
                <div class="flex items-center justify-between mb-8">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Customer Reviews</h2>
                        @if($product->review_count > 0)
                        <div class="flex items-center mt-2">
                            <div class="flex items-center">
                                {!! $product->rating_stars !!}
                            </div>
                            <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">
                                {{ number_format($product->average_rating, 1) }} out of 5 ({{ $product->review_count }} {{ Str::plural('review', $product->review_count) }})
                            </span>
                        </div>
                        @else
                        <p class="text-gray-600 dark:text-gray-400 mt-2">No reviews yet</p>
                        @endif
                    </div>

                    @auth
                        @if($canReview && !$hasReviewed)
                        <button @click="showReviewForm = true" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            Write a Review
                        </button>
                        @elseif($hasReviewed)
                        <span class="text-green-600 dark:text-green-400 text-sm">✓ You've reviewed this product</span>
                        @endif
                    @else
                    <a href="{{ route('login') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Login to Review
                    </a>
                    @endauth
                </div>

                <!-- Review Form -->
                @auth
                @if($canReview && !$hasReviewed)
                <div x-show="showReviewForm" x-cloak class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Write Your Review</h3>

                    <form action="{{ route('products.review.store', $product->slug) }}" method="POST">
                        @csrf

                        <!-- Rating -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rating</label>
                            <div class="flex items-center space-x-1" x-data="{ rating: 0 }">
                                @for($i = 1; $i <= 5; $i++)
                                <button type="button"
                                        @click="rating = {{ $i }}"
                                        @mouseover="rating = {{ $i }}"
                                        class="text-2xl transition-colors"
                                        :class="rating >= {{ $i }} ? 'text-yellow-400' : 'text-gray-300'">
                                    <i class="fas fa-star"></i>
                                </button>
                                @endfor
                                <input type="hidden" name="rating" :value="rating" required>
                            </div>
                            @error('rating')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Review Title -->
                        <div class="mb-4">
                            <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Review Title</label>
                            <input type="text"
                                   id="title"
                                   name="title"
                                   value="{{ old('title') }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                   placeholder="Summarize your experience"
                                   required>
                            @error('title')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Review Content -->
                        <div class="mb-4">
                            <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Your Review</label>
                            <textarea id="content"
                                      name="content"
                                      rows="4"
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                      placeholder="Share your thoughts about this product..."
                                      required>{{ old('content') }}</textarea>
                            @error('content')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex items-center space-x-3">
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                Submit Review
                            </button>
                            <button type="button" @click="showReviewForm = false" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
                @endif
                @endauth

                <!-- Reviews List -->
                @if($product->approvedReviews->count() > 0)
                <div class="space-y-6">
                    @foreach($product->approvedReviews as $review)
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                        <div class="flex items-start justify-between mb-3">
                            <div>
                                <div class="flex items-center space-x-2 mb-1">
                                    <h4 class="font-semibold text-gray-900 dark:text-white">{{ $review->title }}</h4>
                                    <div class="flex items-center">
                                        {!! $review->stars_html !!}
                                    </div>
                                </div>
                                <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                    <span>{{ $review->user->name }}</span>
                                    <span class="mx-2">•</span>
                                    <span>{{ $review->created_at->format('M j, Y') }}</span>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-700 dark:text-gray-300 leading-relaxed">{{ $review->content }}</p>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No reviews yet</h3>
                    <p class="text-gray-600 dark:text-gray-400">Be the first to review this product!</p>
                </div>
                @endif
            </div>

            <!-- Related Products -->
            @if($relatedProducts->count() > 0)
            <div class="mt-16">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Related Products</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach($relatedProducts as $relatedProduct)
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 relative overflow-hidden">
                            @if($relatedProduct->primaryImage)
                                <img src="{{ $relatedProduct->primaryImage->image_url }}" alt="{{ $relatedProduct->title }}" class="w-full h-full object-cover">
                            @else
                                <div class="w-full h-full flex items-center justify-center">
                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            <div class="absolute top-3 right-3">
                                @if($relatedProduct->is_free)
                                    <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">Free</span>
                                @else
                                    <span class="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-semibold">${{ number_format($relatedProduct->price, 2) }}</span>
                                @endif
                            </div>
                        </div>

                        <div class="p-4">
                            <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                                {{ $relatedProduct->title }}
                            </h3>
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-blue-600 dark:text-blue-400">{{ $relatedProduct->category->name }}</span>
                                <a href="{{ route('products.show', $relatedProduct->slug) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors">
                                    View
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </section>
</div>

@push('scripts')
<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('productDetail', () => ({
        showReviewForm: false,

        changeMainImage(imagePath) {
            this.$refs.mainImage.src = imagePath;
        },

        downloadProduct() {
            // Handle free download
            window.location.href = '{{ route("products.download", $product->slug) }}';
        },

        purchaseProduct() {
            // Handle purchase flow
            window.location.href = '{{ route("products.purchase", $product->slug) }}';
        }
    }))
})
</script>
@endpush
@endsection
