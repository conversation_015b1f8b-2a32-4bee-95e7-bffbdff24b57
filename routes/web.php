<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Frontend\HomeController;
use App\Http\Controllers\Frontend\ProductController;
use App\Http\Controllers\Frontend\CategoryController;

use Illuminate\Support\Facades\Route;

// Frontend Routes
Route::get('/', [HomeController::class, 'index'])->name('home');

// Products
Route::prefix('products')->name('products.')->group(function () {
    Route::get('/', [ProductController::class, 'index'])->name('index');
    Route::get('/search', [ProductController::class, 'search'])->name('search');
    Route::get('/{slug}', [ProductController::class, 'show'])->name('show');
    Route::get('/{slug}/download', [ProductController::class, 'download'])->name('download');
    Route::get('/{slug}/purchase', [ProductController::class, 'purchase'])->name('purchase');
});

// Payment routes
Route::prefix('payment')->name('payment.')->middleware('auth')->group(function () {
    Route::get('/{product:slug}', [\App\Http\Controllers\Frontend\PaymentController::class, 'show'])->name('show');
    Route::post('/{product:slug}/create-intent', [\App\Http\Controllers\Frontend\PaymentController::class, 'createPaymentIntent'])->name('create-intent');
    Route::post('/confirm', [\App\Http\Controllers\Frontend\PaymentController::class, 'confirmPayment'])->name('confirm');
    Route::get('/success/{order}', [\App\Http\Controllers\Frontend\PaymentController::class, 'success'])->name('success');
    Route::get('/failed', [\App\Http\Controllers\Frontend\PaymentController::class, 'failed'])->name('failed');
    Route::get('/bank-transfer/{order}', [\App\Http\Controllers\Frontend\PaymentController::class, 'bankTransferConfirmation'])->name('bank-transfer');
});

// Webhook routes (no auth required)
Route::post('/webhooks/stripe', [\App\Http\Controllers\Frontend\PaymentController::class, 'stripeWebhook'])->name('webhooks.stripe');

// Dashboard routes
Route::get('/dashboard', [\App\Http\Controllers\Frontend\DashboardController::class, 'index'])->middleware('auth')->name('dashboard');
Route::prefix('dashboard')->name('dashboard.')->middleware('auth')->group(function () {
    Route::get('/orders', [\App\Http\Controllers\Frontend\DashboardController::class, 'orders'])->name('orders');
    Route::get('/orders/{order}', [\App\Http\Controllers\Frontend\DashboardController::class, 'orderDetails'])->name('order-details');
    Route::get('/downloads', [\App\Http\Controllers\Frontend\DashboardController::class, 'downloads'])->name('downloads');
    Route::get('/profile', [\App\Http\Controllers\Frontend\DashboardController::class, 'profile'])->name('profile');
    Route::patch('/profile', [\App\Http\Controllers\Frontend\DashboardController::class, 'updateProfile'])->name('profile.update');
    Route::get('/settings', [\App\Http\Controllers\Frontend\DashboardController::class, 'settings'])->name('settings');
    Route::patch('/password', [\App\Http\Controllers\Frontend\DashboardController::class, 'updatePassword'])->name('password.update');
    Route::get('/analytics', [\App\Http\Controllers\Frontend\DashboardController::class, 'analytics'])->name('analytics');
});

// API routes for search functionality
Route::prefix('api/products')->name('api.products.')->group(function () {
    Route::get('/suggestions', [ProductController::class, 'suggestions'])->name('suggestions');
    Route::get('/filter-options', [ProductController::class, 'filterOptions'])->name('filter-options');
});

// Categories
Route::prefix('categories')->name('categories.')->group(function () {
    Route::get('/', [CategoryController::class, 'index'])->name('index');
    Route::get('/{slug}', [CategoryController::class, 'show'])->name('show');
});

// Blog routes
Route::prefix('blog')->name('blog.')->group(function () {
    Route::get('/', [\App\Http\Controllers\Frontend\BlogController::class, 'index'])->name('index');
    Route::get('/search', [\App\Http\Controllers\Frontend\BlogController::class, 'search'])->name('search');
    Route::get('/category/{slug}', [\App\Http\Controllers\Frontend\BlogController::class, 'category'])->name('category');
    Route::get('/tag/{tag}', [\App\Http\Controllers\Frontend\BlogController::class, 'tag'])->name('tag');
    Route::get('/{slug}', [\App\Http\Controllers\Frontend\BlogController::class, 'show'])->name('show');
    Route::post('/{slug}/comments', [\App\Http\Controllers\Frontend\BlogController::class, 'storeComment'])->name('comments.store');
});

// SEO routes
Route::get('/sitemap.xml', function () {
    $seoService = app(\App\Services\SEOService::class);
    return response($seoService->generateSitemap())
        ->header('Content-Type', 'application/xml');
})->name('sitemap');

Route::get('/robots.txt', function () {
    $seoService = app(\App\Services\SEOService::class);
    return response($seoService->generateRobotsTxt())
        ->header('Content-Type', 'text/plain');
})->name('robots');

// Member Dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified', 'check.role:member'])
    ->name('dashboard');

// Admin Routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'verified', 'role:admin'])->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

    // User management
    Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
    Route::patch('users/{user}/toggle-status', [\App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('users.toggle-status');

    // Product management
    Route::resource('products', \App\Http\Controllers\Admin\ProductController::class);
    Route::patch('products/{product}/approve', [\App\Http\Controllers\Admin\ProductController::class, 'approve'])->name('products.approve');
    Route::patch('products/{product}/reject', [\App\Http\Controllers\Admin\ProductController::class, 'reject'])->name('products.reject');
    Route::patch('products/{product}/toggle-featured', [\App\Http\Controllers\Admin\ProductController::class, 'toggleFeatured'])->name('products.toggle-featured');
    Route::delete('product-images/{image}', [\App\Http\Controllers\Admin\ProductController::class, 'deleteImage'])->name('products.delete-image');
    Route::patch('products/{product}/update-image-order', [\App\Http\Controllers\Admin\ProductController::class, 'updateImageOrder'])->name('products.update-image-order');

    // Category management
    Route::resource('categories', \App\Http\Controllers\Admin\CategoryController::class);
    Route::patch('categories/{category}/toggle-status', [\App\Http\Controllers\Admin\CategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
    Route::post('categories/update-order', [\App\Http\Controllers\Admin\CategoryController::class, 'updateOrder'])->name('categories.update-order');

    // Order management
    Route::prefix('orders')->name('orders.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\OrderController::class, 'index'])->name('index');
        Route::get('/{order}', [\App\Http\Controllers\Admin\OrderController::class, 'show'])->name('show');
        Route::patch('/{order}/status', [\App\Http\Controllers\Admin\OrderController::class, 'updateStatus'])->name('update-status');
        Route::post('/{order}/confirm-bank-transfer', [\App\Http\Controllers\Admin\OrderController::class, 'confirmBankTransfer'])->name('confirm-bank-transfer');
        Route::get('/export/csv', [\App\Http\Controllers\Admin\OrderController::class, 'export'])->name('export');
        Route::get('/analytics/data', [\App\Http\Controllers\Admin\OrderController::class, 'analytics'])->name('analytics');
    });

    // Blog management
    Route::prefix('blog')->name('blog.')->group(function () {
        // Blog posts
        Route::resource('posts', \App\Http\Controllers\Admin\BlogPostController::class);
        Route::post('posts/bulk-action', [\App\Http\Controllers\Admin\BlogPostController::class, 'bulkAction'])->name('posts.bulk-action');

        // Blog categories
        Route::resource('categories', \App\Http\Controllers\Admin\BlogCategoryController::class);
        Route::post('categories/update-order', [\App\Http\Controllers\Admin\BlogCategoryController::class, 'updateOrder'])->name('categories.update-order');
        Route::patch('categories/{category}/toggle-status', [\App\Http\Controllers\Admin\BlogCategoryController::class, 'toggleStatus'])->name('categories.toggle-status');

        // Blog comments
        Route::prefix('comments')->name('comments.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\BlogCommentController::class, 'index'])->name('index');
            Route::patch('/{comment}/approve', [\App\Http\Controllers\Admin\BlogCommentController::class, 'approve'])->name('approve');
            Route::patch('/{comment}/reject', [\App\Http\Controllers\Admin\BlogCommentController::class, 'reject'])->name('reject');
            Route::patch('/{comment}/spam', [\App\Http\Controllers\Admin\BlogCommentController::class, 'markAsSpam'])->name('spam');
            Route::delete('/{comment}', [\App\Http\Controllers\Admin\BlogCommentController::class, 'destroy'])->name('destroy');
            Route::post('/bulk-action', [\App\Http\Controllers\Admin\BlogCommentController::class, 'bulkAction'])->name('bulk-action');
        });
    });

    // SEO & Performance
    Route::prefix('seo')->name('seo.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\SEOController::class, 'index'])->name('index');
        Route::get('/performance', [\App\Http\Controllers\Admin\SEOController::class, 'performance'])->name('performance');
        Route::get('/analytics', [\App\Http\Controllers\Admin\SEOController::class, 'analytics'])->name('analytics');
        Route::get('/tools', [\App\Http\Controllers\Admin\SEOController::class, 'tools'])->name('tools');

        // AJAX routes
        Route::post('/generate-sitemap', [\App\Http\Controllers\Admin\SEOController::class, 'generateSitemap'])->name('generate-sitemap');
        Route::post('/clear-caches', [\App\Http\Controllers\Admin\SEOController::class, 'clearCaches'])->name('clear-caches');
        Route::post('/test-page-speed', [\App\Http\Controllers\Admin\SEOController::class, 'testPageSpeed'])->name('test-page-speed');
        Route::post('/audit', [\App\Http\Controllers\Admin\SEOController::class, 'audit'])->name('audit');
    });
});


Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
