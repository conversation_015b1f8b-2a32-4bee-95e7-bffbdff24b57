<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Frontend\HomeController;
use App\Http\Controllers\Frontend\ProductController;
use App\Http\Controllers\Frontend\CategoryController;
use App\Http\Controllers\Frontend\NewsletterController;

use Illuminate\Support\Facades\Route;

// Frontend Routes
Route::get('/', [HomeController::class, 'index'])->name('home');

// Products
Route::prefix('products')->name('products.')->group(function () {
    Route::get('/', [ProductController::class, 'index'])->name('index');
    Route::get('/search', [ProductController::class, 'search'])->name('search');
    Route::get('/{slug}', [ProductController::class, 'show'])->name('show');
    Route::get('/{slug}/download', [ProductController::class, 'download'])->name('download');
    Route::get('/{slug}/purchase', [ProductController::class, 'purchase'])->name('purchase');
    Route::post('/{slug}/review', [ProductController::class, 'storeReview'])->name('review.store')->middleware('auth');
});

// Payment routes
Route::prefix('payment')->name('payment.')->middleware('auth')->group(function () {
    Route::get('/{product:slug}', [\App\Http\Controllers\Frontend\PaymentController::class, 'show'])->name('show');
    Route::post('/{product:slug}/create-intent', [\App\Http\Controllers\Frontend\PaymentController::class, 'createPaymentIntent'])->name('create-intent');
    Route::post('/confirm', [\App\Http\Controllers\Frontend\PaymentController::class, 'confirmPayment'])->name('confirm');
    Route::get('/success/{order}', [\App\Http\Controllers\Frontend\PaymentController::class, 'success'])->name('success');
    Route::get('/failed', [\App\Http\Controllers\Frontend\PaymentController::class, 'failed'])->name('failed');
    Route::get('/bank-transfer/{order}', [\App\Http\Controllers\Frontend\PaymentController::class, 'bankTransferConfirmation'])->name('bank-transfer');
    Route::get('/paypal/success/{order}', [\App\Http\Controllers\Frontend\PaymentController::class, 'paypalSuccess'])->name('paypal.success');
    Route::get('/paypal/cancel/{order}', [\App\Http\Controllers\Frontend\PaymentController::class, 'paypalCancel'])->name('paypal.cancel');

    // Crypto payment routes
    Route::post('/crypto/create/{product:slug}', [\App\Http\Controllers\Frontend\CryptoPaymentController::class, 'create'])->name('crypto.create');
    Route::get('/crypto/{order}', [\App\Http\Controllers\Frontend\CryptoPaymentController::class, 'show'])->name('crypto.show');
    Route::get('/crypto/{order}/status', [\App\Http\Controllers\Frontend\CryptoPaymentController::class, 'checkStatus'])->name('crypto.status');
    Route::get('/crypto/{order}/success', [\App\Http\Controllers\Frontend\CryptoPaymentController::class, 'success'])->name('crypto.success');
    Route::get('/crypto/{order}/cancel', [\App\Http\Controllers\Frontend\CryptoPaymentController::class, 'cancel'])->name('crypto.cancel');
    Route::get('/crypto/currencies', [\App\Http\Controllers\Frontend\CryptoPaymentController::class, 'getCurrencies'])->name('crypto.currencies');
});

// Newsletter routes
Route::post('/newsletter/subscribe', [NewsletterController::class, 'subscribe'])->name('newsletter.subscribe');
Route::get('/newsletter/unsubscribe', [NewsletterController::class, 'unsubscribe'])->name('newsletter.unsubscribe');

// Webhook routes (no auth required)
Route::post('/webhooks/stripe', [\App\Http\Controllers\Frontend\PaymentController::class, 'stripeWebhook'])->name('webhooks.stripe');
Route::post('/payment/crypto/callback', [\App\Http\Controllers\Frontend\CryptoPaymentController::class, 'callback'])->name('payment.crypto.callback');

// Dashboard routes - Role-based dashboard access
Route::get('/dashboard', function () {
    $user = auth()->user();

    if (!$user) {
        return redirect()->route('login');
    }

    // Redirect admin users to admin dashboard
    if ($user->role === 'admin') {
        return redirect()->route('admin.dashboard');
    }

    // Members access the frontend dashboard
    return app(\App\Http\Controllers\Frontend\DashboardController::class)->index();
})->middleware('auth')->name('dashboard');

Route::prefix('dashboard')->name('dashboard.')->middleware(['auth', 'check.role:member'])->group(function () {
    Route::get('/orders', [\App\Http\Controllers\Frontend\DashboardController::class, 'orders'])->name('orders');
    Route::get('/orders/{order}', [\App\Http\Controllers\Frontend\DashboardController::class, 'orderDetails'])->name('order-details');
    Route::get('/downloads', [\App\Http\Controllers\Frontend\DashboardController::class, 'downloads'])->name('downloads');
    Route::get('/profile', [\App\Http\Controllers\Frontend\DashboardController::class, 'profile'])->name('profile');
    Route::patch('/profile', [\App\Http\Controllers\Frontend\DashboardController::class, 'updateProfile'])->name('profile.update');
    Route::get('/settings', [\App\Http\Controllers\Frontend\DashboardController::class, 'settings'])->name('settings');
    Route::patch('/password', [\App\Http\Controllers\Frontend\DashboardController::class, 'updatePassword'])->name('password.update');
    Route::get('/analytics', [\App\Http\Controllers\Frontend\DashboardController::class, 'analytics'])->name('analytics');
});

// API routes for search functionality
Route::prefix('api/products')->name('api.products.')->group(function () {
    Route::get('/suggestions', [ProductController::class, 'suggestions'])->name('suggestions');
    Route::get('/filter-options', [ProductController::class, 'filterOptions'])->name('filter-options');
});

// Categories
Route::prefix('categories')->name('categories.')->group(function () {
    Route::get('/', [CategoryController::class, 'index'])->name('index');
    Route::get('/{slug}', [CategoryController::class, 'show'])->name('show');
});

// Blog routes
Route::prefix('blog')->name('blog.')->group(function () {
    Route::get('/', [\App\Http\Controllers\Frontend\BlogController::class, 'index'])->name('index');
    Route::get('/search', [\App\Http\Controllers\Frontend\BlogController::class, 'search'])->name('search');
    Route::get('/category/{slug}', [\App\Http\Controllers\Frontend\BlogController::class, 'category'])->name('category');
    Route::get('/tag/{tag}', [\App\Http\Controllers\Frontend\BlogController::class, 'tag'])->name('tag');
    Route::get('/{slug}', [\App\Http\Controllers\Frontend\BlogController::class, 'show'])->name('show');
    Route::post('/{slug}/comments', [\App\Http\Controllers\Frontend\BlogController::class, 'storeComment'])->name('comments.store');
});

// Static Pages routes
Route::get('/pages/{slug}', [\App\Http\Controllers\Frontend\PageController::class, 'show'])->name('pages.show');

// Specific static page routes for common pages
Route::get('/help-center', [\App\Http\Controllers\Frontend\PageController::class, 'show'])->defaults('slug', 'help-center')->name('help-center');
Route::get('/terms-of-service', [\App\Http\Controllers\Frontend\PageController::class, 'show'])->defaults('slug', 'terms-of-service')->name('terms-of-service');
Route::get('/privacy-policy', [\App\Http\Controllers\Frontend\PageController::class, 'show'])->defaults('slug', 'privacy-policy')->name('privacy-policy');
Route::get('/refund-policy', [\App\Http\Controllers\Frontend\PageController::class, 'show'])->defaults('slug', 'refund-policy')->name('refund-policy');
Route::get('/contact', [\App\Http\Controllers\Frontend\PageController::class, 'contact'])->name('contact');
Route::post('/contact', [\App\Http\Controllers\Frontend\PageController::class, 'submitContact'])->name('contact.submit');





// Admin Routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'verified', 'role:admin'])->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

    // User management
    Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
    Route::get('users-export', [\App\Http\Controllers\Admin\UserController::class, 'export'])->name('users.export');
    Route::patch('users/{user}/toggle-status', [\App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('users.toggle-status');

    // Product management
    Route::resource('products', \App\Http\Controllers\Admin\ProductController::class);
    Route::patch('products/{product}/approve', [\App\Http\Controllers\Admin\ProductController::class, 'approve'])->name('products.approve');
    Route::patch('products/{product}/reject', [\App\Http\Controllers\Admin\ProductController::class, 'reject'])->name('products.reject');
    Route::patch('products/{product}/toggle-featured', [\App\Http\Controllers\Admin\ProductController::class, 'toggleFeatured'])->name('products.toggle-featured');
    Route::delete('product-images/{image}', [\App\Http\Controllers\Admin\ProductController::class, 'deleteImage'])->name('products.delete-image');
    Route::patch('products/{product}/update-image-order', [\App\Http\Controllers\Admin\ProductController::class, 'updateImageOrder'])->name('products.update-image-order');

    // Category management
    Route::resource('categories', \App\Http\Controllers\Admin\CategoryController::class);
    Route::patch('categories/{category}/toggle-status', [\App\Http\Controllers\Admin\CategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
    Route::post('categories/update-order', [\App\Http\Controllers\Admin\CategoryController::class, 'updateOrder'])->name('categories.update-order');

    // Order management
    Route::prefix('orders')->name('orders.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\OrderController::class, 'index'])->name('index');
        Route::get('/{order}', [\App\Http\Controllers\Admin\OrderController::class, 'show'])->name('show');
        Route::patch('/{order}/status', [\App\Http\Controllers\Admin\OrderController::class, 'updateStatus'])->name('update-status');
        Route::post('/{order}/confirm-bank-transfer', [\App\Http\Controllers\Admin\OrderController::class, 'confirmBankTransfer'])->name('confirm-bank-transfer');
        Route::get('/export/csv', [\App\Http\Controllers\Admin\OrderController::class, 'export'])->name('export');
        Route::get('/analytics/data', [\App\Http\Controllers\Admin\OrderController::class, 'analytics'])->name('analytics');
    });





    // Pages management
    Route::resource('pages', \App\Http\Controllers\Admin\PageController::class);

    // Blog management
    Route::prefix('blog')->name('blog.')->group(function () {
        // Blog Posts
        Route::resource('posts', \App\Http\Controllers\Admin\BlogPostController::class);
        Route::post('posts/{post}/toggle-featured', [\App\Http\Controllers\Admin\BlogPostController::class, 'toggleFeatured'])->name('posts.toggle-featured');
        Route::post('posts/bulk-action', [\App\Http\Controllers\Admin\BlogPostController::class, 'bulkAction'])->name('posts.bulk-action');

        // Blog Categories
        Route::resource('categories', \App\Http\Controllers\Admin\BlogCategoryController::class);
        Route::post('categories/{category}/toggle-active', [\App\Http\Controllers\Admin\BlogCategoryController::class, 'toggleActive'])->name('categories.toggle-active');
        Route::post('categories/bulk-action', [\App\Http\Controllers\Admin\BlogCategoryController::class, 'bulkAction'])->name('categories.bulk-action');

        // Blog Comments
        Route::get('comments', [\App\Http\Controllers\Admin\BlogCommentController::class, 'index'])->name('comments.index');
        Route::get('comments/{comment}', [\App\Http\Controllers\Admin\BlogCommentController::class, 'show'])->name('comments.show');
        Route::post('comments/{comment}/approve', [\App\Http\Controllers\Admin\BlogCommentController::class, 'approve'])->name('comments.approve');
        Route::post('comments/{comment}/reject', [\App\Http\Controllers\Admin\BlogCommentController::class, 'reject'])->name('comments.reject');
        Route::post('comments/{comment}/spam', [\App\Http\Controllers\Admin\BlogCommentController::class, 'spam'])->name('comments.spam');
        Route::post('comments/{comment}/toggle-pin', [\App\Http\Controllers\Admin\BlogCommentController::class, 'togglePin'])->name('comments.toggle-pin');
        Route::delete('comments/{comment}', [\App\Http\Controllers\Admin\BlogCommentController::class, 'destroy'])->name('comments.destroy');
        Route::post('comments/bulk-action', [\App\Http\Controllers\Admin\BlogCommentController::class, 'bulkAction'])->name('comments.bulk-action');
    });

    // Review management
    Route::resource('reviews', \App\Http\Controllers\Admin\ReviewController::class)->only(['index', 'show', 'destroy']);
    Route::patch('reviews/{review}/approve', [\App\Http\Controllers\Admin\ReviewController::class, 'approve'])->name('reviews.approve');
    Route::patch('reviews/{review}/reject', [\App\Http\Controllers\Admin\ReviewController::class, 'reject'])->name('reviews.reject');
    Route::post('reviews/bulk-action', [\App\Http\Controllers\Admin\ReviewController::class, 'bulkAction'])->name('reviews.bulk-action');

    // Contact management
    Route::resource('contact', \App\Http\Controllers\Admin\ContactController::class)->only(['index', 'show', 'destroy']);
    Route::patch('contact/{contact}/update-status', [\App\Http\Controllers\Admin\ContactController::class, 'updateStatus'])->name('contact.update-status');
    Route::post('contact/{contact}/send-reply', [\App\Http\Controllers\Admin\ContactController::class, 'sendReply'])->name('contact.send-reply');
    Route::post('contact/bulk-action', [\App\Http\Controllers\Admin\ContactController::class, 'bulkAction'])->name('contact.bulk-action');

    // Newsletter management
    Route::prefix('newsletter')->name('newsletter.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\NewsletterController::class, 'index'])->name('index');
        Route::delete('/{newsletter}', [\App\Http\Controllers\Admin\NewsletterController::class, 'destroy'])->name('destroy');
        Route::get('/export', [\App\Http\Controllers\Admin\NewsletterController::class, 'export'])->name('export');
        Route::patch('/{newsletter}/toggle-status', [\App\Http\Controllers\Admin\NewsletterController::class, 'toggleStatus'])->name('toggle-status');
    });

    // Settings management
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/general', [\App\Http\Controllers\Admin\SettingsController::class, 'general'])->name('general');
        Route::post('/general', [\App\Http\Controllers\Admin\SettingsController::class, 'updateGeneral'])->name('general.update');

        Route::get('/email', [\App\Http\Controllers\Admin\SettingsController::class, 'email'])->name('email');
        Route::post('/email', [\App\Http\Controllers\Admin\SettingsController::class, 'updateEmail'])->name('email.update');
        Route::post('/email/test', [\App\Http\Controllers\Admin\SettingsController::class, 'testEmail'])->name('email.test');
        Route::post('/email/test-smtp', [\App\Http\Controllers\Admin\SettingsController::class, 'testSmtp'])->name('email.test-smtp');
        Route::post('/email/test-imap', [\App\Http\Controllers\Admin\SettingsController::class, 'testImap'])->name('email.test-imap');

        Route::get('/payment', [\App\Http\Controllers\Admin\SettingsController::class, 'payment'])->name('payment');
        Route::post('/payment', [\App\Http\Controllers\Admin\SettingsController::class, 'updatePayment'])->name('payment.update');
        Route::post('/payment/test-nowpayments', [\App\Http\Controllers\Admin\SettingsController::class, 'testNowPayments'])->name('payment.test-nowpayments');

        Route::get('/seo', [\App\Http\Controllers\Admin\SettingsController::class, 'seo'])->name('seo');
        Route::post('/seo', [\App\Http\Controllers\Admin\SettingsController::class, 'updateSeo'])->name('seo.update');

        // PayPal OAuth routes
        Route::prefix('paypal')->name('paypal.')->group(function () {
            Route::get('/connect', [\App\Http\Controllers\Admin\PayPalOAuthController::class, 'redirect'])->name('connect');
            Route::get('/callback', [\App\Http\Controllers\Admin\PayPalOAuthController::class, 'callback'])->name('callback');
            Route::post('/disconnect', [\App\Http\Controllers\Admin\PayPalOAuthController::class, 'disconnect'])->name('disconnect');
            Route::post('/test', [\App\Http\Controllers\Admin\PayPalOAuthController::class, 'testConnection'])->name('test');
        });
    });
});


Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
