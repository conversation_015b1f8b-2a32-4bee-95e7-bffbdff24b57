<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AdminBackendTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);
    }

    public function test_admin_can_access_dashboard(): void
    {
        $response = $this->actingAs($this->admin)->get('/admin/dashboard');
        $response->assertStatus(200);
        $response->assertSee('Admin Dashboard');
    }

    public function test_admin_can_view_users_index(): void
    {
        $response = $this->actingAs($this->admin)->get('/admin/users');
        $response->assertStatus(200);
        $response->assertSee('User Management');
    }

    public function test_admin_can_create_user(): void
    {
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'member',
            'is_active' => true,
        ];

        $response = $this->actingAs($this->admin)->post('/admin/users', $userData);

        $response->assertRedirect('/admin/users');
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'member',
        ]);
    }

    public function test_admin_can_update_user(): void
    {
        $user = User::factory()->create(['role' => 'member']);

        $updateData = [
            'name' => 'Updated Name',
            'email' => $user->email,
            'role' => 'member',
            'is_active' => false,
        ];

        $response = $this->actingAs($this->admin)->patch("/admin/users/{$user->id}", $updateData);

        $response->assertRedirect('/admin/users');
        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'name' => 'Updated Name',
            'role' => 'member',
            'is_active' => false,
        ]);
    }

    public function test_admin_can_toggle_user_status(): void
    {
        $user = User::factory()->create(['is_active' => true]);

        $response = $this->actingAs($this->admin)->patch("/admin/users/{$user->id}/toggle-status");

        $response->assertRedirect();
        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'is_active' => false,
        ]);
    }

    public function test_admin_can_delete_user(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($this->admin)->delete("/admin/users/{$user->id}");

        $response->assertRedirect('/admin/users');
        $this->assertDatabaseMissing('users', ['id' => $user->id]);
    }

    public function test_admin_cannot_delete_themselves(): void
    {
        $response = $this->actingAs($this->admin)->delete("/admin/users/{$this->admin->id}");

        $response->assertRedirect('/admin/users');
        $this->assertDatabaseHas('users', ['id' => $this->admin->id]);
    }

    public function test_admin_can_view_categories_index(): void
    {
        $response = $this->actingAs($this->admin)->get('/admin/categories');
        $response->assertStatus(200);
        $response->assertSee('Category Management');
    }

    public function test_admin_can_create_category(): void
    {
        $categoryData = [
            'name' => 'Test Category',
            'description' => 'Test description',
            'icon' => 'fas fa-test',
            'color' => '#FF0000',
            'sort_order' => 1,
            'is_active' => true,
        ];

        $response = $this->actingAs($this->admin)->post('/admin/categories', $categoryData);

        $response->assertRedirect('/admin/categories');
        $this->assertDatabaseHas('categories', [
            'name' => 'Test Category',
            'slug' => 'test-category',
        ]);
    }

    public function test_admin_can_view_products_index(): void
    {
        $response = $this->actingAs($this->admin)->get('/admin/products');
        $response->assertStatus(200);
        $response->assertSee('Product Management');
    }

    public function test_admin_can_approve_product(): void
    {
        $admin_creator = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);
        $category = Category::factory()->create();
        $product = Product::factory()->create([
            'user_id' => $admin_creator->id,
            'category_id' => $category->id,
            'status' => 'draft',
        ]);

        $response = $this->actingAs($this->admin)->patch(route('admin.products.approve', $product));

        $response->assertRedirect();
        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'status' => 'published',
        ]);
    }

    public function test_admin_can_toggle_product_featured_status(): void
    {
        $admin_creator = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);
        $category = Category::factory()->create();
        $product = Product::factory()->create([
            'user_id' => $admin_creator->id,
            'category_id' => $category->id,
            'is_featured' => false,
        ]);

        $response = $this->actingAs($this->admin)->patch(route('admin.products.toggle-featured', $product));

        $response->assertRedirect();
        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'is_featured' => true,
        ]);
    }

    public function test_non_admin_cannot_access_admin_routes(): void
    {
        $member = User::factory()->create([
            'role' => 'member',
            'is_active' => true,
        ]);

        $response = $this->actingAs($member)->get('/admin/dashboard');
        $response->assertStatus(403);

        $response = $this->actingAs($member)->get('/admin/users');
        $response->assertStatus(403);

        $response = $this->actingAs($member)->get('/admin/categories');
        $response->assertStatus(403);
    }
}
