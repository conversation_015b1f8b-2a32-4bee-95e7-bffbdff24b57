<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_register_as_member(): void
    {
        $response = $this->post('/register', [
            'name' => 'Test Member',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'member',
            'bio' => 'Test bio',
            'website' => 'https://example.com',
        ]);

        $response->assertRedirect('/dashboard');
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'member',
        ]);
    }



    public function test_admin_can_access_admin_dashboard(): void
    {
        $admin = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);

        $response = $this->actingAs($admin)->get('/admin/dashboard');
        $response->assertStatus(200);
    }



    public function test_member_can_access_member_dashboard(): void
    {
        $member = User::factory()->create([
            'role' => 'member',
            'is_active' => true,
        ]);

        $response = $this->actingAs($member)->get('/dashboard');
        $response->assertStatus(200);
    }

    public function test_member_cannot_access_admin_dashboard(): void
    {
        $member = User::factory()->create([
            'role' => 'member',
            'is_active' => true,
        ]);

        $response = $this->actingAs($member)->get('/admin/dashboard');
        $response->assertStatus(403);
    }



    public function test_inactive_user_cannot_access_dashboard(): void
    {
        $user = User::factory()->create([
            'role' => 'member',
            'is_active' => false,
        ]);

        $response = $this->actingAs($user)->get('/dashboard');
        $response->assertRedirect('/login');
    }

    public function test_login_redirects_based_on_role(): void
    {
        // Test admin redirect
        $admin = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
            'password' => Hash::make('password123'),
        ]);

        $response = $this->post('/login', [
            'email' => $admin->email,
            'password' => 'password123',
        ]);

        $response->assertRedirect('/admin/dashboard');



        // Test member redirect
        $member = User::factory()->create([
            'role' => 'member',
            'is_active' => true,
            'password' => Hash::make('password123'),
        ]);

        $this->post('/logout');

        $response = $this->post('/login', [
            'email' => $member->email,
            'password' => 'password123',
        ]);

        $response->assertRedirect('/dashboard');
    }
}
